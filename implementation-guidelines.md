## Implementation Guidelines

### General Principles

1. Understand the existing codebase before making changes  
2. Follow the defined implementation plan strictly  
3. Preserve all existing functionality unless explicitly instructed otherwise  
4. Write efficient, modern, and well-structured code  

### Code Quality & Structure

5. Ensure code is clean, concise, and well-documented  
6. Replace legacy patterns with modern ones when appropriate  
7. Avoid deprecated or outdated functions, patterns, and files  
8. Remove unused, commented-out, or redundant code  
9. Work only within the current file/folder structure  
10. Do not install unnecessary packages or dependencies
11. When resolving error alawys use a systemic approach. 
12. While implementing a new feature, functionality or fix errors use a systematic approach.

### Naming & Style

13. Use consistent, descriptive, and readable naming conventions  