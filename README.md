# PayOp WooCommerce Payment Gateway

A comprehensive WooCommerce payment gateway plugin for PayOp, supporting 122+ payment methods with Direct Integration approach.

## 🚀 Features

- **122+ Payment Methods**: Support for cards, bank transfers, e-wallets, cash payments, and cryptocurrency
- **Direct Integration**: Complete bypass of PayOp hosted checkout for seamless user experience
- **Dynamic Field Management**: Intelligent handling of varying field requirements across payment methods
- **WooCommerce Blocks Support**: Full compatibility with modern block-based checkout
- **HPOS Compatible**: Supports High-Performance Order Storage
- **Security First**: Comprehensive security measures including encryption, IP whitelisting, and signature validation
- **Multi-Currency Support**: EUR, USD, GBP, CAD, AUD, BRL, DKK, PHP
- **Real-time Status Updates**: IPN/webhook handling for instant payment confirmations
- **Admin Dashboard**: Comprehensive management interface with statistics and logs

## 📋 Requirements

- WordPress 6.0 or higher
- WooCommerce 7.0 or higher
- PHP 8.0 or higher (8.2+ recommended)
- SSL certificate (required for production)
- PayOp merchant account

## 🔧 Installation

### Automatic Installation

1. Download the plugin zip file
2. Go to WordPress Admin → Plugins → Add New
3. Click "Upload Plugin" and select the zip file
4. Click "Install Now" and then "Activate"

### Manual Installation

1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through the WordPress admin
3. Go to WooCommerce → Settings → Payments
4. Configure the PayOp gateway

## ⚙️ Configuration

### 1. Get PayOp Credentials

1. Sign up for a PayOp merchant account
2. Obtain your API credentials:
   - Public Key (e.g., `application-606`)
   - Secret Key
   - JWT Token
   - Project ID (e.g., `606`)

### 2. Configure the Plugin

1. Navigate to **WooCommerce → Settings → Payments**
2. Click on **PayOp Payment Gateway**
3. Enter your API credentials:
   ```
   Public Key: application-606
   Secret Key: [your-secret-key]
   JWT Token: [your-jwt-token]
   Project ID: 606
   ```
4. Enable debug logging for testing
5. Save settings and test the connection

### 3. Test Configuration

1. Use the "Test Connection" button in settings
2. Verify payment methods load correctly
3. Test a small transaction in sandbox mode

## 🎯 Usage

### Customer Experience

1. **Checkout**: Customers select PayOp as payment method
2. **Method Selection**: Choose from 122+ available payment methods
3. **Dynamic Fields**: Complete additional fields based on selected method
4. **Payment**: Redirect to secure payment processing
5. **Confirmation**: Return to store with payment confirmation

### Payment Method Categories

- **Credit/Debit Cards**: International card processing
- **Bank Transfers**: SEPA, FPS, and regional bank transfers
- **E-Wallets**: Popular digital wallet solutions
- **Cash Payments**: Cash-based payment options
- **Cryptocurrency**: Digital currency support

### Regional Support

The plugin intelligently handles regional requirements:

- **European Union**: SEPA transfers with IBAN validation
- **Latin America**: Document validation for Colombia, Brazil, Uruguay
- **Global**: Multi-currency and country-specific payment methods

## 🔒 Security Features

### Data Protection
- AES-256-CBC encryption for sensitive credentials
- Secure signature generation using SHA-256
- Input sanitization and validation
- CSRF protection

### API Security
- JWT Bearer token authentication
- Request signing with secret key
- IP whitelisting for webhooks
- Rate limiting protection

### Compliance
- PCI DSS compliant payment processing
- GDPR compliant data handling
- SOC 2 Type II certified infrastructure

## 🛠️ Advanced Configuration

### Webhook Configuration

PayOp will send payment notifications to:
```
https://yoursite.com/wc-api/payop_ipn
```

Ensure this URL is accessible and not blocked by security plugins.

### Caching Configuration

The plugin includes intelligent caching for payment methods:

```php
// Cache settings
'cache_enabled' => true,
'cache_ttl' => 3600, // 1 hour
'cache_max_size' => 1000 // Max cached methods
```

### Debug Configuration

Enable detailed logging for troubleshooting:

```php
// Debug settings
'debug_enabled' => true,
'log_api_requests' => true,
'log_ipn' => true,
'log_level' => 'info'
```

## 🔧 Developer Guide

### Hooks and Filters

#### Actions
```php
// Payment completed
do_action('payop_payment_completed', $order, $transaction_id, $ipn_data);

// Payment failed
do_action('payop_payment_failed', $order, $transaction_id, $ipn_data);

// Payment pending
do_action('payop_payment_pending', $order, $transaction_id, $ipn_data);
```

#### Filters
```php
// Modify payment methods
add_filter('payop_payment_methods', function($methods) {
    // Customize payment methods
    return $methods;
});

// Modify field validation
add_filter('payop_field_validation', function($result, $field_name, $value) {
    // Custom validation logic
    return $result;
}, 10, 3);
```

### API Integration

```php
// Get API client
$credentials = PayOp_Config::get_api_credentials();
$api_client = new PayOp_API_Client(
    $credentials['public_key'],
    $credentials['secret_key'],
    $credentials['jwt_token'],
    $credentials['project_id']
);

// Get payment methods
$methods = $api_client->get_payment_methods();
```

### Custom Field Validation

```php
// Add custom field validation
add_filter('payop_validate_custom_field', function($result, $field_name, $value, $country) {
    if ($field_name === 'custom_field') {
        // Custom validation logic
        if (!preg_match('/^[A-Z]{2}\d{6}$/', $value)) {
            return [
                'valid' => false,
                'message' => 'Invalid format for custom field'
            ];
        }
    }
    return $result;
}, 10, 4);
```

## 📊 Monitoring and Analytics

### Admin Dashboard

Access comprehensive analytics at **WooCommerce → PayOp Settings**:

- Payment method statistics
- Transaction logs
- Error monitoring
- Performance metrics

### Key Metrics

- Payment success rate
- Popular payment methods
- Geographic distribution
- Error rates and types

## 🐛 Troubleshooting

### Common Issues

#### Payment Methods Not Loading
1. Check API credentials
2. Verify network connectivity
3. Check debug logs
4. Test API connection

#### IPN Not Working
1. Verify webhook URL accessibility
2. Check IP whitelisting
3. Validate SSL certificate
4. Review IPN logs

#### Field Validation Errors
1. Check field requirements
2. Verify country-specific rules
3. Review validation patterns
4. Test with different data

### Debug Information

Enable debug logging and check logs at:
- **WooCommerce → Status → Logs**
- **PayOp Settings → Logs tab**

### Support Channels

1. **Plugin Support**: Check documentation and FAQ
2. **PayOp Support**: Contact PayOp for API-related issues
3. **WooCommerce Support**: For WooCommerce compatibility issues

## 🔄 Updates and Maintenance

### Automatic Updates
The plugin supports WordPress automatic updates when distributed through the repository.

### Manual Updates
1. Backup your site
2. Download the latest version
3. Replace plugin files
4. Test functionality

### Database Migrations
The plugin handles database schema updates automatically during upgrades.

## 📝 Changelog

### Version 1.0.0
- Initial release
- Support for 122+ payment methods
- Direct Integration implementation
- WooCommerce Blocks compatibility
- Comprehensive security features
- Admin dashboard and analytics

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Setup
1. Clone the repository
2. Install dependencies: `composer install`
3. Set up test environment
4. Run tests: `./tests/run-tests.sh`

## 📄 License

This plugin is licensed under the GPL v2 or later.

## 🙏 Acknowledgments

- PayOp for providing comprehensive API documentation
- WooCommerce team for excellent payment gateway framework
- WordPress community for continuous support

---

**Need Help?** Check our [FAQ](FAQ.md) or [contact support](mailto:<EMAIL>).
