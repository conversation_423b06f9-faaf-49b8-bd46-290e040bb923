<?php
/**
 * PayOp Plugin Debug <PERSON>ript
 * 
 * Run this script to debug plugin loading issues
 */

// Check if we're in WordPress environment
if (!defined('ABSPATH')) {
    echo "This script must be run from within WordPress.\n";
    echo "Add this to your wp-config.php temporarily:\n";
    echo "require_once(ABSPATH . 'wp-content/plugins/payop-direct-payment-woo/debug-plugin.php');\n";
    exit;
}

echo "<h2>PayOp Plugin Debug Information</h2>\n";

// Check if plugin file exists
$plugin_file = WP_PLUGIN_DIR . '/payop-direct-payment-woo/payop-woocommerce-gateway.php';
echo "<h3>1. Plugin File Check</h3>\n";
echo "Plugin file path: " . $plugin_file . "<br>\n";
echo "File exists: " . (file_exists($plugin_file) ? 'YES' : 'NO') . "<br>\n";

if (file_exists($plugin_file)) {
    echo "File size: " . filesize($plugin_file) . " bytes<br>\n";
    echo "File permissions: " . substr(sprintf('%o', fileperms($plugin_file)), -4) . "<br>\n";
}

// Check if plugin is active
echo "<h3>2. Plugin Status</h3>\n";
$active_plugins = get_option('active_plugins', []);
$plugin_basename = 'payop-direct-payment-woo/payop-woocommerce-gateway.php';
echo "Plugin basename: " . $plugin_basename . "<br>\n";
echo "Is active: " . (in_array($plugin_basename, $active_plugins) ? 'YES' : 'NO') . "<br>\n";

// Check WooCommerce
echo "<h3>3. WooCommerce Check</h3>\n";
echo "WooCommerce active: " . (class_exists('WooCommerce') ? 'YES' : 'NO') . "<br>\n";
if (class_exists('WooCommerce')) {
    echo "WooCommerce version: " . WC()->version . "<br>\n";
}

// Check PHP version
echo "<h3>4. PHP Environment</h3>\n";
echo "PHP version: " . PHP_VERSION . "<br>\n";
echo "PHP 8.0+ compatible: " . (version_compare(PHP_VERSION, '8.0', '>=') ? 'YES' : 'NO') . "<br>\n";

// Check if PayOp classes are loaded
echo "<h3>5. PayOp Classes</h3>\n";
$payop_classes = [
    'PayOp_WooCommerce_Gateway',
    'PayOp\\WooCommerce\\Gateway\\PayOp_Gateway',
    'PayOp\\WooCommerce\\Utils\\PayOp_Config',
    'PayOp\\WooCommerce\\API\\PayOp_API_Client'
];

foreach ($payop_classes as $class) {
    echo "Class '{$class}': " . (class_exists($class) ? 'LOADED' : 'NOT LOADED') . "<br>\n";
}

// Check WooCommerce payment gateways
echo "<h3>6. WooCommerce Payment Gateways</h3>\n";
if (function_exists('WC')) {
    $gateways = WC()->payment_gateways()->payment_gateways();
    echo "Total gateways: " . count($gateways) . "<br>\n";
    
    $payop_found = false;
    foreach ($gateways as $gateway_id => $gateway) {
        if (strpos($gateway_id, 'payop') !== false || strpos(get_class($gateway), 'PayOp') !== false) {
            echo "PayOp gateway found: {$gateway_id} (" . get_class($gateway) . ")<br>\n";
            $payop_found = true;
        }
    }
    
    if (!$payop_found) {
        echo "<strong>PayOp gateway NOT found in registered gateways</strong><br>\n";
        echo "Registered gateway IDs: " . implode(', ', array_keys($gateways)) . "<br>\n";
    }
} else {
    echo "WooCommerce not available<br>\n";
}

// Check plugin constants
echo "<h3>7. Plugin Constants</h3>\n";
$constants = [
    'PAYOP_WC_VERSION',
    'PAYOP_WC_PLUGIN_FILE',
    'PAYOP_WC_PLUGIN_DIR',
    'PAYOP_WC_PLUGIN_URL'
];

foreach ($constants as $constant) {
    if (defined($constant)) {
        echo "{$constant}: " . constant($constant) . "<br>\n";
    } else {
        echo "{$constant}: NOT DEFINED<br>\n";
    }
}

// Check for errors
echo "<h3>8. Error Check</h3>\n";
$error_log = ini_get('error_log');
echo "Error log location: " . ($error_log ?: 'Default system log') . "<br>\n";

// Try to manually load the plugin
echo "<h3>9. Manual Plugin Load Test</h3>\n";
if (file_exists($plugin_file)) {
    try {
        // Capture any output/errors
        ob_start();
        $error_before = error_get_last();
        
        include_once $plugin_file;
        
        $output = ob_get_clean();
        $error_after = error_get_last();
        
        if ($output) {
            echo "Plugin output: <pre>" . htmlspecialchars($output) . "</pre><br>\n";
        }
        
        if ($error_after && $error_after !== $error_before) {
            echo "Plugin error: " . htmlspecialchars($error_after['message']) . "<br>\n";
        } else {
            echo "Plugin loaded without errors<br>\n";
        }
        
        // Check if main class exists after loading
        echo "Main class exists after load: " . (class_exists('PayOp_WooCommerce_Gateway') ? 'YES' : 'NO') . "<br>\n";
        
    } catch (Exception $e) {
        echo "Exception loading plugin: " . htmlspecialchars($e->getMessage()) . "<br>\n";
    }
}

// Check hooks
echo "<h3>10. WordPress Hooks</h3>\n";
global $wp_filter;

$payop_hooks = [];
foreach ($wp_filter as $hook_name => $hook) {
    foreach ($hook->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (is_array($callback['function']) && is_object($callback['function'][0])) {
                $class_name = get_class($callback['function'][0]);
                if (strpos($class_name, 'PayOp') !== false) {
                    $payop_hooks[] = "{$hook_name} -> {$class_name}::{$callback['function'][1]}";
                }
            }
        }
    }
}

if (!empty($payop_hooks)) {
    echo "PayOp hooks found:<br>\n";
    foreach ($payop_hooks as $hook) {
        echo "- " . htmlspecialchars($hook) . "<br>\n";
    }
} else {
    echo "No PayOp hooks found<br>\n";
}

echo "<h3>Debug Complete</h3>\n";
echo "If the PayOp gateway is not showing up, check the issues highlighted above.<br>\n";
