<?php

// PayOp Signature Generator
// Usage: php generate_signature.php <amount> <currency> <order_id> <secret_key>

if ($argc < 5) {
    echo "Usage: php generate_signature.php <amount> <currency> <order_id> <secret_key>\n";
    echo "Example: php generate_signature.php 100.00 USD order12345 fd6d7b9d6e14146ba064cd3b7afd7a0e\n";
    exit(1);
}

$amount = $argv[1];
$currency = strtoupper($argv[2]);
$id = $argv[3];
$secretKey = $argv[4];

$data = [$amount, $currency, $id, $secretKey];
$signature = hash('sha256', implode(':', $data));

echo "Signature: " . $signature . "\n";
echo "Data used: " . implode(':', $data) . "\n";
?>
