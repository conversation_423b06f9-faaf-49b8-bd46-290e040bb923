{"data": [{"identifier": 757, "type": "bank_transfer", "formType": "standard", "title": "Santander Brasil", "logo": "https://account.payop.com/assets/images/methods/santander.jpg", "currencies": ["EUR"], "countries": ["BR"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "cpf_cnpj", "type": "text", "title": "CPF/CNPJ", "regexp": "^\\d{11,14}$", "required": true}]}}, {"identifier": 1135, "type": "cash", "formType": "standard", "title": "Red Activa (Almacenes Tía)", "logo": "https://account.payop.com/assets/images/methods/almacenestia.jpg", "currencies": ["EUR"], "countries": ["EC"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 200031, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in Austria", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["EUR"], "countries": ["AT"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true}]}}, {"identifier": 700001, "type": "cards_international", "formType": "standard", "title": "Cards via PayDo", "logo": "https://account.payop.com/assets/images/methods/payDoWallet.jpg", "currencies": ["EUR", "AUD", "CAD", "GBP", "USD", "DKK"], "countries": ["AD", "AE", "AG", "AM", "AN", "AQ", "AR", "AT", "AU", "AW", "AZ", "BA", "BE", "BG", "BH", "BM", "BN", "BR", "BS", "BV", "BZ", "CA", "CC", "CG", "CH", "CK", "CL", "CN", "CO", "CR", "CW", "CX", "CY", "CZ", "DE", "DK", "DM", "DO", "DZ", "EC", "EE", "EG", "ES", "ET", "FI", "FJ", "FK", "FO", "FR", "FX", "GB", "GD", "GE", "GF", "GG", "GI", "GL", "GP", "GR", "GS", "GT", "HK", "HM", "HN", "HR", "HU", "ID", "IE", "IL", "IM", "IN", "IO", "IS", "IT", "JE", "JP", "KE", "KG", "KN", "KR", "KW", "KY", "KZ", "LC", "LI", "LS", "LT", "LU", "LV", "MC", "MD", "ME", "MF", "MG", "MH", "MK", "MQ", "MS", "MV", "MX", "MY", "NC", "NF", "NG", "NL", "NO", "NP", "NR", "NT", "NU", "NZ", "OM", "PE", "PF", "PL", "PM", "PN", "PR", "PT", "PY", "QA", "RO", "RW", "SA", "SB", "SC", "SE", "SG", "SH", "SI", "SJ", "SK", "SM", "SR", "SU", "SV", "SX", "TC", "TF", "TH", "TJ", "TK", "TP", "TW", "UA", "UK", "UY", "UZ", "VA", "VC", "VG", "VN", "VU", "WF", "YT", "ZA"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 1251, "type": "cash", "formType": "standard", "title": "Globokas - Kasnet", "logo": "https://account.payop.com/assets/images/methods/gkn.jpg", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 6110, "type": "crypto", "formType": "standard", "title": "Crypto Payment", "logo": "https://account.payop.com/assets/images/payment_methods/crypto_payment.png", "currencies": ["EUR"], "countries": ["AD", "AE", "AG", "AI", "AL", "AM", "AQ", "AR", "AS", "AT", "AU", "AW", "AX", "BB", "BD", "BE", "BF", "BG", "BH", "BJ", "BL", "BM", "BN", "BO", "BQ", "BR", "BS", "BT", "BV", "BW", "BZ", "CA", "CC", "CF", "CH", "CI", "CK", "CL", "CM", "CN", "CO", "CR", "CV", "CW", "CX", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC", "EE", "EG", "ES", "ET", "FI", "FJ", "FK", "FM", "FO", "FR", "GA", "GB", "GD", "GE", "GF", "GG", "GH", "GI", "GL", "GM", "GP", "GQ", "GR", "GS", "GT", "GU", "GY", "HK", "HM", "HN", "HR", "HU", "ID", "IE", "IL", "IM", "IN", "IO", "IS", "IT", "JE", "JM", "JO", "JP", "KE", "KG", "KH", "KI", "KM", "KN", "KR", "KW", "KY", "KZ", "LA", "LC", "LI", "LK", "LR", "LS", "LT", "LU", "LV", "MA", "MC", "MD", "ME", "MF", "MG", "MH", "MK", "MN", "MO", "MP", "MQ", "MR", "MS", "MT", "MU", "MV", "MW", "MX", "MY", "MZ", "NA", "NC", "NE", "NF", "NG", "NL", "NO", "NP", "NR", "NU", "NZ", "OM", "PA", "PE", "PF", "PG", "PH", "PK", "PL", "PM", "PN", "PR", "PT", "PW", "PY", "QA", "RE", "RO", "RS", "RW", "SA", "SB", "SC", "SE", "SG", "SH", "SI", "SJ", "SK", "SL", "SM", "SN", "SR", "ST", "SV", "SX", "SZ", "TC", "TD", "TF", "TG", "TH", "TJ", "TK", "TL", "TM", "TN", "TO", "TR", "TT", "TV", "TZ", "UG", "UM", "UY", "UZ", "VA", "VC", "VG", "VI", "VN", "VU", "WF", "WS", "YT", "ZA", "ZM"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 846, "type": "ewallet", "formType": "standard", "title": "PaydoEWallet", "logo": "https://account.payop.com/assets/images/methods/paydoEWallet.jpg", "currencies": ["USD", "EUR", "GBP"], "countries": ["AD", "AE", "AG", "AM", "AN", "AQ", "AR", "AT", "AU", "AW", "AZ", "BA", "BE", "BG", "BH", "BM", "BN", "BR", "BS", "BT", "BV", "CA", "CC", "CG", "CH", "CK", "CL", "CN", "CO", "CR", "CW", "CX", "CY", "CZ", "DE", "DK", "DM", "DO", "DZ", "EC", "EE", "EG", "ES", "ET", "FI", "FJ", "FK", "FO", "FX", "GB", "GD", "GE", "GF", "GG", "GI", "GL", "GP", "GR", "GS", "GT", "HK", "HM", "HN", "HR", "HU", "ID", "IE", "IL", "IM", "IN", "IO", "IS", "IT", "JE", "JP", "KE", "KG", "KN", "KR", "KW", "KY", "KZ", "LC", "LI", "LS", "LT", "LU", "LV", "MC", "MD", "ME", "MF", "MG", "MH", "MK", "MQ", "MS", "MV", "MX", "MY", "NC", "NF", "NG", "NL", "NO", "NP", "NR", "NT", "NU", "NZ", "OM", "PE", "PF", "PL", "PM", "PN", "PR", "PT", "PY", "QA", "RO", "RW", "SA", "SB", "SC", "SE", "SG", "SH", "SI", "SJ", "SK", "SM", "SR", "SU", "SV", "SX", "TC", "TF", "TH", "TJ", "TK", "TP", "UK", "UY", "UZ", "VA", "VC", "VG", "VN", "VU", "WF", "YT", "ZA", "UA", "PA", "PH", "SN"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 4521, "type": "bank_transfer", "formType": "standard", "title": "Landbank ATM Online", "logo": "https://account.payop.com/assets/images/methods/landbank.jpg", "currencies": ["PHP"], "countries": ["PH"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 1253, "type": "bank_transfer", "formType": "standard", "title": "Banco Amazonas", "logo": "https://account.payop.com/assets/images/methods/banco-amazonas.jpg", "currencies": ["EUR"], "countries": ["EC"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 1249, "type": "bank_transfer", "formType": "standard", "title": "Lulo Bank", "logo": "https://account.payop.com/assets/images/methods/lulobank.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 1167, "type": "cash", "formType": "standard", "title": "Gana", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/col/bank_4_8323_CASH_GANA.png", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 1123, "type": "ewallet", "formType": "standard", "title": "MachPay Chile", "logo": "https://account.payop.com/assets/images/methods/mach_pay_white.png", "currencies": ["EUR"], "countries": ["CL"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 1244, "type": "cash", "formType": "standard", "title": "Scotiabank Agente", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/per/bank_4_1006_CASH_AG_3.png", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 1171, "type": "cash", "formType": "standard", "title": "Paga Todo Para Todo", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/col/bank_4_8323_CASH_PAGATODO.png", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 200661, "type": "cash", "formType": "standard", "title": "Servipag via PayDo", "logo": "https://account.payop.com/assets/images/methods/SP.png", "currencies": ["USD"], "countries": ["CL"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 634, "type": "bank_transfer", "formType": "standard", "title": "BBVA - PSE", "logo": "https://account.payop.com/assets/images/methods/BBVA.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 627, "type": "bank_transfer", "formType": "standard", "title": "Banco Falabella - PSE", "logo": "https://account.payop.com/assets/images/methods/bancoFalabella.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 1168, "type": "cash", "formType": "standard", "title": "Gana Gana", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/col/bank_4_8323_CASH_GANAGANA.png", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 1169, "type": "cash", "formType": "standard", "title": "<PERSON><PERSON>", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/col/bank_4_8323_CASH_JER.png", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 1165, "type": "cash", "formType": "standard", "title": "Apuestas Cúcuta 75", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/col/bank_4_8323_CASH_APUESTACUCUTA.png", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 635, "type": "bank_transfer", "formType": "standard", "title": "CitiBank - PSE", "logo": "https://account.payop.com/assets/images/methods/city.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 1250, "type": "bank_transfer", "formType": "standard", "title": "Banco Finandina", "logo": "https://account.payop.com/assets/images/methods/banco-finandina.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 200687, "type": "cash", "formType": "standard", "title": "Abitab via PayDo", "logo": "https://account.payop.com/assets/images/methods/Abitab.png", "currencies": ["USD"], "countries": ["UY"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document (CI)", "regexp": "^\\d{6,8}$", "required": true}]}}, {"identifier": 1246, "type": "cash", "formType": "standard", "title": "Banco Ripley Agente", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/per/bank_4_8320_CASH_Ripley_AG_3.png", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 1248, "type": "cash", "formType": "standard", "title": "Acciones y Valores", "logo": "https://account.payop.com/assets/images/methods/acciones-valores.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 1254, "type": "bank_transfer", "formType": "standard", "title": "SPEI STP", "logo": "https://account.payop.com/assets/images/methods/spei.jpg", "currencies": ["EUR"], "countries": ["MX"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 1179, "type": "bank_transfer", "formType": "standard", "title": "Banco Nacional de Costa Rica", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/cri/bank_4_1021_ONLINE_B2C.png", "currencies": ["EUR"], "countries": ["CR"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 621, "type": "bank_transfer", "formType": "standard", "title": "Banco Caja Social - PSE", "logo": "https://account.payop.com/assets/images/methods/bancoCajaSocial.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 618, "type": "bank_transfer", "formType": "standard", "title": "Scotiabank (Mexico)", "logo": "https://account.payop.com/assets/images/methods/scotiabank.jpg", "currencies": ["EUR"], "countries": ["MX"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 629, "type": "bank_transfer", "formType": "standard", "title": "Banco Pichincha - PSE", "logo": "https://account.payop.com/assets/images/methods/bancoPichincha.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 606, "type": "bank_transfer", "formType": "standard", "title": "Caja Huancayo", "logo": "https://account.payop.com/assets/images/methods/cajaHuancayo.jpg", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 637, "type": "bank_transfer", "formType": "standard", "title": "Itau Corpbanca - PSE", "logo": "https://account.payop.com/assets/images/methods/itau.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 1172, "type": "cash", "formType": "standard", "title": "Suchance", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/col/bank_4_8323_CASH_SUCHANCE.png", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 705, "type": "cash", "formType": "standard", "title": "Efecty", "logo": "https://account.payop.com/assets/images/methods/efecty.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 633, "type": "bank_transfer", "formType": "standard", "title": "Bancoomeva - PSE", "logo": "https://account.payop.com/assets/images/methods/bancoloomeva.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 200682, "type": "cash", "formType": "standard", "title": " Payvalida via PayDo", "logo": "https://account.payop.com/assets/images/methods/payvalida_group.png", "currencies": ["USD"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 864, "type": "ewallet", "formType": "standard", "title": "Maya Bills Pay", "logo": "https://account.payop.com/assets/images/methods/payMaya.png", "currencies": ["PHP"], "countries": ["PH"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 626, "type": "bank_transfer", "formType": "standard", "title": "Banco de Occidente - PSE", "logo": "https://account.payop.com/assets/images/methods/bancoDeOccidente.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 602, "type": "cash", "formType": "standard", "title": "Caixa Econômica Federal (Lotéricas)", "logo": "https://account.payop.com/assets/images/methods/lotericas.jpg", "currencies": ["EUR"], "countries": ["BR"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "cpf_cnpj", "type": "text", "title": "CPF/CNPJ", "regexp": "^\\d{11,14}$", "required": true}]}}, {"identifier": 4510, "type": "bank_transfer", "formType": "standard", "title": "BPI Online", "logo": "https://account.payop.com/assets/images/methods/bpi.jpg", "currencies": ["PHP"], "countries": ["PH"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 1132, "type": "bank_transfer", "formType": "standard", "title": "Cotrafa", "logo": "https://account.payop.com/assets/images/methods/cooperstiva_financiera_cotrafa.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 200033, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in Ireland", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["EUR"], "countries": ["IE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true}]}}, {"identifier": 623, "type": "bank_transfer", "formType": "standard", "title": "Banco Cooperativo Coopcentral - PSE", "logo": "https://account.payop.com/assets/images/methods/banco_cooperativo.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 600, "type": "cash", "formType": "standard", "title": "Walmart (Líder)", "logo": "https://account.payop.com/assets/images/methods/lider.jpg", "currencies": ["EUR"], "countries": ["CL"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 690, "type": "cash", "formType": "standard", "title": "Walmart (aCuenta)", "logo": "https://account.payop.com/assets/images/methods/aCuenta.jpg", "currencies": ["EUR"], "countries": ["CL"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 200032, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in Poland", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["EUR"], "countries": ["PL"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true}]}}, {"identifier": 200672, "type": "bank_transfer", "formType": "standard", "title": "OVO via PayDo", "logo": "https://account.payop.com/assets/images/methods/ovo_via_paydo.jpg", "currencies": ["USD"], "countries": ["ID"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 1180, "type": "cash", "formType": "standard", "title": "Banco Guayaquil", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/ecu/bank_4_8216_CASH_B2C.png", "currencies": ["EUR"], "countries": ["EC"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 693, "type": "bank_transfer", "formType": "standard", "title": "Webpay via PayDo", "logo": "https://account.payop.com/assets/images/methods/WP.png", "currencies": ["USD"], "countries": ["CL"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 200956, "type": "bank_transfer", "formType": "standard", "title": "Interbank", "logo": "https://account.payop.com/assets/images/methods/interbank.jpg", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 200685, "type": "cash", "formType": "standard", "title": "Red Pagos via PayDo", "logo": "https://account.payop.com/assets/images/methods/redpagos.png", "currencies": ["USD"], "countries": ["UY"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 200999, "type": "cash", "formType": "standard", "title": "Infonet via PayDo", "logo": "https://account.payop.com/assets/images/methods/infonet_cobranzas_via_paydo.jpg", "currencies": ["USD"], "countries": ["PY"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 201001, "type": "ewallet", "formType": "standard", "title": "VPay QR via PayDo", "logo": "https://account.payop.com/assets/images/methods/vpay_via_paydo.png", "currencies": ["USD"], "countries": ["BO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 622, "type": "bank_transfer", "formType": "standard", "title": "Banco Scotiabank Colpatria - PSE", "logo": "https://account.payop.com/assets/images/methods/scotiabank-1.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 200018, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["EUR"], "countries": ["AT", "ES", "IT", "PT", "FR", "DE", "FI", "NL", "EE", "LT", "IE", "PL"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true}, {"name": "bank_country", "type": "bank_country", "regexp": "^(ES|IT|PT|FR|DE|FI|NL|EE|LT|AT|PL|IE)$", "required": true}]}}, {"identifier": 1247, "type": "cash", "formType": "standard", "title": "Western Union Agente", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/per/bank_4_8320_CASH_AG_3.png", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 692, "type": "cash", "formType": "standard", "title": "Oxxo via PayDo", "logo": "https://account.payop.com/assets/images/methods/OX.png", "currencies": ["USD"], "countries": ["MX"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 575, "type": "cash", "formType": "standard", "title": "Caja Tacna", "logo": "https://account.payop.com/assets/images/methods/cajaTacna.jpg", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": ********, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in UK", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["GBP"], "countries": ["GB"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(FPS)$", "required": true}, {"name": "bank_country", "type": "bank_country", "regexp": "^(GB)$", "required": true}]}}, {"identifier": 1178, "type": "cash", "formType": "standard", "title": "Banco Nacional de Costa Rica", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/cri/bank_4_1021_CASH.png", "currencies": ["EUR"], "countries": ["CR"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 200002, "type": "bank_transfer", "formType": "standard", "title": "EPS via PayDo", "logo": "https://account.payop.com/assets/images/methods/eps_via_paydo.png", "currencies": ["EUR"], "countries": ["AT"], "config": {"fields": [{"name": "email", "type": "email", "required": true}]}}, {"identifier": 200677, "type": "cash", "formType": "standard", "title": "Arcus via PayDo", "logo": "https://account.payop.com/assets/images/methods/arcus_via_paydo.jpg", "currencies": ["USD"], "countries": ["MX"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 200969, "type": "ewallet", "formType": "standard", "title": "PIX", "logo": "https://account.payop.com/assets/images/methods/PIX.jpg", "currencies": ["EUR"], "countries": ["BR"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "cpf_cnpj", "type": "text", "title": "CPF/CNPJ", "regexp": "^\\d{11,14}$", "required": true}]}}, {"identifier": 200671, "type": "bank_transfer", "formType": "standard", "title": "Virtual Accounts via PayDo", "logo": "https://account.payop.com/assets/images/methods/virtual_account_via_paydo.png", "currencies": ["USD"], "countries": ["ID"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 214969, "type": "ewallet", "formType": "standard", "title": "PIX via PayDo", "logo": "https://account.payop.com/assets/images/methods/pix_via_paydo.png", "currencies": ["USD"], "countries": ["BR"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 598, "type": "cash", "formType": "standard", "title": "CajaVecina", "logo": "https://account.payop.com/assets/images/methods/cajaVecina.jpg", "currencies": ["EUR"], "countries": ["CL"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 203891, "type": "bank_transfer", "formType": "standard", "title": "Pay by <PERSON><PERSON>", "logo": "https://account.payop.com/assets/images/methods/pay-by-monzo.png", "currencies": ["GBP"], "countries": ["GB"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}]}}, {"identifier": 203822, "type": "bank_transfer", "formType": "standard", "title": "Pay by Rev<PERSON>ut", "logo": "https://account.payop.com/assets/images/methods/pay-by-revolut.png", "currencies": ["EUR", "GBP"], "countries": ["ES", "IT", "PT", "FR", "DE", "FI", "AT", "LT", "EE", "CY", "HU", "LV", "BE", "BG", "HR", "CZ", "DK", "GR", "IE", "IS", "LU", "MT", "NL", "NO", "PL", "RO", "SK", "SE", "SI"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "iban_type", "type": "iban_type", "regexp": "^(GB|NOT_GB)$", "required": true}]}}, {"identifier": 2001017, "type": "cash", "formType": "standard", "title": "CashToCode", "logo": "https://account.payop.com/assets/images/methods/cashtoCode-logo-blue.png", "currencies": ["EUR", "USD"], "countries": ["UK", "AT", "IE", "DE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "firstname", "type": "text", "title": "First Name", "required": true}, {"name": "lastname", "type": "text", "title": "Last Name", "required": true}, {"name": "dateOfBirth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}]}}, {"identifier": 200024, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in Finland", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["EUR"], "countries": ["FI"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true}]}}, {"identifier": 200569, "type": "cash", "formType": "standard", "title": "Boleto via PayDo", "logo": "https://account.payop.com/assets/images/methods/Boleto.png", "currencies": ["BRL", "USD"], "countries": ["BR"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 203821, "type": "bank_transfer", "formType": "standard", "title": "Pay by Revolut (GBP)", "logo": "https://account.payop.com/assets/images/methods/pay-by-revolut.png", "currencies": ["GBP"], "countries": ["GB"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}]}}, {"identifier": 599, "type": "cash", "formType": "standard", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logo": "https://account.payop.com/assets/images/methods/serviestado.jpg", "currencies": ["EUR"], "countries": ["CL"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 582, "type": "cash", "formType": "standard", "title": "Banco Azteca", "logo": "https://account.payop.com/assets/images/methods/bancoAzteca.jpg", "currencies": ["EUR"], "countries": ["MX"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 612, "type": "bank_transfer", "formType": "standard", "title": "Banco Guayaquil", "logo": "https://account.payop.com/assets/images/methods/banco-guayaquil.jpg", "currencies": ["EUR"], "countries": ["EC"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 1126, "type": "bank_transfer", "formType": "standard", "title": "Ban100", "logo": "https://account.payop.com/assets/images/methods/ban100.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 571, "type": "cash", "formType": "standard", "title": "Caja Arequipa", "logo": "https://account.payop.com/assets/images/methods/cajaArequipa.jpg", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 573, "type": "cash", "formType": "standard", "title": "Caja Huancayo", "logo": "https://account.payop.com/assets/images/methods/cajaHuancayo.jpg", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 200017, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in Germany", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["EUR"], "countries": ["DE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true}]}}, {"identifier": 632, "type": "bank_transfer", "formType": "standard", "title": "Bancolombia - PSE", "logo": "https://account.payop.com/assets/images/methods/bancolombia.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 608, "type": "bank_transfer", "formType": "standard", "title": "Caja Trujillo", "logo": "https://account.payop.com/assets/images/methods/cajaTrujillo.jpg", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 200027, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in Netherlands", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["EUR"], "countries": ["NL"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true}]}}, {"identifier": 707, "type": "cash", "formType": "standard", "title": "PayNet", "logo": "https://account.payop.com/assets/images/methods/paynet-2.jpg", "currencies": ["EUR"], "countries": ["MX"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 4518, "type": "bank_transfer", "formType": "standard", "title": "ECPay (GCash/Payment Centers)", "logo": "https://account.payop.com/assets/images/methods/ECPay.jpg", "currencies": ["PHP"], "countries": ["PH"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 1130, "type": "bank_transfer", "formType": "standard", "title": "Coltefinanciera", "logo": "https://account.payop.com/assets/images/methods/coltefinanciera.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 4531, "type": "bank_transfer", "formType": "standard", "title": "Dragonpay Prepaid Credits", "logo": "https://account.payop.com/assets/images/methods/dragonpay.jpg", "currencies": ["PHP"], "countries": ["PH"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 1154, "type": "cash", "formType": "standard", "title": "PagoEfectivo", "logo": "https://account.payop.com/assets/images/methods/pagoEfectivo.jpg", "currencies": ["EUR"], "countries": ["EC"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 589, "type": "cash", "formType": "standard", "title": "Scotiabank (Mexico)", "logo": "https://account.payop.com/assets/images/methods/scotiabank.jpg", "currencies": ["EUR"], "countries": ["MX"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 595, "type": "cash", "formType": "standard", "title": "Movil Red", "logo": "https://account.payop.com/assets/images/methods/movilRed.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 1166, "type": "cash", "formType": "standard", "title": "Apuestas Unidas", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/col/bank_4_8323_CASH_APUESTASUNIDAS.png", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 1170, "type": "cash", "formType": "standard", "title": "La Perla", "logo": "https://safetypaycdn1.azureedge.net/ExpressCdnV41/Express3/Content/V4/images/col/bank_4_8323_CASH_LAPERLA.png", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 862, "type": "ewallet", "formType": "standard", "title": "GCash Bills Pay", "logo": "https://account.payop.com/assets/images/methods/gcash.png", "currencies": ["PHP"], "countries": ["PH"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 200998, "type": "bank_transfer", "formType": "standard", "title": "Bank transfer (South Africa) via PayDo", "logo": "https://account.payop.com/assets/images/methods/NB.png", "currencies": ["USD"], "countries": ["ZA"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 210013, "type": "ewallet", "formType": "standard", "title": "Interac® via PayDo", "logo": "https://account.payop.com/assets/images/methods/interac_via_paydo.png", "currencies": ["CAD"], "countries": ["CA"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 1239, "type": "cash", "formType": "standard", "title": "Santander Mexico WS", "logo": "https://account.payop.com/assets/images/methods/santander.jpg", "currencies": ["EUR"], "countries": ["MX"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 607, "type": "bank_transfer", "formType": "standard", "title": "Caja Tacna", "logo": "https://account.payop.com/assets/images/methods/cajaTacna.jpg", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 200920, "type": "ewallet", "formType": "standard", "title": "Fawry via PayDo", "logo": "https://account.payop.com/assets/images/methods/FW.png", "currencies": ["USD"], "countries": ["EG"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 625, "type": "bank_transfer", "formType": "standard", "title": "Banco de Bogota - PSE", "logo": "https://account.payop.com/assets/images/methods/bancoDeBogota.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 628, "type": "bank_transfer", "formType": "standard", "title": "Banco GNB Sudameris - PSE", "logo": "https://account.payop.com/assets/images/methods/banco_gnb_sudameris.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 630, "type": "bank_transfer", "formType": "standard", "title": "Banco Popular - PSE", "logo": "https://account.payop.com/assets/images/methods/bancoPopular.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 4519, "type": "ewallet", "formType": "standard", "title": "GrabPay", "logo": "https://account.payop.com/assets/images/methods/grabPay.jpg", "currencies": ["PHP"], "countries": ["PH"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 200665, "type": "bank_transfer", "formType": "standard", "title": "Banco Davivienda - PSE", "logo": "https://account.payop.com/assets/images/methods/davivienda.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 4524, "type": "bank_transfer", "formType": "standard", "title": "Metrobank Online Banking", "logo": "https://account.payop.com/assets/images/methods/metrobank.jpg", "currencies": ["PHP"], "countries": ["PH"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 4544, "type": "bank_transfer", "formType": "standard", "title": "Unionbank Online Banking", "logo": "https://account.payop.com/assets/images/methods/unionbank.jpg", "currencies": ["PHP"], "countries": ["PH"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 4503, "type": "bank_transfer", "formType": "standard", "title": "AUB Online/Cash Payment", "logo": "https://account.payop.com/assets/images/methods/AUBOnline.jpg", "currencies": ["PHP"], "countries": ["PH"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}]}}, {"identifier": 200029, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in Estonia", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["EUR"], "countries": ["EE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true}, {"name": "bank_country", "type": "bank_country", "regexp": "^(ES|IT|PT|FR|DE|FI|NL|EE|LT)$", "required": true}]}}, {"identifier": 605, "type": "bank_transfer", "formType": "standard", "title": "Caja Arequipa", "logo": "https://account.payop.com/assets/images/methods/cajaArequipa.jpg", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 200022, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in Spain", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["EUR"], "countries": ["ES"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true}]}}, {"identifier": 200030, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in Lithuania", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["EUR"], "countries": ["LT"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true}, {"name": "bank_country", "type": "bank_country", "regexp": "^(ES|IT|PT|FR|DE|FI|NL|EE|LT)$", "required": true}]}}, {"identifier": 578, "type": "bank_transfer", "formType": "standard", "title": "Banco de Crédito", "logo": "https://account.payop.com/assets/images/methods/bancoDeCredito.jpg", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 203801, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in United Kingdom", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["GBP"], "countries": ["GB"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}]}}, {"identifier": 620, "type": "bank_transfer", "formType": "standard", "title": "Banco AV Villas - PSE", "logo": "https://account.payop.com/assets/images/methods/av_villas_digital.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 697, "type": "bank_transfer", "formType": "standard", "title": "Scotiabank", "logo": "https://account.payop.com/assets/images/methods/scotiabank.jpg", "currencies": ["EUR"], "countries": ["PE"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 694, "type": "bank_transfer", "formType": "standard", "title": "Santander Mexico WS", "logo": "https://account.payop.com/assets/images/methods/santander.jpg", "currencies": ["EUR"], "countries": ["MX"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 200021, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in Italy", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["EUR"], "countries": ["IT"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true}]}}, {"identifier": 200020, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in France", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["EUR"], "countries": ["FR"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true}]}}, {"identifier": 200023, "type": "bank_transfer", "formType": "standard", "title": "Pay by bank in Portugal", "logo": "https://account.payop.com/assets/images/methods/pay-by-bank.png", "currencies": ["EUR"], "countries": ["PT"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "title": "Full name", "required": true}, {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true}, {"name": "bank_code", "type": "bank_code", "required": true}, {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true}]}}, {"identifier": 594, "type": "cash", "formType": "standard", "title": "SuRed (Acertemos)", "logo": "https://account.payop.com/assets/images/methods/suRed.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 689, "type": "cash", "formType": "standard", "title": "Walmart (Express de Líder)", "logo": "https://account.payop.com/assets/images/methods/expressDeLider.jpg", "currencies": ["EUR"], "countries": ["CL"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}]}}, {"identifier": 619, "type": "bank_transfer", "formType": "standard", "title": "Banco Agrario - PSE", "logo": "https://account.payop.com/assets/images/methods/banco_agrario_de_columbia.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 636, "type": "bank_transfer", "formType": "standard", "title": "DaviPlata - PSE", "logo": "https://account.payop.com/assets/images/methods/davi_plata.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}, {"identifier": 638, "type": "bank_transfer", "formType": "standard", "title": "Nequi - PSE", "logo": "https://account.payop.com/assets/images/methods/nequi.jpg", "currencies": ["EUR"], "countries": ["CO"], "config": {"fields": [{"name": "email", "type": "email", "required": true}, {"name": "name", "type": "text", "required": true}, {"name": "phone", "type": "text", "required": true}, {"name": "document", "type": "text", "title": "Document", "regexp": "^\\d{6,10}$", "required": true}]}}], "status": 1}