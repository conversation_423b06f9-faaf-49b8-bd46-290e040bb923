<?php
/**
 * Plugin Name: PayOp WooCommerce Payment Gateway
 * Plugin URI: https://github.com/payop/woocommerce-gateway
 * Description: Accept payments via PayOp with 122+ payment methods using Direct Integration (bypassing PayOp hosted checkout)
 * Version: 1.0.0
 * Author: PayOp
 * Author URI: https://payop.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: payop-woocommerce
 * Domain Path: /languages
 * Requires at least: 6.0
 * Tested up to: 6.4
 * Requires PHP: 8.0
 * WC requires at least: 7.0
 * WC tested up to: 8.5
 * Network: false
 * Woo: 12345678901234567890123456789012
 *
 * @package PayOp\WooCommerce
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('PAYOP_WC_VERSION', '1.0.0');
define('PAYOP_WC_PLUGIN_FILE', __FILE__);
define('PAYOP_WC_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('PAYOP_WC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('PAYOP_WC_PLUGIN_BASENAME', plugin_basename(__FILE__));

// PayOp API Configuration - From documented endpoints
define('PAYOP_API_BASE_URL', 'https://api.payop.com');
define('PAYOP_API_VERSION_V1', '/v1');
define('PAYOP_API_VERSION_V2', '/v2');

// Documented API Endpoints
define('PAYOP_ENDPOINT_PAYMENT_METHODS', '/v1/instrument-settings/payment-methods/available-for-application');
define('PAYOP_ENDPOINT_INVOICE_CREATE', '/v1/invoices/create');
define('PAYOP_ENDPOINT_INVOICE_GET', '/v1/invoices');
define('PAYOP_ENDPOINT_CHECKOUT_CREATE', '/v1/checkout/create');
define('PAYOP_ENDPOINT_CHECKOUT_STATUS', '/v1/checkout/check-invoice-status');
define('PAYOP_ENDPOINT_TRANSACTION_GET', '/v2/transactions');
define('PAYOP_ENDPOINT_CHECKOUT_VOID', '/v1/checkout/void');

// IPN Security - From documentation
define('PAYOP_IPN_ALLOWED_IPS', [
    '*************',
    '*************',
    '************',
    '*************'
]);

/**
 * Main PayOp WooCommerce Plugin Class
 */
final class PayOp_WooCommerce_Gateway {

    /**
     * Plugin instance
     *
     * @var PayOp_WooCommerce_Gateway
     */
    private static $instance = null;

    /**
     * Get plugin instance
     *
     * @return PayOp_WooCommerce_Gateway
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('plugins_loaded', [$this, 'init']);
        add_action('init', [$this, 'load_textdomain']);

        // Declare HPOS compatibility
        add_action('before_woocommerce_init', [$this, 'declare_hpos_compatibility']);

        // Activation and deactivation hooks
        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!$this->is_woocommerce_active()) {
            add_action('admin_notices', [$this, 'woocommerce_missing_notice']);
            return;
        }

        // Check PHP version
        if (!$this->is_php_version_supported()) {
            add_action('admin_notices', [$this, 'php_version_notice']);
            return;
        }

        // Initialize autoloader
        $this->init_autoloader();

        // Initialize the gateway
        add_action('woocommerce_loaded', [$this, 'init_gateway']);
    }

    /**
     * Check if WooCommerce is active
     *
     * @return bool
     */
    private function is_woocommerce_active() {
        return class_exists('WooCommerce');
    }

    /**
     * Check if PHP version is supported
     *
     * @return bool
     */
    private function is_php_version_supported() {
        return version_compare(PHP_VERSION, '8.0', '>=');
    }

    /**
     * Declare HPOS compatibility
     */
    public function declare_hpos_compatibility() {
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
        }
    }

    /**
     * Initialize autoloader
     */
    private function init_autoloader() {
        spl_autoload_register([$this, 'autoload']);
    }

    /**
     * Autoload classes
     *
     * @param string $class_name
     */
    public function autoload($class_name) {
        // Only autoload PayOp classes
        if (strpos($class_name, 'PayOp\\WooCommerce\\') !== 0) {
            return;
        }

        // Convert namespace to file path
        $class_file = str_replace('PayOp\\WooCommerce\\', '', $class_name);
        $class_file = str_replace('\\', DIRECTORY_SEPARATOR, $class_file);

        // Map specific classes to their file paths
        $class_map = [
            'Gateway' . DIRECTORY_SEPARATOR . 'PayOp_Gateway' => 'gateway' . DIRECTORY_SEPARATOR . 'class-payop-gateway.php',
            'API' . DIRECTORY_SEPARATOR . 'PayOp_API_Client' => 'api' . DIRECTORY_SEPARATOR . 'class-payop-api-client.php',
            'API' . DIRECTORY_SEPARATOR . 'PayOp_Payment_Methods' => 'api' . DIRECTORY_SEPARATOR . 'class-payop-payment-methods.php',
            'API' . DIRECTORY_SEPARATOR . 'PayOp_AJAX_Handler' => 'api' . DIRECTORY_SEPARATOR . 'class-payop-ajax-handler.php',
            'Utils' . DIRECTORY_SEPARATOR . 'PayOp_Config' => 'utils' . DIRECTORY_SEPARATOR . 'class-payop-config.php',
            'Utils' . DIRECTORY_SEPARATOR . 'PayOp_Security' => 'utils' . DIRECTORY_SEPARATOR . 'class-payop-security.php',
            'Utils' . DIRECTORY_SEPARATOR . 'PayOp_Error_Handler' => 'utils' . DIRECTORY_SEPARATOR . 'class-payop-error-handler.php',
            'Admin' . DIRECTORY_SEPARATOR . 'PayOp_Admin_Settings' => 'admin' . DIRECTORY_SEPARATOR . 'class-payop-admin-settings.php',
        ];

        if (isset($class_map[$class_file])) {
            $file_path = PAYOP_WC_PLUGIN_DIR . 'includes' . DIRECTORY_SEPARATOR . $class_map[$class_file];
        } else {
            // Fallback to automatic conversion
            $class_file = 'includes' . DIRECTORY_SEPARATOR . 'class-' . strtolower(str_replace('_', '-', $class_file)) . '.php';
            $file_path = PAYOP_WC_PLUGIN_DIR . $class_file;
        }

        if (file_exists($file_path)) {
            require_once $file_path;
        }
    }

    /**
     * Initialize the gateway
     */
    public function init_gateway() {
        // Load required classes first
        $this->load_required_classes();

        // Add the gateway to WooCommerce
        add_filter('woocommerce_payment_gateways', [$this, 'add_gateway']);

        // Initialize blocks support
        $this->init_blocks_support();

        // Initialize admin interface
        if (is_admin()) {
            $this->init_admin_interface();
        }

        // Initialize status handler
        $this->init_status_handler();
    }

    /**
     * Load required classes
     */
    private function load_required_classes() {
        // Load gateway class
        require_once PAYOP_WC_PLUGIN_DIR . 'includes/gateway/class-payop-gateway.php';

        // Load utility classes
        require_once PAYOP_WC_PLUGIN_DIR . 'includes/utils/class-payop-config.php';

        // Load API classes
        require_once PAYOP_WC_PLUGIN_DIR . 'includes/api/class-payop-api-client.php';
    }

    /**
     * Initialize admin interface
     */
    public function init_admin_interface() {
        if (!class_exists('PayOp\\WooCommerce\\Admin\\PayOp_Admin_Settings')) {
            require_once PAYOP_WC_PLUGIN_DIR . 'includes/admin/class-payop-admin-settings.php';
        }

        new \PayOp\WooCommerce\Admin\PayOp_Admin_Settings();
    }

    /**
     * Initialize status handler
     */
    public function init_status_handler() {
        if (!class_exists('PayOp\\WooCommerce\\API\\PayOp_Status_Handler')) {
            require_once PAYOP_WC_PLUGIN_DIR . 'includes/api/class-payop-status-handler.php';
        }

        new \PayOp\WooCommerce\API\PayOp_Status_Handler();
    }

    /**
     * Initialize WooCommerce Blocks support
     */
    public function init_blocks_support() {
        if (!class_exists('PayOp\\WooCommerce\\Blocks\\PayOp_Checkout_Block')) {
            require_once PAYOP_WC_PLUGIN_DIR . 'includes/blocks/class-payop-checkout-block.php';
        }
    }

    /**
     * Add gateway to WooCommerce
     *
     * @param array $gateways
     * @return array
     */
    public function add_gateway($gateways) {
        // Ensure the gateway class is loaded
        if (class_exists('\PayOp\WooCommerce\Gateway\PayOp_Gateway')) {
            $gateways[] = \PayOp\WooCommerce\Gateway\PayOp_Gateway::class;
        }
        return $gateways;
    }

    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'payop-woocommerce',
            false,
            dirname(PAYOP_WC_PLUGIN_BASENAME) . '/languages'
        );
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Check dependencies
        if (!$this->is_woocommerce_active()) {
            deactivate_plugins(PAYOP_WC_PLUGIN_BASENAME);
            wp_die(
                esc_html__('PayOp WooCommerce Gateway requires WooCommerce to be installed and active.', 'payop-woocommerce'),
                esc_html__('Plugin Activation Error', 'payop-woocommerce'),
                ['back_link' => true]
            );
        }

        if (!$this->is_php_version_supported()) {
            deactivate_plugins(PAYOP_WC_PLUGIN_BASENAME);
            wp_die(
                esc_html__('PayOp WooCommerce Gateway requires PHP 8.0 or higher.', 'payop-woocommerce'),
                esc_html__('Plugin Activation Error', 'payop-woocommerce'),
                ['back_link' => true]
            );
        }

        // Create database tables if needed
        $this->create_tables();

        // Set default options
        $this->set_default_options();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear any cached data
        $this->clear_cache();
    }

    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Payment methods cache table
        $table_name = $wpdb->prefix . 'payop_payment_methods';
        $sql = "CREATE TABLE $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            method_id int(11) NOT NULL,
            method_data longtext NOT NULL,
            last_updated datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_method_id (method_id)
        ) $charset_collate;";

        // Transaction logs table
        $table_name = $wpdb->prefix . 'payop_transactions';
        $sql .= "CREATE TABLE $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            order_id int(11) NOT NULL,
            invoice_id varchar(255) NOT NULL,
            transaction_id varchar(255),
            payment_method_id int(11) NOT NULL,
            status varchar(50) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_order_id (order_id),
            KEY idx_invoice_id (invoice_id)
        ) $charset_collate;";

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';

        // Capture any output from dbDelta to prevent activation issues
        ob_start();
        dbDelta($sql);
        ob_end_clean();
    }

    /**
     * Set default options
     */
    private function set_default_options() {
        // Set default gateway settings
        $default_settings = [
            'enabled' => 'no',
            'title' => __('PayOp Payment Gateway', 'payop-woocommerce'),
            'description' => __('Pay securely using PayOp with 122+ payment methods', 'payop-woocommerce'),
            'public_key' => '',
            'secret_key' => '',
            'jwt_token' => '',
            'project_id' => '',
            'debug' => 'no'
        ];

        foreach ($default_settings as $key => $value) {
            $option_key = 'woocommerce_payop_' . $key;
            if (false === get_option($option_key)) {
                update_option($option_key, $value);
            }
        }
    }

    /**
     * Clear cache
     */
    private function clear_cache() {
        global $wpdb;
        
        // Clear payment methods cache
        $wpdb->query("DELETE FROM {$wpdb->prefix}payop_payment_methods");
        
        // Clear any transients
        delete_transient('payop_payment_methods');
    }

    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        echo '<div class="notice notice-error"><p>';
        echo esc_html__('PayOp WooCommerce Gateway requires WooCommerce to be installed and active.', 'payop-woocommerce');
        echo '</p></div>';
    }

    /**
     * PHP version notice
     */
    public function php_version_notice() {
        echo '<div class="notice notice-error"><p>';
        printf(
            esc_html__('PayOp WooCommerce Gateway requires PHP 8.0 or higher. You are running PHP %s.', 'payop-woocommerce'),
            esc_html(PHP_VERSION)
        );
        echo '</p></div>';
    }
}

// Initialize the plugin
PayOp_WooCommerce_Gateway::instance();
