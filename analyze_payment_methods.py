#!/usr/bin/env python3
import json
from collections import defaultdict, Counter

# Read the payment methods response
with open('payment_methods_response.json', 'r') as f:
    data = json.load(f)

payment_methods = data['data']

print(f"Total Payment Methods: {len(payment_methods)}")
print("=" * 80)

# Analyze by type
types = Counter([method['type'] for method in payment_methods])
print("\nPayment Method Types:")
for type_name, count in types.most_common():
    print(f"  {type_name}: {count} methods")

# Analyze by currency
all_currencies = set()
currency_methods = defaultdict(list)
for method in payment_methods:
    for currency in method['currencies']:
        all_currencies.add(currency)
        currency_methods[currency].append(method['identifier'])

print(f"\nSupported Currencies ({len(all_currencies)}):")
for currency in sorted(all_currencies):
    print(f"  {currency}: {len(currency_methods[currency])} methods")

# Analyze by country
all_countries = set()
country_methods = defaultdict(list)
for method in payment_methods:
    for country in method['countries']:
        all_countries.add(country)
        country_methods[country].append(method['identifier'])

print(f"\nSupported Countries ({len(all_countries)}):")
top_countries = sorted(country_methods.items(), key=lambda x: len(x[1]), reverse=True)[:20]
for country, method_ids in top_countries:
    print(f"  {country}: {len(method_ids)} methods")

# Analyze field requirements
field_types = Counter()
field_requirements = defaultdict(int)

for method in payment_methods:
    if 'config' in method and 'fields' in method['config']:
        for field in method['config']['fields']:
            field_types[field['type']] += 1
            field_name = field['name']
            field_requirements[field_name] += 1

print(f"\nField Types Used:")
for field_type, count in field_types.most_common():
    print(f"  {field_type}: {count} occurrences")

print(f"\nMost Common Required Fields:")
for field_name, count in sorted(field_requirements.items(), key=lambda x: x[1], reverse=True)[:15]:
    print(f"  {field_name}: {count} methods")

# Detailed examples by type
print("\n" + "=" * 80)
print("DETAILED EXAMPLES BY TYPE")
print("=" * 80)

examples_by_type = defaultdict(list)
for method in payment_methods:
    examples_by_type[method['type']].append(method)

for type_name in sorted(examples_by_type.keys()):
    print(f"\n{type_name.upper()} ({len(examples_by_type[type_name])} methods):")
    
    # Show first 3 examples
    for i, method in enumerate(examples_by_type[type_name][:3]):
        print(f"\n  Example {i+1}: {method['title']} (ID: {method['identifier']})")
        print(f"    Currencies: {', '.join(method['currencies'])}")
        print(f"    Countries: {', '.join(method['countries'][:10])}{'...' if len(method['countries']) > 10 else ''}")
        
        if 'config' in method and 'fields' in method['config']:
            print(f"    Required Fields:")
            for field in method['config']['fields']:
                required = "✓" if field.get('required', False) else "○"
                field_title = field.get('title', field['name'])
                regexp = f" (pattern: {field['regexp']})" if 'regexp' in field else ""
                print(f"      {required} {field_title} ({field['type']}){regexp}")
        
        if i < len(examples_by_type[type_name]) - 1:
            print()

print("\n" + "=" * 80)
print("SPECIAL FIELD PATTERNS")
print("=" * 80)

# Find methods with special field patterns
special_patterns = {}
for method in payment_methods:
    if 'config' in method and 'fields' in method['config']:
        for field in method['config']['fields']:
            if 'regexp' in field:
                pattern_key = f"{field['name']}_{field['type']}"
                if pattern_key not in special_patterns:
                    special_patterns[pattern_key] = []
                special_patterns[pattern_key].append({
                    'method': method['title'],
                    'pattern': field['regexp'],
                    'title': field.get('title', field['name'])
                })

for pattern_key, examples in special_patterns.items():
    print(f"\n{pattern_key}:")
    unique_patterns = {}
    for example in examples:
        pattern = example['pattern']
        if pattern not in unique_patterns:
            unique_patterns[pattern] = []
        unique_patterns[pattern].append(example['method'])
    
    for pattern, methods in unique_patterns.items():
        print(f"  Pattern: {pattern}")
        print(f"  Used by: {', '.join(methods[:3])}{'...' if len(methods) > 3 else ''}")
