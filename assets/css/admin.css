/**
 * PayOp Admin Styles
 * 
 * Styles for the admin interface and settings pages
 */

/* Admin page layout */
.payop-methods-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    padding: 15px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.payop-methods-header h3 {
    margin: 0;
}

/* Statistics grid */
.payop-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.payop-stat-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.payop-stat-card h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.stat-number {
    font-size: 36px;
    font-weight: bold;
    color: #0073aa;
    line-height: 1;
}

.stat-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.stat-list li {
    padding: 5px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
}

.stat-list li:last-child {
    border-bottom: none;
}

/* Loading states */
.payop-loading {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

.payop-loading:before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: payop-spin 1s linear infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes payop-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Connection test result */
#connection-result {
    margin-top: 15px;
}

#connection-result .notice {
    margin: 0;
}

/* Transaction status styling */
.status-pending {
    color: #f56e28;
    font-weight: bold;
}

.status-completed {
    color: #00a32a;
    font-weight: bold;
}

.status-failed {
    color: #d63638;
    font-weight: bold;
}

.status-cancelled {
    color: #666;
    font-weight: bold;
}

/* Log viewer */
.payop-log-viewer {
    margin: 20px 0;
}

.payop-log-viewer textarea {
    background: #f1f1f1;
    border: 1px solid #ddd;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    resize: vertical;
}

/* Form styling improvements */
.form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
}

.form-table td {
    padding: 15px 10px;
}

.form-table input[type="text"],
.form-table input[type="password"],
.form-table textarea {
    width: 100%;
    max-width: 400px;
}

.form-table textarea {
    height: 80px;
    resize: vertical;
}

.form-table .description {
    margin-top: 5px;
    font-style: italic;
    color: #666;
}

/* Button styling */
.button.payop-test {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

.button.payop-test:hover {
    background: #005a87;
    border-color: #005a87;
}

.button.payop-refresh {
    background: #00a32a;
    border-color: #00a32a;
    color: #fff;
}

.button.payop-refresh:hover {
    background: #007a1f;
    border-color: #007a1f;
}

/* Tab content */
.tab-content {
    background: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
}

/* Methods list */
#methods-list {
    margin-top: 20px;
}

.payop-method-item {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.payop-method-info {
    flex: 1;
}

.payop-method-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.payop-method-details {
    color: #666;
    font-size: 14px;
}

.payop-method-actions {
    display: flex;
    gap: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
    .payop-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .payop-methods-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .form-table th,
    .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .payop-method-item {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

/* Success/error messages */
.payop-message {
    padding: 10px 15px;
    border-radius: 4px;
    margin: 15px 0;
}

.payop-message.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.payop-message.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Settings sections */
.payop-settings-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.payop-settings-section:last-child {
    border-bottom: none;
}

.payop-settings-section h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

/* Help text */
.payop-help {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 15px;
    margin: 15px 0;
}

.payop-help h4 {
    margin-top: 0;
    color: #0073aa;
}

.payop-help ul {
    margin-bottom: 0;
}

/* API status indicator */
.payop-api-status {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.payop-api-status.connected {
    background: #00a32a;
}

.payop-api-status.disconnected {
    background: #d63638;
}

.payop-api-status.unknown {
    background: #666;
}
