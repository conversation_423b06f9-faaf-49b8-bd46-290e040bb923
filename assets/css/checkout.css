/**
 * PayOp WooCommerce Checkout Styles
 * 
 * Styles for payment method selection and dynamic fields
 */

/* Payment form container */
#payop-payment-form {
    margin: 1em 0;
    padding: 1em;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
}

/* Loading indicator */
#payop-payment-methods-loading {
    text-align: center;
    padding: 2em;
    color: #666;
}

#payop-payment-methods-loading:before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-top: 2px solid #666;
    border-radius: 50%;
    animation: payop-spin 1s linear infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes payop-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Payment methods grid */
#payop-payment-methods {
    margin: 1em 0;
}

.payop-payment-method-group {
    margin-bottom: 2em;
}

.payop-payment-method-group h4 {
    margin: 0 0 1em 0;
    padding: 0.5em;
    background: #e9e9e9;
    border-radius: 4px;
    font-size: 1.1em;
    font-weight: bold;
}

.payop-payment-methods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1em;
}

.payop-payment-method {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 1em;
    background: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payop-payment-method:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 4px rgba(0, 115, 170, 0.1);
}

.payop-payment-method.selected {
    border-color: #0073aa;
    background: #f0f8ff;
}

.payop-payment-method input[type="radio"] {
    margin-right: 0.5em;
}

.payop-payment-method-logo {
    max-width: 40px;
    max-height: 40px;
    float: right;
    margin-left: 1em;
}

.payop-payment-method-title {
    font-weight: bold;
    margin-bottom: 0.5em;
}

.payop-payment-method-info {
    font-size: 0.9em;
    color: #666;
}

/* Additional fields */
#payop-additional-fields {
    margin: 1em 0;
    padding: 1em;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
}

#payop-additional-fields h4 {
    margin: 0 0 1em 0;
    color: #333;
    font-size: 1.1em;
}

.payop-field-group {
    margin-bottom: 1.5em;
    position: relative;
}

.payop-field-group label {
    display: block;
    margin-bottom: 0.5em;
    font-weight: bold;
    color: #333;
}

.payop-field-group input,
.payop-field-group select {
    width: 100%;
    padding: 0.75em;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1em;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.payop-field-group input:focus,
.payop-field-group select:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
}

.payop-field-group input.error,
.payop-field-group select.error {
    border-color: #d63638;
    box-shadow: 0 0 0 2px rgba(214, 54, 56, 0.1);
}

.payop-field-error {
    color: #d63638;
    font-size: 0.9em;
    margin-top: 0.5em;
    display: block;
}

.payop-field-description {
    font-size: 0.9em;
    color: #666;
    margin-top: 0.5em;
    font-style: italic;
}

/* Field validation states */
.payop-field input.valid {
    border-color: #00a32a;
}

.payop-field input.valid:focus {
    box-shadow: 0 0 0 2px rgba(0, 163, 42, 0.1);
}

/* Input masks and special fields */
.payop-field input[data-mask] {
    font-family: monospace;
}

/* Document field styling */
.payop-field[data-field="document"] input {
    letter-spacing: 1px;
}

/* Date field styling */
.payop-field[data-field="date_of_birth"] input {
    font-family: monospace;
}

/* Bank selection fields */
.payop-field[data-field="bank_code"] select,
.payop-field[data-field="bank_type"] select {
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 0.7em top 50%;
    background-size: 0.65em auto;
}

/* Loading states */
.payop-loading {
    text-align: center;
    padding: 2em;
    color: #666;
}

.payop-loading:before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-top: 2px solid #666;
    border-radius: 50%;
    animation: payop-spin 1s linear infinite;
    margin-right: 10px;
    vertical-align: middle;
}

/* Required field indicator */
.payop-required:after {
    content: " *";
    color: #d63638;
}

/* Responsive design */
@media (max-width: 768px) {
    .payop-payment-methods-grid {
        grid-template-columns: 1fr;
    }
    
    #payop-payment-form {
        margin: 0.5em 0;
        padding: 0.5em;
    }
}

/* Error messages */
.payop-error {
    color: #d63638;
    background: #fff2f2;
    border: 1px solid #d63638;
    border-radius: 4px;
    padding: 1em;
    margin: 1em 0;
}

/* Success messages */
.payop-success {
    color: #00a32a;
    background: #f0fff4;
    border: 1px solid #00a32a;
    border-radius: 4px;
    padding: 1em;
    margin: 1em 0;
}
