/**
 * PayOp WooCommerce Blocks Checkout Component
 * 
 * React component for WooCommerce Blocks checkout integration
 */

const { registerPaymentMethod } = window.wc.wcBlocksRegistry;
const { createElement, useState, useEffect } = window.wp.element;
const { __ } = window.wp.i18n;
const { decodeEntities } = window.wp.htmlEntities;

/**
 * PayOp Payment Method Component
 */
const PayOpPaymentMethod = ({ billing, shippingData, eventRegistration, emitResponse }) => {
    const [selectedMethod, setSelectedMethod] = useState(null);
    const [paymentMethods, setPaymentMethods] = useState([]);
    const [additionalFields, setAdditionalFields] = useState([]);
    const [fieldValues, setFieldValues] = useState({});
    const [isLoading, setIsLoading] = useState(true);
    const [errors, setErrors] = useState({});

    const { onPaymentSetup, onCheckoutValidation } = eventRegistration;

    /**
     * Load payment methods on component mount
     */
    useEffect(() => {
        loadPaymentMethods();
    }, [billing.billingData.country, billing.billingData.currency]);

    /**
     * Setup payment processing
     */
    useEffect(() => {
        const unsubscribe = onPaymentSetup(() => {
            if (!selectedMethod) {
                return {
                    type: emitResponse.responseTypes.ERROR,
                    message: __('Please select a payment method.', 'payop-woocommerce'),
                };
            }

            // Validate additional fields
            const validationResult = validateAdditionalFields();
            if (!validationResult.isValid) {
                return {
                    type: emitResponse.responseTypes.ERROR,
                    message: validationResult.message,
                };
            }

            return {
                type: emitResponse.responseTypes.SUCCESS,
                meta: {
                    paymentMethodData: {
                        payop_payment_method: selectedMethod,
                        payop_additional_fields: fieldValues
                    },
                },
            };
        });

        return unsubscribe;
    }, [selectedMethod, fieldValues, onPaymentSetup, emitResponse]);

    /**
     * Load available payment methods
     */
    const loadPaymentMethods = async () => {
        setIsLoading(true);
        
        try {
            const response = await fetch(payopBlockData.ajaxUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'payop_get_payment_methods',
                    nonce: payopBlockData.nonce,
                    currency: billing.billingData.currency || 'EUR',
                    country: billing.billingData.country || 'US',
                    group_by_type: true
                })
            });

            const data = await response.json();
            
            if (data.success) {
                setPaymentMethods(data.data.methods);
            } else {
                console.error('Failed to load payment methods:', data.data.message);
            }
        } catch (error) {
            console.error('Error loading payment methods:', error);
        } finally {
            setIsLoading(false);
        }
    };

    /**
     * Handle payment method selection
     */
    const handleMethodSelection = async (methodId) => {
        setSelectedMethod(methodId);
        setAdditionalFields([]);
        setFieldValues({});
        setErrors({});

        // Load additional fields for selected method
        try {
            const response = await fetch(payopBlockData.ajaxUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'payop_get_method_fields',
                    nonce: payopBlockData.nonce,
                    method_id: methodId,
                    country: billing.billingData.country || 'US'
                })
            });

            const data = await response.json();
            
            if (data.success) {
                setAdditionalFields(data.data.fields);
            }
        } catch (error) {
            console.error('Error loading method fields:', error);
        }
    };

    /**
     * Handle field value change
     */
    const handleFieldChange = (fieldName, value) => {
        setFieldValues(prev => ({
            ...prev,
            [fieldName]: value
        }));

        // Clear error for this field
        if (errors[fieldName]) {
            setErrors(prev => ({
                ...prev,
                [fieldName]: null
            }));
        }
    };

    /**
     * Validate additional fields
     */
    const validateAdditionalFields = () => {
        const newErrors = {};
        let isValid = true;

        additionalFields.forEach(field => {
            if (field.required && !fieldValues[field.name]) {
                newErrors[field.name] = __('This field is required.', 'payop-woocommerce');
                isValid = false;
            }
        });

        setErrors(newErrors);

        return {
            isValid,
            message: isValid ? '' : __('Please fill in all required fields.', 'payop-woocommerce')
        };
    };

    /**
     * Render payment method groups
     */
    const renderPaymentMethods = () => {
        if (isLoading) {
            return createElement('div', { className: 'payop-loading' }, 
                __('Loading payment methods...', 'payop-woocommerce')
            );
        }

        if (!paymentMethods || Object.keys(paymentMethods).length === 0) {
            return createElement('div', { className: 'payop-error' }, 
                __('No payment methods available.', 'payop-woocommerce')
            );
        }

        return Object.entries(paymentMethods).map(([type, group]) => 
            createElement('div', { key: type, className: 'payop-method-group' }, [
                createElement('h4', { key: 'title' }, group.title),
                createElement('div', { key: 'methods', className: 'payop-methods-grid' },
                    group.methods.map(method => 
                        createElement('div', {
                            key: method.id,
                            className: `payop-method ${selectedMethod === method.id ? 'selected' : ''}`,
                            onClick: () => handleMethodSelection(method.id)
                        }, [
                            createElement('input', {
                                key: 'radio',
                                type: 'radio',
                                name: 'payop_payment_method',
                                value: method.id,
                                checked: selectedMethod === method.id,
                                onChange: () => handleMethodSelection(method.id)
                            }),
                            method.logo && createElement('img', {
                                key: 'logo',
                                src: method.logo,
                                alt: method.title,
                                className: 'payop-method-logo'
                            }),
                            createElement('div', { key: 'title', className: 'payop-method-title' }, method.title),
                            method.required_fields_count > 0 && createElement('div', {
                                key: 'info',
                                className: 'payop-method-info'
                            }, `${method.required_fields_count} additional field(s) required`)
                        ])
                    )
                )
            ])
        );
    };

    /**
     * Render additional fields
     */
    const renderAdditionalFields = () => {
        if (additionalFields.length === 0) {
            return null;
        }

        return createElement('div', { className: 'payop-additional-fields' }, [
            createElement('h4', { key: 'title' }, __('Additional Information Required', 'payop-woocommerce')),
            ...additionalFields.map(field => 
                createElement('div', {
                    key: field.name,
                    className: 'payop-field-group'
                }, [
                    createElement('label', {
                        key: 'label',
                        className: field.required ? 'payop-required' : ''
                    }, field.title),
                    field.type === 'select' && field.options ? 
                        createElement('select', {
                            key: 'select',
                            value: fieldValues[field.name] || '',
                            onChange: (e) => handleFieldChange(field.name, e.target.value),
                            required: field.required
                        }, [
                            createElement('option', { key: 'empty', value: '' }, __('Select...', 'payop-woocommerce')),
                            ...Object.entries(field.options).map(([value, label]) =>
                                createElement('option', { key: value, value }, label)
                            )
                        ]) :
                        createElement('input', {
                            key: 'input',
                            type: field.type || 'text',
                            value: fieldValues[field.name] || '',
                            onChange: (e) => handleFieldChange(field.name, e.target.value),
                            placeholder: field.placeholder || '',
                            required: field.required,
                            maxLength: field.max_length || undefined
                        }),
                    field.description && createElement('div', {
                        key: 'description',
                        className: 'payop-field-description'
                    }, field.description),
                    errors[field.name] && createElement('div', {
                        key: 'error',
                        className: 'payop-field-error'
                    }, errors[field.name])
                ])
            )
        ]);
    };

    return createElement('div', { className: 'payop-payment-form' }, [
        renderPaymentMethods(),
        renderAdditionalFields()
    ]);
};

/**
 * PayOp Payment Method Configuration
 */
const payopPaymentMethod = {
    name: 'payop',
    label: decodeEntities(payopBlockData.title) || __('PayOp Payment Gateway', 'payop-woocommerce'),
    content: createElement(PayOpPaymentMethod),
    edit: createElement(PayOpPaymentMethod),
    canMakePayment: () => true,
    ariaLabel: decodeEntities(payopBlockData.title) || __('PayOp Payment Gateway', 'payop-woocommerce'),
    supports: {
        features: payopBlockData.supports || ['products']
    }
};

// Register the payment method
registerPaymentMethod(payopPaymentMethod);
