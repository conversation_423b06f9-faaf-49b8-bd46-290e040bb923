/**
 * PayOp WooCommerce Checkout JavaScript
 *
 * Handles dynamic payment method selection and field rendering
 * Implements Direct Integration approach - bypasses PayOp hosted checkout
 */

(function($) {
    'use strict';

    /**
     * PayOp Checkout Handler
     */
    var PayOpCheckout = {

        // Current state
        selectedMethod: null,
        loadedMethods: [],
        currentFields: [],

        /**
         * Initialize
         */
        init: function() {
            this.bindEvents();
            this.loadPaymentMethods();
        },

        /**
         * Bind events
         */
        bindEvents: function() {
            // Payment method selection
            $(document).on('change', 'input[name="payop_payment_method"]', this.onPaymentMethodChange.bind(this));

            // Field validation
            $(document).on('blur', '.payop-field input, .payop-field select', this.validateField.bind(this));

            // Form submission
            $('form.checkout').on('checkout_place_order_payop', this.validateForm.bind(this));
        },

        /**
         * Load available payment methods
         */
        loadPaymentMethods: function() {
            var self = this;
            var $container = $('#payop-payment-methods');
            var $loading = $('#payop-payment-methods-loading');

            // Get currency and country from checkout
            var currency = this.getCheckoutCurrency();
            var country = this.getCheckoutCountry();

            $.ajax({
                url: payop_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'payop_get_payment_methods',
                    nonce: payop_params.nonce,
                    currency: currency,
                    country: country,
                    group_by_type: true
                },
                success: function(response) {
                    $loading.hide();

                    if (response.success) {
                        self.loadedMethods = response.data.methods;
                        self.renderPaymentMethods(response.data.methods, response.data.grouped);
                        $container.show();
                    } else {
                        self.showError(response.data.message || 'Failed to load payment methods');
                    }
                },
                error: function() {
                    $loading.hide();
                    self.showError('Network error loading payment methods');
                }
            });
        },

        /**
         * Render payment methods
         */
        renderPaymentMethods: function(methods, grouped) {
            var $container = $('#payop-payment-methods');
            var html = '';

            if (grouped) {
                // Render grouped methods
                $.each(methods, function(type, group) {
                    html += '<div class="payop-payment-method-group">';
                    html += '<h4>' + group.title + '</h4>';
                    html += '<div class="payop-payment-methods-grid">';

                    $.each(group.methods, function(index, method) {
                        html += this.renderSingleMethod(method);
                    }.bind(this));

                    html += '</div></div>';
                }.bind(this));
            } else {
                // Render flat list
                html += '<div class="payop-payment-methods-grid">';
                $.each(methods, function(index, method) {
                    html += this.renderSingleMethod(method);
                }.bind(this));
                html += '</div>';
            }

            $container.html(html);
        },

        /**
         * Render single payment method
         */
        renderSingleMethod: function(method) {
            var html = '<div class="payop-payment-method" data-method-id="' + method.id + '">';
            html += '<label>';
            html += '<input type="radio" name="payop_payment_method" value="' + method.id + '">';

            if (method.logo) {
                html += '<img src="' + method.logo + '" alt="' + method.title + '" class="payop-payment-method-logo">';
            }

            html += '<div class="payop-payment-method-title">' + method.title + '</div>';

            if (method.required_fields_count > 0) {
                html += '<div class="payop-payment-method-info">';
                html += method.required_fields_count + ' additional field(s) required';
                html += '</div>';
            }

            html += '</label></div>';

            return html;
        },

        /**
         * Handle payment method change
         */
        onPaymentMethodChange: function(e) {
            var methodId = $(e.target).val();
            this.selectedMethod = methodId;

            // Update visual selection
            $('.payop-payment-method').removeClass('selected');
            $(e.target).closest('.payop-payment-method').addClass('selected');

            // Load additional fields
            this.loadMethodFields(methodId);
        },

        /**
         * Load additional fields for selected method
         */
        loadMethodFields: function(methodId) {
            var self = this;
            var $container = $('#payop-additional-fields');
            var country = this.getCheckoutCountry();

            $container.html('<div class="payop-loading">Loading additional fields...</div>').show();

            $.ajax({
                url: payop_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'payop_get_method_fields',
                    nonce: payop_params.nonce,
                    method_id: methodId,
                    country: country
                },
                success: function(response) {
                    if (response.success) {
                        self.currentFields = response.data.fields;
                        self.renderAdditionalFields(response.data.fields);
                    } else {
                        $container.html('<div class="payop-error">' + (response.data.message || 'Failed to load fields') + '</div>');
                    }
                },
                error: function() {
                    $container.html('<div class="payop-error">Network error loading fields</div>');
                }
            });
        },

        /**
         * Render additional fields
         */
        renderAdditionalFields: function(fields) {
            var self = this;
            var $container = $('#payop-additional-fields');
            var html = '';

            if (fields.length === 0) {
                $container.hide();
                return;
            }

            html += '<h4>Additional Information Required</h4>';

            $.each(fields, function(index, field) {
                html += '<div class="payop-field-group payop-field" data-field="' + field.name + '">';
                html += '<label for="payop_' + field.name + '"';
                if (field.required) {
                    html += ' class="payop-required"';
                }
                html += '>' + field.title + '</label>';

                if (field.type === 'select' && field.options) {
                    html += '<select id="payop_' + field.name + '" name="payop_' + field.name + '"';
                    if (field.required) {
                        html += ' required';
                    }
                    html += ' data-field-config="' + self.escapeHtml(JSON.stringify(field)) + '">';
                    html += '<option value="">Select...</option>';

                    // Populate options
                    $.each(field.options, function(value, label) {
                        html += '<option value="' + value + '">' + label + '</option>';
                    });

                    html += '</select>';
                } else {
                    html += '<input type="' + (field.type || 'text') + '" id="payop_' + field.name + '" name="payop_' + field.name + '"';
                    if (field.placeholder) {
                        html += ' placeholder="' + field.placeholder + '"';
                    }
                    if (field.validation_pattern) {
                        html += ' data-pattern="' + field.validation_pattern + '"';
                    }
                    if (field.input_mask) {
                        html += ' data-mask="' + field.input_mask + '"';
                    }
                    if (field.max_length) {
                        html += ' maxlength="' + field.max_length + '"';
                    }
                    if (field.required) {
                        html += ' required';
                    }
                    html += ' data-field-config="' + self.escapeHtml(JSON.stringify(field)) + '">';
                }

                if (field.description) {
                    html += '<div class="payop-field-description">' + field.description + '</div>';
                }

                html += '<div class="payop-field-error" style="display:none;"></div>';
                html += '</div>';
            });

            $container.html(html);

            // Apply input masks if available
            this.applyInputMasks();
        },

        /**
         * Validate individual field
         */
        validateField: function(e) {
            var $field = $(e.target);
            var fieldName = $field.attr('name').replace('payop_', '');
            var fieldValue = $field.val();
            var fieldConfig = $field.data('field-config') || {};
            var country = this.getCheckoutCountry();

            $.ajax({
                url: payop_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'payop_validate_field',
                    nonce: payop_params.nonce,
                    field_name: fieldName,
                    field_value: fieldValue,
                    field_config: JSON.stringify(fieldConfig),
                    country: country
                },
                success: function(response) {
                    var $errorDiv = $field.closest('.payop-field').find('.payop-field-error');

                    if (response.success) {
                        $field.removeClass('error');
                        $errorDiv.hide();
                    } else {
                        $field.addClass('error');
                        $errorDiv.text(response.data.message).show();
                    }
                }
            });
        },

        /**
         * Apply input masks to fields
         */
        applyInputMasks: function() {
            // Apply input masks if jQuery mask plugin is available
            if (typeof $.fn.mask !== 'undefined') {
                $('[data-mask]').each(function() {
                    var mask = $(this).data('mask');
                    $(this).mask(mask);
                });
            }
        },

        /**
         * Escape HTML for safe insertion
         */
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        },

        /**
         * Validate form before submission
         */
        validateForm: function() {
            var selectedMethod = $('input[name="payop_payment_method"]:checked').val();

            if (!selectedMethod) {
                this.showError('Please select a payment method.');
                return false;
            }

            // Validate all required fields
            var isValid = true;
            $('.payop-field input[required], .payop-field select[required]').each(function() {
                if (!$(this).val()) {
                    $(this).addClass('error');
                    isValid = false;
                }
            });

            if (!isValid) {
                this.showError('Please fill in all required fields.');
                return false;
            }

            return true;
        },

        /**
         * Get checkout currency
         */
        getCheckoutCurrency: function() {
            // Try to get from WooCommerce checkout data
            return $('input[name="billing_currency"]').val() ||
                   $('.woocommerce-checkout').data('currency') ||
                   'EUR'; // Default fallback
        },

        /**
         * Get checkout country
         */
        getCheckoutCountry: function() {
            // Try to get from billing country field
            return $('select[name="billing_country"]').val() ||
                   $('input[name="billing_country"]').val() ||
                   'US'; // Default fallback
        },

        /**
         * Show error message
         */
        showError: function(message) {
            var $container = $('#payop-payment-methods');
            $container.html('<div class="payop-error">' + message + '</div>');
        }
    };

    /**
     * Initialize when DOM is ready
     */
    $(document).ready(function() {
        // Only initialize on checkout page with PayOp gateway
        if ($('#payop-payment-form').length > 0) {
            PayOpCheckout.init();
        }
    });

})(jQuery);
