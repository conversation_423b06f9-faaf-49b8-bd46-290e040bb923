/**
 * PayOp Admin JavaScript
 * 
 * Handles admin interface interactions and AJAX requests
 */

(function($) {
    'use strict';

    /**
     * PayOp Admin Handler
     */
    var PayOpAdmin = {
        
        /**
         * Initialize
         */
        init: function() {
            this.bindEvents();
            this.loadMethodStats();
        },

        /**
         * Bind events
         */
        bindEvents: function() {
            $('#test-connection').on('click', this.testConnection.bind(this));
            $('#refresh-methods').on('click', this.refreshMethods.bind(this));
        },

        /**
         * Test API connection
         */
        testConnection: function(e) {
            e.preventDefault();
            
            var $button = $(e.target);
            var originalText = $button.text();
            
            $button.text(payopAdmin.strings.testing).prop('disabled', true);
            $('#connection-result').hide();

            $.ajax({
                url: payopAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'payop_test_connection',
                    nonce: payopAdmin.nonce
                },
                success: function(response) {
                    var $result = $('#connection-result');
                    
                    if (response.success) {
                        $result.html('<div class="notice notice-success"><p>' + 
                            response.data.message + 
                            ' (' + response.data.methods_count + ' payment methods available)</p></div>');
                    } else {
                        $result.html('<div class="notice notice-error"><p>' + 
                            (response.data.message || payopAdmin.strings.error) + '</p></div>');
                    }
                    
                    $result.show();
                },
                error: function() {
                    $('#connection-result').html('<div class="notice notice-error"><p>' + 
                        payopAdmin.strings.error + '</p></div>').show();
                },
                complete: function() {
                    $button.text(originalText).prop('disabled', false);
                }
            });
        },

        /**
         * Refresh payment methods
         */
        refreshMethods: function(e) {
            e.preventDefault();
            
            var $button = $(e.target);
            var originalText = $button.text();
            
            $button.text(payopAdmin.strings.refreshing).prop('disabled', true);

            $.ajax({
                url: payopAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'payop_refresh_methods',
                    nonce: payopAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        $('<div class="notice notice-success is-dismissible"><p>' + 
                            response.data.message + '</p></div>')
                            .insertAfter('.payop-methods-header')
                            .delay(3000)
                            .fadeOut();
                        
                        // Reload stats
                        PayOpAdmin.loadMethodStats();
                    } else {
                        alert(response.data.message || 'Failed to refresh methods');
                    }
                },
                error: function() {
                    alert('Network error occurred');
                },
                complete: function() {
                    $button.text(originalText).prop('disabled', false);
                }
            });
        },

        /**
         * Load method statistics
         */
        loadMethodStats: function() {
            var $container = $('#methods-stats');
            
            if ($container.length === 0) {
                return;
            }

            $container.html('<div class="payop-loading">Loading statistics...</div>');

            $.ajax({
                url: payopAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'payop_get_method_stats',
                    nonce: payopAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        PayOpAdmin.renderMethodStats(response.data);
                    } else {
                        $container.html('<div class="notice notice-error"><p>Failed to load statistics</p></div>');
                    }
                },
                error: function() {
                    $container.html('<div class="notice notice-error"><p>Network error loading statistics</p></div>');
                }
            });
        },

        /**
         * Render method statistics
         */
        renderMethodStats: function(stats) {
            var $container = $('#methods-stats');
            var html = '';

            // Total methods
            html += '<div class="payop-stat-card">';
            html += '<h4>Total Payment Methods</h4>';
            html += '<div class="stat-number">' + stats.total_methods + '</div>';
            html += '</div>';

            // By type
            if (stats.by_type) {
                html += '<div class="payop-stat-card">';
                html += '<h4>By Type</h4>';
                html += '<ul class="stat-list">';
                $.each(stats.by_type, function(type, count) {
                    var typeLabel = PayOpAdmin.getTypeLabel(type);
                    html += '<li>' + typeLabel + ': <strong>' + count + '</strong></li>';
                });
                html += '</ul>';
                html += '</div>';
            }

            // By currency
            if (stats.by_currency) {
                html += '<div class="payop-stat-card">';
                html += '<h4>Top Currencies</h4>';
                html += '<ul class="stat-list">';
                
                // Sort currencies by count and show top 5
                var sortedCurrencies = Object.entries(stats.by_currency)
                    .sort(function(a, b) { return b[1] - a[1]; })
                    .slice(0, 5);
                
                $.each(sortedCurrencies, function(index, item) {
                    html += '<li>' + item[0] + ': <strong>' + item[1] + '</strong></li>';
                });
                html += '</ul>';
                html += '</div>';
            }

            // Field requirements
            if (stats.field_requirements) {
                html += '<div class="payop-stat-card">';
                html += '<h4>Common Required Fields</h4>';
                html += '<ul class="stat-list">';
                
                // Sort fields by frequency and show top 5
                var sortedFields = Object.entries(stats.field_requirements)
                    .sort(function(a, b) { return b[1] - a[1]; })
                    .slice(0, 5);
                
                $.each(sortedFields, function(index, item) {
                    var fieldLabel = PayOpAdmin.getFieldLabel(item[0]);
                    html += '<li>' + fieldLabel + ': <strong>' + item[1] + '</strong></li>';
                });
                html += '</ul>';
                html += '</div>';
            }

            $container.html(html);
        },

        /**
         * Get human-readable type label
         */
        getTypeLabel: function(type) {
            var labels = {
                'cards_international': 'Credit/Debit Cards',
                'bank_transfer': 'Bank Transfer',
                'ewallet': 'E-Wallets',
                'cash': 'Cash Payments',
                'crypto': 'Cryptocurrency'
            };
            
            return labels[type] || type.replace('_', ' ').replace(/\b\w/g, function(l) {
                return l.toUpperCase();
            });
        },

        /**
         * Get human-readable field label
         */
        getFieldLabel: function(field) {
            var labels = {
                'email': 'Email Address',
                'name': 'Full Name',
                'phone': 'Phone Number',
                'document': 'Document Number',
                'date_of_birth': 'Date of Birth',
                'bank_code': 'Bank Code',
                'bank_type': 'Bank Type'
            };
            
            return labels[field] || field.replace('_', ' ').replace(/\b\w/g, function(l) {
                return l.toUpperCase();
            });
        }
    };

    /**
     * Initialize when DOM is ready
     */
    $(document).ready(function() {
        if (typeof payopAdmin !== 'undefined') {
            PayOpAdmin.init();
        }
    });

})(jQuery);
