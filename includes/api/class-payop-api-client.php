<?php
/**
 * PayOp API Client
 *
 * @package PayOp\WooCommerce\API
 */

namespace PayOp\WooCommerce\API;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp API Client
 * 
 * Handles all communication with PayOp API using Direct Integration approach
 * Based on documented endpoints: https://api.payop.com
 */
class PayOp_API_Client {

    /**
     * API base URL - from documented endpoints
     */
    const API_BASE_URL = PAYOP_API_BASE_URL;

    /**
     * API credentials
     */
    private $public_key;
    private $secret_key;
    private $jwt_token;
    private $project_id;
    private $debug;

    /**
     * Constructor
     *
     * @param string $public_key PayOp public key
     * @param string $secret_key PayOp secret key
     * @param string $jwt_token PayOp JWT token
     * @param string $project_id PayOp project ID
     * @param bool $debug Enable debug logging
     */
    public function __construct($public_key, $secret_key, $jwt_token, $project_id, $debug = false) {
        $this->public_key = $public_key;
        $this->secret_key = $secret_key;
        $this->jwt_token = $jwt_token;
        $this->project_id = $project_id;
        $this->debug = $debug;
    }

    /**
     * Get available payment methods
     * Endpoint: GET /v1/instrument-settings/payment-methods/available-for-application/{ID}
     *
     * @return array|WP_Error
     */
    public function get_payment_methods() {
        $endpoint = PAYOP_ENDPOINT_PAYMENT_METHODS . '/' . $this->project_id;
        
        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->jwt_token
        ];

        return $this->make_request('GET', $endpoint, [], $headers);
    }

    /**
     * Create invoice
     * Endpoint: POST /v1/invoices/create
     *
     * @param array $order_data Order data
     * @return array|WP_Error
     */
    public function create_invoice($order_data) {
        $endpoint = PAYOP_ENDPOINT_INVOICE_CREATE;
        
        // Generate signature using documented formula: SHA256(amount:currency:order_id:secret_key)
        $signature = $this->generate_signature(
            $order_data['order']['amount'],
            $order_data['order']['currency'],
            $order_data['order']['id'],
            $this->secret_key
        );

        $data = array_merge($order_data, [
            'publicKey' => $this->public_key,
            'signature' => $signature
        ]);

        $headers = [
            'Content-Type' => 'application/json'
        ];

        return $this->make_request('POST', $endpoint, $data, $headers);
    }

    /**
     * Get invoice information
     * Endpoint: GET /v1/invoices/{invoiceID}
     *
     * @param string $invoice_id Invoice ID
     * @return array|WP_Error
     */
    public function get_invoice_info($invoice_id) {
        $endpoint = PAYOP_ENDPOINT_INVOICE_GET . '/' . $invoice_id;
        
        $headers = [
            'Content-Type' => 'application/json'
        ];

        return $this->make_request('GET', $endpoint, [], $headers);
    }

    /**
     * Create checkout transaction
     * Endpoint: POST /v1/checkout/create
     *
     * @param string $invoice_id Invoice ID
     * @param array $customer_data Customer data
     * @return array|WP_Error
     */
    public function create_checkout($invoice_id, $customer_data) {
        $endpoint = PAYOP_ENDPOINT_CHECKOUT_CREATE;
        
        $data = array_merge($customer_data, [
            'invoiceIdentifier' => $invoice_id
        ]);

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->jwt_token
        ];

        return $this->make_request('POST', $endpoint, $data, $headers);
    }

    /**
     * Check invoice status
     * Endpoint: GET /v1/checkout/check-invoice-status/{invoiceID}
     *
     * @param string $invoice_id Invoice ID
     * @return array|WP_Error
     */
    public function check_invoice_status($invoice_id) {
        $endpoint = PAYOP_ENDPOINT_CHECKOUT_STATUS . '/' . $invoice_id;
        
        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->jwt_token
        ];

        return $this->make_request('GET', $endpoint, [], $headers);
    }

    /**
     * Get transaction details
     * Endpoint: GET /v2/transactions/{transactionID}
     *
     * @param string $transaction_id Transaction ID
     * @return array|WP_Error
     */
    public function get_transaction_details($transaction_id) {
        $endpoint = PAYOP_ENDPOINT_TRANSACTION_GET . '/' . $transaction_id;
        
        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->jwt_token
        ];

        return $this->make_request('GET', $endpoint, [], $headers);
    }

    /**
     * Void transaction
     * Endpoint: POST /v1/checkout/void
     *
     * @param string $invoice_id Invoice ID
     * @return array|WP_Error
     */
    public function void_transaction($invoice_id) {
        $endpoint = PAYOP_ENDPOINT_CHECKOUT_VOID;
        
        $data = [
            'invoiceIdentifier' => $invoice_id
        ];

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->jwt_token
        ];

        return $this->make_request('POST', $endpoint, $data, $headers);
    }

    /**
     * Generate PayOp signature
     * Formula: SHA256(amount:currency:order_id:secret_key)
     *
     * @param string $amount Order amount
     * @param string $currency Currency code
     * @param string $order_id Order ID
     * @param string $secret_key Secret key
     * @return string
     */
    private function generate_signature($amount, $currency, $order_id, $secret_key) {
        $data = [$amount, $currency, $order_id, $secret_key];
        return hash('sha256', implode(':', $data));
    }

    /**
     * Make HTTP request to PayOp API
     *
     * @param string $method HTTP method
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @param array $headers Request headers
     * @return array|WP_Error
     */
    private function make_request($method, $endpoint, $data = [], $headers = []) {
        $url = self::API_BASE_URL . $endpoint;

        $args = [
            'method' => $method,
            'headers' => $headers,
            'timeout' => 30,
            'user-agent' => 'PayOp-WooCommerce/' . PAYOP_WC_VERSION,
            'sslverify' => true,
            'redirection' => 0
        ];

        if (!empty($data) && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            $args['body'] = wp_json_encode($data);
        }

        $this->log('API Request: ' . $method . ' ' . $url);
        if ($this->debug && !empty($data)) {
            $this->log('Request Data: ' . wp_json_encode($data));
        }

        $response = wp_remote_request($url, $args);

        if (is_wp_error($response)) {
            $this->log('API Error: ' . $response->get_error_message());
            return $this->handle_request_error($response);
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        $this->log('API Response Code: ' . $response_code);
        if ($this->debug) {
            $this->log('API Response Body: ' . $response_body);
        }

        $decoded_response = json_decode($response_body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $error_message = 'Invalid JSON response: ' . json_last_error_msg();
            $this->log('JSON Error: ' . $error_message);
            return new \WP_Error('json_error', $error_message);
        }

        // Handle API errors based on documented status codes
        if ($response_code >= 400) {
            return $this->handle_api_error($response_code, $decoded_response);
        }

        return $decoded_response;
    }

    /**
     * Handle request errors (network, timeout, etc.)
     *
     * @param WP_Error $error
     * @return WP_Error
     */
    private function handle_request_error($error) {
        $error_code = $error->get_error_code();
        $error_message = $error->get_error_message();

        switch ($error_code) {
            case 'http_request_timeout':
                return new \WP_Error('timeout', __('PayOp API request timeout. Please try again.', 'payop-woocommerce'));

            case 'http_request_failed':
                return new \WP_Error('network_error', __('Network error connecting to PayOp API.', 'payop-woocommerce'));

            default:
                return new \WP_Error('request_error', sprintf(__('PayOp API request failed: %s', 'payop-woocommerce'), $error_message));
        }
    }

    /**
     * Handle API errors based on documented response codes
     *
     * @param int $response_code
     * @param array $response_data
     * @return WP_Error
     */
    private function handle_api_error($response_code, $response_data) {
        $error_message = isset($response_data['message']) ? $response_data['message'] : '';

        switch ($response_code) {
            case 401:
                $this->log('API Error 401: Unauthorized - Invalid credentials');
                return new \WP_Error('unauthorized', __('Invalid PayOp API credentials. Please check your settings.', 'payop-woocommerce'));

            case 403:
                $this->log('API Error 403: Forbidden - Access denied');
                return new \WP_Error('forbidden', __('Access denied to PayOp API. Please check your permissions.', 'payop-woocommerce'));

            case 404:
                $this->log('API Error 404: Not Found - ' . $error_message);
                return new \WP_Error('not_found', __('PayOp API resource not found.', 'payop-woocommerce'));

            case 422:
                $this->log('API Error 422: Unprocessable Entity - ' . $error_message);
                if (isset($response_data['message']) && is_array($response_data['message'])) {
                    // Handle validation errors
                    $validation_errors = [];
                    foreach ($response_data['message'] as $field => $errors) {
                        if (is_array($errors)) {
                            $validation_errors[] = $field . ': ' . implode(', ', $errors);
                        }
                    }
                    $error_message = implode('; ', $validation_errors);
                }
                return new \WP_Error('validation_error', sprintf(__('PayOp API validation error: %s', 'payop-woocommerce'), $error_message));

            case 429:
                $this->log('API Error 429: Rate limit exceeded');
                return new \WP_Error('rate_limit', __('PayOp API rate limit exceeded. Please try again later.', 'payop-woocommerce'));

            case 500:
            case 502:
            case 503:
            case 504:
                $this->log('API Error ' . $response_code . ': Server error');
                return new \WP_Error('server_error', __('PayOp API server error. Please try again later.', 'payop-woocommerce'));

            default:
                $this->log('API Error ' . $response_code . ': ' . $error_message);
                return new \WP_Error('api_error', sprintf(__('PayOp API error (%d): %s', 'payop-woocommerce'), $response_code, $error_message));
        }
    }

    /**
     * Log messages
     *
     * @param string $message
     */
    private function log($message) {
        if ($this->debug) {
            $logger = wc_get_logger();
            $logger->info($message, ['source' => 'payop-api']);
        }
    }
}
