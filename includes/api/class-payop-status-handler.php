<?php
/**
 * PayOp Status Handler
 *
 * @package PayOp\WooCommerce\API
 */

namespace PayOp\WooCommerce\API;

use PayOp\WooCommerce\Utils\PayOp_Config;
use PayOp\WooCommerce\Utils\PayOp_Security;
use PayOp\WooCommerce\Utils\PayOp_Error_Handler;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Status Handler
 * 
 * Handles payment status checks and redirects from PayOp
 */
class PayOp_Status_Handler {

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', [$this, 'handle_status_check']);
        add_action('wp_ajax_payop_check_payment_status', [$this, 'ajax_check_payment_status']);
        add_action('wp_ajax_nopriv_payop_check_payment_status', [$this, 'ajax_check_payment_status']);
    }

    /**
     * Handle status check requests
     */
    public function handle_status_check() {
        // Check if this is a PayOp status request
        if (!isset($_GET['payop_status']) || !isset($_GET['order_id'])) {
            return;
        }

        $order_id = intval($_GET['order_id']);
        $order = wc_get_order($order_id);

        if (!$order) {
            wp_die(__('Order not found.', 'payop-woocommerce'));
        }

        // Validate order ownership for logged-in users
        if (is_user_logged_in()) {
            $current_user_id = get_current_user_id();
            $order_user_id = $order->get_user_id();
            
            if ($current_user_id !== $order_user_id && !current_user_can('manage_woocommerce')) {
                wp_die(__('Access denied.', 'payop-woocommerce'));
            }
        } else {
            // For guest users, validate using order key
            $order_key = $_GET['key'] ?? '';
            if ($order_key !== $order->get_order_key()) {
                wp_die(__('Invalid order key.', 'payop-woocommerce'));
            }
        }

        // Check payment status
        $this->check_and_update_payment_status($order);

        // Redirect based on order status
        $this->redirect_based_on_status($order);
    }

    /**
     * AJAX check payment status
     */
    public function ajax_check_payment_status() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'payop_status_check')) {
            wp_send_json_error(['message' => __('Security check failed', 'payop-woocommerce')]);
        }

        $order_id = intval($_POST['order_id'] ?? 0);
        $order = wc_get_order($order_id);

        if (!$order) {
            wp_send_json_error(['message' => __('Order not found', 'payop-woocommerce')]);
        }

        // Check payment status
        $status_updated = $this->check_and_update_payment_status($order);

        wp_send_json_success([
            'status' => $order->get_status(),
            'status_updated' => $status_updated,
            'redirect_url' => $this->get_redirect_url($order)
        ]);
    }

    /**
     * Check and update payment status
     *
     * @param WC_Order $order Order object
     * @return bool True if status was updated
     */
    private function check_and_update_payment_status($order) {
        $invoice_id = $order->get_meta('_payop_invoice_id');
        
        if (empty($invoice_id)) {
            PayOp_Error_Handler::log_error('No invoice ID found for order', PayOp_Error_Handler::ERROR_TYPE_PAYMENT, [
                'order_id' => $order->get_id()
            ]);
            return false;
        }

        // Get API client
        $credentials = PayOp_Config::get_api_credentials();
        
        if (!class_exists('PayOp\\WooCommerce\\API\\PayOp_API_Client')) {
            require_once PAYOP_WC_PLUGIN_DIR . 'includes/api/class-payop-api-client.php';
        }

        $api_client = new PayOp_API_Client(
            $credentials['public_key'],
            $credentials['secret_key'],
            $credentials['jwt_token'],
            $credentials['project_id']
        );

        // Check invoice status
        $invoice_status = $api_client->get_invoice_info($invoice_id);
        
        if (is_wp_error($invoice_status)) {
            PayOp_Error_Handler::log_error('Failed to check invoice status: ' . $invoice_status->get_error_message(), PayOp_Error_Handler::ERROR_TYPE_API, [
                'order_id' => $order->get_id(),
                'invoice_id' => $invoice_id
            ]);
            return false;
        }

        $current_status = $order->get_status();
        $payment_status = $invoice_status['data']['status'] ?? '';

        // Update order status based on payment status
        $status_updated = false;
        
        switch ($payment_status) {
            case 'success':
            case 'completed':
                if (!in_array($current_status, ['completed', 'processing'])) {
                    $transaction_id = $invoice_status['data']['txid'] ?? '';
                    $order->payment_complete($transaction_id);
                    $order->add_order_note(__('Payment confirmed via status check', 'payop-woocommerce'));
                    $status_updated = true;
                }
                break;

            case 'failed':
                if ($current_status !== 'failed') {
                    $order->update_status('failed', __('Payment failed (confirmed via status check)', 'payop-woocommerce'));
                    $status_updated = true;
                }
                break;

            case 'cancelled':
                if ($current_status !== 'cancelled') {
                    $order->update_status('cancelled', __('Payment cancelled (confirmed via status check)', 'payop-woocommerce'));
                    $status_updated = true;
                }
                break;

            case 'pending':
            case 'processing':
                if ($current_status !== 'pending') {
                    $order->update_status('pending', __('Payment pending (confirmed via status check)', 'payop-woocommerce'));
                    $status_updated = true;
                }
                break;
        }

        // Update transaction record
        if ($status_updated) {
            $this->update_transaction_status($order, $payment_status, $invoice_status['data']);
        }

        return $status_updated;
    }

    /**
     * Update transaction status in database
     *
     * @param WC_Order $order Order object
     * @param string $status Payment status
     * @param array $status_data Status data from API
     */
    private function update_transaction_status($order, $status, $status_data) {
        global $wpdb;

        $transaction_id = $status_data['txid'] ?? '';

        $wpdb->update(
            $wpdb->prefix . 'payop_transactions',
            [
                'transaction_id' => $transaction_id,
                'status' => $status,
                'updated_at' => current_time('mysql')
            ],
            ['order_id' => $order->get_id()],
            ['%s', '%s', '%s'],
            ['%d']
        );

        // Update order meta
        if (!empty($transaction_id)) {
            $order->update_meta_data('_payop_transaction_id', $transaction_id);
        }
        $order->update_meta_data('_payop_status_data', $status_data);
        $order->save();
    }

    /**
     * Redirect based on order status
     *
     * @param WC_Order $order Order object
     */
    private function redirect_based_on_status($order) {
        $redirect_url = $this->get_redirect_url($order);
        
        if ($redirect_url) {
            wp_safe_redirect($redirect_url);
            exit;
        }
    }

    /**
     * Get redirect URL based on order status
     *
     * @param WC_Order $order Order object
     * @return string Redirect URL
     */
    private function get_redirect_url($order) {
        $status = $order->get_status();

        switch ($status) {
            case 'completed':
            case 'processing':
                // Redirect to thank you page
                return $order->get_checkout_order_received_url();

            case 'failed':
                // Redirect to checkout with error message
                wc_add_notice(__('Payment failed. Please try again.', 'payop-woocommerce'), 'error');
                return wc_get_checkout_url();

            case 'cancelled':
                // Redirect to cart
                wc_add_notice(__('Payment was cancelled.', 'payop-woocommerce'), 'notice');
                return wc_get_cart_url();

            case 'pending':
            default:
                // Redirect to order pay page
                return $order->get_checkout_payment_url();
        }
    }

    /**
     * Get payment status polling JavaScript
     *
     * @param WC_Order $order Order object
     * @return string JavaScript code
     */
    public function get_status_polling_script($order) {
        $script = "
        <script>
        (function($) {
            var pollCount = 0;
            var maxPolls = 60; // Poll for 5 minutes (60 * 5 seconds)
            
            function pollPaymentStatus() {
                if (pollCount >= maxPolls) {
                    console.log('PayOp: Polling timeout reached');
                    return;
                }
                
                $.ajax({
                    url: '" . admin_url('admin-ajax.php') . "',
                    type: 'POST',
                    data: {
                        action: 'payop_check_payment_status',
                        order_id: " . $order->get_id() . ",
                        nonce: '" . wp_create_nonce('payop_status_check') . "'
                    },
                    success: function(response) {
                        if (response.success) {
                            var data = response.data;
                            
                            if (data.status_updated && data.redirect_url) {
                                window.location.href = data.redirect_url;
                                return;
                            }
                            
                            // Continue polling if status is still pending
                            if (data.status === 'pending') {
                                pollCount++;
                                setTimeout(pollPaymentStatus, 5000); // Poll every 5 seconds
                            }
                        }
                    },
                    error: function() {
                        console.log('PayOp: Status check failed');
                        pollCount++;
                        if (pollCount < maxPolls) {
                            setTimeout(pollPaymentStatus, 10000); // Retry after 10 seconds on error
                        }
                    }
                });
            }
            
            // Start polling after 3 seconds
            setTimeout(pollPaymentStatus, 3000);
            
        })(jQuery);
        </script>";

        return $script;
    }

    /**
     * Add status check endpoint to WooCommerce API
     */
    public function add_status_endpoint() {
        add_rewrite_rule(
            '^payop-status/?$',
            'index.php?payop_status=1',
            'top'
        );

        add_filter('query_vars', function($vars) {
            $vars[] = 'payop_status';
            return $vars;
        });
    }

    /**
     * Handle webhook verification
     *
     * @param array $webhook_data Webhook data
     * @return bool True if webhook is valid
     */
    public function verify_webhook($webhook_data) {
        // Validate webhook signature
        $signature = $_SERVER['HTTP_X_PAYOP_SIGNATURE'] ?? '';
        
        if (empty($signature)) {
            PayOp_Security::log_security_event('missing_webhook_signature', [
                'headers' => getallheaders()
            ]);
            return false;
        }

        $credentials = PayOp_Config::get_api_credentials();
        $expected_signature = hash_hmac('sha256', wp_json_encode($webhook_data), $credentials['secret_key']);

        if (!hash_equals($expected_signature, $signature)) {
            PayOp_Security::log_security_event('invalid_webhook_signature', [
                'received_signature' => $signature,
                'expected_signature' => $expected_signature
            ]);
            return false;
        }

        return true;
    }
}
