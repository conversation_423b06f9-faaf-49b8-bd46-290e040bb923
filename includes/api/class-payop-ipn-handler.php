<?php
/**
 * PayOp IPN Handler
 *
 * @package PayOp\WooCommerce\API
 */

namespace PayOp\WooCommerce\API;

use PayOp\WooCommerce\Utils\PayOp_Config;
use PayOp\WooCommerce\Utils\PayOp_Security;
use PayOp\WooCommerce\Utils\PayOp_Error_Handler;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp IPN Handler
 * 
 * Handles Instant Payment Notifications from PayOp
 * Based on documented IPN security requirements and payload structure
 */
class PayOp_IPN_Handler {

    /**
     * IPN status constants
     */
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_PENDING = 'pending';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';

    /**
     * Process IPN request
     */
    public function process_ipn() {
        // Get raw POST data
        $raw_data = file_get_contents('php://input');
        
        if (empty($raw_data)) {
            PayOp_Error_Handler::log_error('Empty IPN data received', PayOp_Error_Handler::ERROR_TYPE_SECURITY);
            $this->send_response(400, 'Empty data');
            return;
        }

        // Decode JSON data
        $ipn_data = json_decode($raw_data, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            PayOp_Error_Handler::log_error('Invalid JSON in IPN data', PayOp_Error_Handler::ERROR_TYPE_SECURITY, [
                'raw_data' => substr($raw_data, 0, 500),
                'json_error' => json_last_error_msg()
            ]);
            $this->send_response(400, 'Invalid JSON');
            return;
        }

        // Log IPN received
        $this->log('IPN received: ' . wp_json_encode($ipn_data));

        // Validate IPN security
        if (!$this->validate_ipn_security($ipn_data)) {
            $this->send_response(403, 'Security validation failed');
            return;
        }

        // Process the IPN
        $result = $this->process_ipn_data($ipn_data);
        
        if ($result['success']) {
            $this->send_response(200, 'OK');
        } else {
            $this->send_response(400, $result['message']);
        }
    }

    /**
     * Validate IPN security
     *
     * @param array $ipn_data IPN data
     * @return bool
     */
    private function validate_ipn_security($ipn_data) {
        // Validate IP address
        $client_ip = PayOp_Security::get_client_ip();
        if (!PayOp_Security::validate_ipn_ip($client_ip)) {
            PayOp_Security::log_security_event('invalid_ipn_ip', [
                'ip' => $client_ip,
                'allowed_ips' => PAYOP_IPN_ALLOWED_IPS
            ]);
            return false;
        }

        // Validate timestamp to prevent replay attacks
        if (isset($ipn_data['timestamp'])) {
            if (!PayOp_Security::validate_webhook_timestamp($ipn_data['timestamp'])) {
                PayOp_Security::log_security_event('ipn_timestamp_invalid', [
                    'timestamp' => $ipn_data['timestamp'],
                    'current_time' => time()
                ]);
                return false;
            }
        }

        // Validate signature
        if (isset($ipn_data['signature'])) {
            $credentials = PayOp_Config::get_api_credentials();
            $signature = $ipn_data['signature'];
            unset($ipn_data['signature']); // Remove signature from data for validation

            if (!PayOp_Security::validate_ipn_signature($ipn_data, $signature, $credentials['secret_key'])) {
                PayOp_Security::log_security_event('invalid_ipn_signature', [
                    'received_signature' => $signature,
                    'data_keys' => array_keys($ipn_data)
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * Process IPN data
     *
     * @param array $ipn_data IPN data
     * @return array Processing result
     */
    private function process_ipn_data($ipn_data) {
        try {
            // Extract required fields
            $invoice_id = $ipn_data['invoiceId'] ?? '';
            $transaction_id = $ipn_data['txid'] ?? '';
            $status = $ipn_data['status'] ?? '';
            $amount = $ipn_data['amount'] ?? 0;
            $currency = $ipn_data['currency'] ?? '';

            if (empty($invoice_id)) {
                throw new \Exception('Missing invoice ID in IPN data');
            }

            // Find order by invoice ID
            $order = $this->get_order_by_invoice_id($invoice_id);
            if (!$order) {
                throw new \Exception('Order not found for invoice ID: ' . $invoice_id);
            }

            $this->log('Processing IPN for order: ' . $order->get_id() . ', status: ' . $status);

            // Validate amount and currency
            if (!$this->validate_ipn_amount($order, $amount, $currency)) {
                throw new \Exception('Amount or currency mismatch');
            }

            // Update transaction record
            $this->update_transaction_record($order, $transaction_id, $status, $ipn_data);

            // Process based on status
            switch ($status) {
                case self::STATUS_SUCCESS:
                    $this->process_successful_payment($order, $transaction_id, $ipn_data);
                    break;

                case self::STATUS_FAILED:
                    $this->process_failed_payment($order, $transaction_id, $ipn_data);
                    break;

                case self::STATUS_PENDING:
                    $this->process_pending_payment($order, $transaction_id, $ipn_data);
                    break;

                case self::STATUS_CANCELLED:
                    $this->process_cancelled_payment($order, $transaction_id, $ipn_data);
                    break;

                case self::STATUS_REFUNDED:
                    $this->process_refunded_payment($order, $transaction_id, $ipn_data);
                    break;

                default:
                    throw new \Exception('Unknown payment status: ' . $status);
            }

            return ['success' => true, 'message' => 'IPN processed successfully'];

        } catch (\Exception $e) {
            PayOp_Error_Handler::log_error('IPN processing error: ' . $e->getMessage(), PayOp_Error_Handler::ERROR_TYPE_PAYMENT, [
                'ipn_data' => $ipn_data
            ]);
            
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Get order by invoice ID
     *
     * @param string $invoice_id Invoice ID
     * @return WC_Order|null
     */
    private function get_order_by_invoice_id($invoice_id) {
        global $wpdb;

        // First try to find in transaction table
        $order_id = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT order_id FROM {$wpdb->prefix}payop_transactions WHERE invoice_id = %s",
                $invoice_id
            )
        );

        if ($order_id) {
            return wc_get_order($order_id);
        }

        // Fallback: search in order meta
        $orders = wc_get_orders([
            'meta_key' => '_payop_invoice_id',
            'meta_value' => $invoice_id,
            'limit' => 1
        ]);

        return !empty($orders) ? $orders[0] : null;
    }

    /**
     * Validate IPN amount and currency
     *
     * @param WC_Order $order Order object
     * @param float $ipn_amount IPN amount
     * @param string $ipn_currency IPN currency
     * @return bool
     */
    private function validate_ipn_amount($order, $ipn_amount, $ipn_currency) {
        $order_amount = floatval($order->get_total());
        $order_currency = $order->get_currency();

        // Allow small rounding differences (0.01)
        $amount_diff = abs($order_amount - floatval($ipn_amount));
        
        if ($amount_diff > 0.01) {
            PayOp_Security::log_security_event('ipn_amount_mismatch', [
                'order_id' => $order->get_id(),
                'order_amount' => $order_amount,
                'ipn_amount' => $ipn_amount,
                'difference' => $amount_diff
            ]);
            return false;
        }

        if ($order_currency !== $ipn_currency) {
            PayOp_Security::log_security_event('ipn_currency_mismatch', [
                'order_id' => $order->get_id(),
                'order_currency' => $order_currency,
                'ipn_currency' => $ipn_currency
            ]);
            return false;
        }

        return true;
    }

    /**
     * Update transaction record
     *
     * @param WC_Order $order Order object
     * @param string $transaction_id Transaction ID
     * @param string $status Payment status
     * @param array $ipn_data Full IPN data
     */
    private function update_transaction_record($order, $transaction_id, $status, $ipn_data) {
        global $wpdb;

        $wpdb->update(
            $wpdb->prefix . 'payop_transactions',
            [
                'transaction_id' => $transaction_id,
                'status' => $status,
                'updated_at' => current_time('mysql')
            ],
            ['order_id' => $order->get_id()],
            ['%s', '%s', '%s'],
            ['%d']
        );

        // Store IPN data in order meta
        $order->update_meta_data('_payop_ipn_data', $ipn_data);
        $order->update_meta_data('_payop_transaction_id', $transaction_id);
        $order->save();
    }

    /**
     * Process successful payment
     *
     * @param WC_Order $order Order object
     * @param string $transaction_id Transaction ID
     * @param array $ipn_data IPN data
     */
    private function process_successful_payment($order, $transaction_id, $ipn_data) {
        if ($order->get_status() === 'completed') {
            $this->log('Order already completed: ' . $order->get_id());
            return;
        }

        // Mark order as completed
        $order->payment_complete($transaction_id);
        
        $order->add_order_note(
            sprintf(
                __('PayOp payment completed. Transaction ID: %s', 'payop-woocommerce'),
                $transaction_id
            )
        );

        $this->log('Payment completed for order: ' . $order->get_id());

        // Trigger action for other plugins
        do_action('payop_payment_completed', $order, $transaction_id, $ipn_data);
    }

    /**
     * Process failed payment
     *
     * @param WC_Order $order Order object
     * @param string $transaction_id Transaction ID
     * @param array $ipn_data IPN data
     */
    private function process_failed_payment($order, $transaction_id, $ipn_data) {
        $order->update_status('failed', __('PayOp payment failed', 'payop-woocommerce'));
        
        $failure_reason = $ipn_data['failureReason'] ?? __('Payment failed', 'payop-woocommerce');
        $order->add_order_note(
            sprintf(
                __('PayOp payment failed. Reason: %s. Transaction ID: %s', 'payop-woocommerce'),
                $failure_reason,
                $transaction_id
            )
        );

        $this->log('Payment failed for order: ' . $order->get_id() . ', reason: ' . $failure_reason);

        // Trigger action for other plugins
        do_action('payop_payment_failed', $order, $transaction_id, $ipn_data);
    }

    /**
     * Process pending payment
     *
     * @param WC_Order $order Order object
     * @param string $transaction_id Transaction ID
     * @param array $ipn_data IPN data
     */
    private function process_pending_payment($order, $transaction_id, $ipn_data) {
        if ($order->get_status() !== 'pending') {
            $order->update_status('pending', __('PayOp payment pending', 'payop-woocommerce'));
        }
        
        $order->add_order_note(
            sprintf(
                __('PayOp payment is pending. Transaction ID: %s', 'payop-woocommerce'),
                $transaction_id
            )
        );

        $this->log('Payment pending for order: ' . $order->get_id());

        // Trigger action for other plugins
        do_action('payop_payment_pending', $order, $transaction_id, $ipn_data);
    }

    /**
     * Process cancelled payment
     *
     * @param WC_Order $order Order object
     * @param string $transaction_id Transaction ID
     * @param array $ipn_data IPN data
     */
    private function process_cancelled_payment($order, $transaction_id, $ipn_data) {
        $order->update_status('cancelled', __('PayOp payment cancelled', 'payop-woocommerce'));
        
        $order->add_order_note(
            sprintf(
                __('PayOp payment was cancelled. Transaction ID: %s', 'payop-woocommerce'),
                $transaction_id
            )
        );

        $this->log('Payment cancelled for order: ' . $order->get_id());

        // Trigger action for other plugins
        do_action('payop_payment_cancelled', $order, $transaction_id, $ipn_data);
    }

    /**
     * Process refunded payment
     *
     * @param WC_Order $order Order object
     * @param string $transaction_id Transaction ID
     * @param array $ipn_data IPN data
     */
    private function process_refunded_payment($order, $transaction_id, $ipn_data) {
        $refund_amount = floatval($ipn_data['refundAmount'] ?? $order->get_total());
        
        // Create WooCommerce refund
        $refund = wc_create_refund([
            'order_id' => $order->get_id(),
            'amount' => $refund_amount,
            'reason' => __('PayOp refund', 'payop-woocommerce')
        ]);

        if (is_wp_error($refund)) {
            PayOp_Error_Handler::log_error('Failed to create refund: ' . $refund->get_error_message(), PayOp_Error_Handler::ERROR_TYPE_PAYMENT, [
                'order_id' => $order->get_id(),
                'refund_amount' => $refund_amount
            ]);
        } else {
            $order->add_order_note(
                sprintf(
                    __('PayOp refund processed. Amount: %s. Transaction ID: %s', 'payop-woocommerce'),
                    wc_price($refund_amount),
                    $transaction_id
                )
            );

            $this->log('Refund processed for order: ' . $order->get_id() . ', amount: ' . $refund_amount);
        }

        // Trigger action for other plugins
        do_action('payop_payment_refunded', $order, $transaction_id, $ipn_data, $refund_amount);
    }

    /**
     * Send HTTP response
     *
     * @param int $code HTTP status code
     * @param string $message Response message
     */
    private function send_response($code, $message) {
        status_header($code);
        echo esc_html($message);
        exit;
    }

    /**
     * Log IPN events
     *
     * @param string $message Log message
     */
    private function log($message) {
        $debug_settings = PayOp_Config::get_debug_settings();
        if ($debug_settings['enabled'] && $debug_settings['log_ipn']) {
            $logger = wc_get_logger();
            $logger->info($message, ['source' => 'payop-ipn']);
        }
    }
}
