<?php
/**
 * PayOp Payment Methods Manager
 *
 * @package PayOp\WooCommerce\API
 */

namespace PayOp\WooCommerce\API;

use PayOp\WooCommerce\Utils\PayOp_Config;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Payment Methods Manager
 * 
 * Handles fetching, caching, and filtering of 122+ PayOp payment methods
 * Based on factual API analysis showing method distribution and requirements
 */
class PayOp_Payment_Methods {

    /**
     * API client instance
     *
     * @var PayOp_API_Client
     */
    private $api_client;

    /**
     * Cache key prefix
     */
    const CACHE_PREFIX = 'payop_payment_methods_';

    /**
     * Default cache TTL (1 hour)
     */
    const DEFAULT_CACHE_TTL = 3600;

    /**
     * Constructor
     *
     * @param PayOp_API_Client $api_client
     */
    public function __construct($api_client) {
        $this->api_client = $api_client;
    }

    /**
     * Get all available payment methods
     * Fetches from cache first, then API if needed
     *
     * @param bool $force_refresh Force refresh from API
     * @return array|WP_Error
     */
    public function get_payment_methods($force_refresh = false) {
        $cache_key = self::CACHE_PREFIX . 'all';
        
        if (!$force_refresh) {
            $cached_methods = $this->get_from_cache($cache_key);
            if ($cached_methods !== false) {
                $this->log('Payment methods loaded from cache');
                return $cached_methods;
            }
        }

        $this->log('Fetching payment methods from API');
        $response = $this->api_client->get_payment_methods();

        if (is_wp_error($response)) {
            $this->log('Error fetching payment methods: ' . $response->get_error_message());
            return $response;
        }

        if (!isset($response['data']) || !is_array($response['data'])) {
            $error_message = 'Invalid payment methods response format';
            $this->log($error_message);
            return new \WP_Error('invalid_response', $error_message);
        }

        $payment_methods = $response['data'];
        $this->log('Fetched ' . count($payment_methods) . ' payment methods from API');

        // Cache the results
        $this->save_to_cache($cache_key, $payment_methods);
        $this->save_to_database($payment_methods);

        return $payment_methods;
    }

    /**
     * Filter payment methods by currency
     *
     * @param string $currency Currency code (EUR, USD, etc.)
     * @param bool $force_refresh Force refresh from API
     * @return array|WP_Error
     */
    public function filter_by_currency($currency, $force_refresh = false) {
        $cache_key = self::CACHE_PREFIX . 'currency_' . strtoupper($currency);
        
        if (!$force_refresh) {
            $cached_methods = $this->get_from_cache($cache_key);
            if ($cached_methods !== false) {
                return $cached_methods;
            }
        }

        $all_methods = $this->get_payment_methods($force_refresh);
        
        if (is_wp_error($all_methods)) {
            return $all_methods;
        }

        $filtered_methods = array_filter($all_methods, function($method) use ($currency) {
            return in_array(strtoupper($currency), array_map('strtoupper', $method['currencies']));
        });

        $this->save_to_cache($cache_key, $filtered_methods);
        
        return array_values($filtered_methods);
    }

    /**
     * Filter payment methods by country
     *
     * @param string $country Country code (US, GB, etc.)
     * @param bool $force_refresh Force refresh from API
     * @return array|WP_Error
     */
    public function filter_by_country($country, $force_refresh = false) {
        $cache_key = self::CACHE_PREFIX . 'country_' . strtoupper($country);
        
        if (!$force_refresh) {
            $cached_methods = $this->get_from_cache($cache_key);
            if ($cached_methods !== false) {
                return $cached_methods;
            }
        }

        $all_methods = $this->get_payment_methods($force_refresh);
        
        if (is_wp_error($all_methods)) {
            return $all_methods;
        }

        $filtered_methods = array_filter($all_methods, function($method) use ($country) {
            return in_array(strtoupper($country), array_map('strtoupper', $method['countries']));
        });

        $this->save_to_cache($cache_key, $filtered_methods);
        
        return array_values($filtered_methods);
    }

    /**
     * Filter payment methods by currency and country
     *
     * @param string $currency Currency code
     * @param string $country Country code
     * @param bool $force_refresh Force refresh from API
     * @return array|WP_Error
     */
    public function filter_by_currency_and_country($currency, $country, $force_refresh = false) {
        $cache_key = self::CACHE_PREFIX . 'filter_' . strtoupper($currency) . '_' . strtoupper($country);
        
        if (!$force_refresh) {
            $cached_methods = $this->get_from_cache($cache_key);
            if ($cached_methods !== false) {
                return $cached_methods;
            }
        }

        $all_methods = $this->get_payment_methods($force_refresh);
        
        if (is_wp_error($all_methods)) {
            return $all_methods;
        }

        $filtered_methods = array_filter($all_methods, function($method) use ($currency, $country) {
            $currency_match = in_array(strtoupper($currency), array_map('strtoupper', $method['currencies']));
            $country_match = in_array(strtoupper($country), array_map('strtoupper', $method['countries']));
            return $currency_match && $country_match;
        });

        $this->save_to_cache($cache_key, $filtered_methods);
        
        return array_values($filtered_methods);
    }

    /**
     * Group payment methods by type
     * Based on factual analysis: bank_transfer (68), cash (42), ewallet (10), cards_international (1), crypto (1)
     *
     * @param array $methods Payment methods array
     * @return array
     */
    public function group_by_type($methods = null) {
        if ($methods === null) {
            $methods = $this->get_payment_methods();
            if (is_wp_error($methods)) {
                return $methods;
            }
        }

        $grouped = [];
        foreach ($methods as $method) {
            $type = $method['type'];
            if (!isset($grouped[$type])) {
                $grouped[$type] = [];
            }
            $grouped[$type][] = $method;
        }

        // Sort by priority based on analysis
        $type_priority = [
            'cards_international' => 1,
            'ewallet' => 2,
            'bank_transfer' => 3,
            'cash' => 4,
            'crypto' => 5
        ];

        uksort($grouped, function($a, $b) use ($type_priority) {
            $priority_a = $type_priority[$a] ?? 999;
            $priority_b = $type_priority[$b] ?? 999;
            return $priority_a - $priority_b;
        });

        return $grouped;
    }

    /**
     * Get payment method by ID
     *
     * @param int $method_id Payment method identifier
     * @return array|null
     */
    public function get_method_by_id($method_id) {
        $all_methods = $this->get_payment_methods();
        
        if (is_wp_error($all_methods)) {
            return null;
        }

        foreach ($all_methods as $method) {
            if ($method['identifier'] == $method_id) {
                return $method;
            }
        }

        return null;
    }

    /**
     * Get required fields for a payment method
     *
     * @param int $method_id Payment method identifier
     * @return array
     */
    public function get_method_fields($method_id) {
        $method = $this->get_method_by_id($method_id);
        
        if (!$method || !isset($method['config']['fields'])) {
            return [];
        }

        return $method['config']['fields'];
    }

    /**
     * Get payment method statistics
     * Based on factual analysis of 122 methods
     *
     * @return array
     */
    public function get_statistics() {
        $all_methods = $this->get_payment_methods();
        
        if (is_wp_error($all_methods)) {
            return [];
        }

        $stats = [
            'total_methods' => count($all_methods),
            'by_type' => [],
            'by_currency' => [],
            'field_requirements' => []
        ];

        // Count by type
        foreach ($all_methods as $method) {
            $type = $method['type'];
            $stats['by_type'][$type] = ($stats['by_type'][$type] ?? 0) + 1;

            // Count by currency
            foreach ($method['currencies'] as $currency) {
                $stats['by_currency'][$currency] = ($stats['by_currency'][$currency] ?? 0) + 1;
            }

            // Count field requirements
            if (isset($method['config']['fields'])) {
                foreach ($method['config']['fields'] as $field) {
                    if ($field['required'] ?? false) {
                        $field_name = $field['name'];
                        $stats['field_requirements'][$field_name] = ($stats['field_requirements'][$field_name] ?? 0) + 1;
                    }
                }
            }
        }

        return $stats;
    }

    /**
     * Clear all payment method caches
     *
     * @return bool
     */
    public function clear_cache() {
        global $wpdb;

        // Clear database cache
        $wpdb->query("DELETE FROM {$wpdb->prefix}payop_payment_methods");

        // Clear WordPress transients
        $cache_keys = [
            self::CACHE_PREFIX . 'all',
            // Add other cache keys as needed
        ];

        foreach ($cache_keys as $key) {
            delete_transient($key);
        }

        $this->log('Payment methods cache cleared');
        return true;
    }

    /**
     * Get data from cache
     *
     * @param string $cache_key
     * @return mixed|false
     */
    private function get_from_cache($cache_key) {
        return get_transient($cache_key);
    }

    /**
     * Save data to cache
     *
     * @param string $cache_key
     * @param mixed $data
     * @return bool
     */
    private function save_to_cache($cache_key, $data) {
        $cache_settings = PayOp_Config::get_cache_settings();
        $ttl = $cache_settings['ttl'] ?? self::DEFAULT_CACHE_TTL;
        
        return set_transient($cache_key, $data, $ttl);
    }

    /**
     * Save payment methods to database
     *
     * @param array $payment_methods
     * @return bool
     */
    private function save_to_database($payment_methods) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'payop_payment_methods';

        // Clear existing data
        $wpdb->query("DELETE FROM $table_name");

        // Insert new data
        foreach ($payment_methods as $method) {
            $wpdb->insert(
                $table_name,
                [
                    'method_id' => $method['identifier'],
                    'method_data' => wp_json_encode($method),
                    'last_updated' => current_time('mysql')
                ],
                ['%d', '%s', '%s']
            );
        }

        return true;
    }

    /**
     * Log messages
     *
     * @param string $message
     */
    private function log($message) {
        $debug_settings = PayOp_Config::get_debug_settings();
        if ($debug_settings['enabled']) {
            $logger = wc_get_logger();
            $logger->info($message, ['source' => 'payop-payment-methods']);
        }
    }
}
