<?php
/**
 * PayOp AJAX Handler
 *
 * @package PayOp\WooCommerce\API
 */

namespace PayOp\WooCommerce\API;

use PayOp\WooCommerce\Utils\PayOp_Config;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp AJAX Handler
 * 
 * Handles AJAX requests for payment methods and checkout operations
 */
class PayOp_AJAX_Handler {

    /**
     * Payment methods manager
     *
     * @var PayOp_Payment_Methods
     */
    private $payment_methods;

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
        $this->init_payment_methods();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // AJAX hooks for logged in and non-logged in users
        add_action('wp_ajax_payop_get_payment_methods', [$this, 'get_payment_methods']);
        add_action('wp_ajax_nopriv_payop_get_payment_methods', [$this, 'get_payment_methods']);
        
        add_action('wp_ajax_payop_get_method_fields', [$this, 'get_method_fields']);
        add_action('wp_ajax_nopriv_payop_get_method_fields', [$this, 'get_method_fields']);
        
        add_action('wp_ajax_payop_validate_field', [$this, 'validate_field']);
        add_action('wp_ajax_nopriv_payop_validate_field', [$this, 'validate_field']);
    }

    /**
     * Initialize payment methods manager
     */
    private function init_payment_methods() {
        $credentials = PayOp_Config::get_api_credentials();
        
        if (!class_exists('PayOp\\WooCommerce\\API\\PayOp_API_Client')) {
            require_once PAYOP_WC_PLUGIN_DIR . 'includes/api/class-payop-api-client.php';
        }
        
        if (!class_exists('PayOp\\WooCommerce\\API\\PayOp_Payment_Methods')) {
            require_once PAYOP_WC_PLUGIN_DIR . 'includes/api/class-payop-payment-methods.php';
        }

        $api_client = new PayOp_API_Client(
            $credentials['public_key'],
            $credentials['secret_key'],
            $credentials['jwt_token'],
            $credentials['project_id'],
            PayOp_Config::get('debug_enabled', false)
        );

        $this->payment_methods = new PayOp_Payment_Methods($api_client);
    }

    /**
     * Get payment methods via AJAX
     */
    public function get_payment_methods() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'payop_checkout')) {
            wp_send_json_error(['message' => __('Security check failed', 'payop-woocommerce')]);
        }

        $currency = sanitize_text_field($_POST['currency'] ?? '');
        $country = sanitize_text_field($_POST['country'] ?? '');
        $group_by_type = filter_var($_POST['group_by_type'] ?? true, FILTER_VALIDATE_BOOLEAN);

        try {
            // Get filtered payment methods
            if (!empty($currency) && !empty($country)) {
                $methods = $this->payment_methods->filter_by_currency_and_country($currency, $country);
            } elseif (!empty($currency)) {
                $methods = $this->payment_methods->filter_by_currency($currency);
            } elseif (!empty($country)) {
                $methods = $this->payment_methods->filter_by_country($country);
            } else {
                $methods = $this->payment_methods->get_payment_methods();
            }

            if (is_wp_error($methods)) {
                wp_send_json_error(['message' => $methods->get_error_message()]);
            }

            // Group by type if requested
            if ($group_by_type) {
                $methods = $this->payment_methods->group_by_type($methods);
            }

            // Format methods for frontend
            $formatted_methods = $this->format_methods_for_frontend($methods, $group_by_type);

            wp_send_json_success([
                'methods' => $formatted_methods,
                'total' => $group_by_type ? array_sum(array_map('count', $methods)) : count($methods),
                'grouped' => $group_by_type
            ]);

        } catch (Exception $e) {
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Get payment method fields via AJAX
     */
    public function get_method_fields() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'payop_checkout')) {
            wp_send_json_error(['message' => __('Security check failed', 'payop-woocommerce')]);
        }

        $method_id = intval($_POST['method_id'] ?? 0);
        $country = sanitize_text_field($_POST['country'] ?? '');

        if (empty($method_id)) {
            wp_send_json_error(['message' => __('Invalid payment method ID', 'payop-woocommerce')]);
        }

        try {
            // Get method configuration
            $method = $this->payment_methods->get_method_by_id($method_id);
            if (!$method) {
                wp_send_json_error(['message' => __('Payment method not found', 'payop-woocommerce')]);
            }

            // Load field manager
            if (!class_exists('PayOp\\WooCommerce\\Utils\\PayOp_Field_Manager')) {
                require_once PAYOP_WC_PLUGIN_DIR . 'includes/utils/class-payop-field-manager.php';
            }

            // Get processed fields with country-specific validation
            $fields = \PayOp\WooCommerce\Utils\PayOp_Field_Manager::get_required_fields($method, $country);

            wp_send_json_success([
                'fields' => $fields,
                'method_id' => $method_id,
                'method_title' => $method['title']
            ]);

        } catch (Exception $e) {
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Validate field via AJAX
     */
    public function validate_field() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'payop_checkout')) {
            wp_send_json_error(['message' => __('Security check failed', 'payop-woocommerce')]);
        }

        $field_name = sanitize_text_field($_POST['field_name'] ?? '');
        $field_value = sanitize_text_field($_POST['field_value'] ?? '');
        $country = sanitize_text_field($_POST['country'] ?? '');
        $field_config = json_decode(stripslashes($_POST['field_config'] ?? '{}'), true);

        try {
            // Load field manager
            if (!class_exists('PayOp\\WooCommerce\\Utils\\PayOp_Field_Manager')) {
                require_once PAYOP_WC_PLUGIN_DIR . 'includes/utils/class-payop-field-manager.php';
            }

            $validation_result = \PayOp\WooCommerce\Utils\PayOp_Field_Manager::validate_field(
                $field_name,
                $field_value,
                $field_config,
                $country
            );

            if ($validation_result['valid']) {
                wp_send_json_success($validation_result);
            } else {
                wp_send_json_error($validation_result);
            }

        } catch (Exception $e) {
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Format payment methods for frontend display
     *
     * @param array $methods Payment methods
     * @param bool $grouped Whether methods are grouped
     * @return array
     */
    private function format_methods_for_frontend($methods, $grouped = false) {
        if ($grouped) {
            $formatted = [];
            foreach ($methods as $type => $type_methods) {
                $formatted[$type] = [
                    'title' => $this->get_type_title($type),
                    'methods' => array_map([$this, 'format_single_method'], $type_methods)
                ];
            }
            return $formatted;
        } else {
            return array_map([$this, 'format_single_method'], $methods);
        }
    }

    /**
     * Format single payment method
     *
     * @param array $method Payment method data
     * @return array
     */
    private function format_single_method($method) {
        return [
            'id' => $method['identifier'],
            'title' => $method['title'],
            'type' => $method['type'],
            'logo' => $method['logo'] ?? '',
            'currencies' => $method['currencies'],
            'countries' => $method['countries'],
            'required_fields_count' => count(array_filter($method['config']['fields'] ?? [], function($field) {
                return $field['required'] ?? false;
            }))
        ];
    }

    /**
     * Format fields for frontend display
     *
     * @param array $fields Field configuration
     * @return array
     */
    private function format_fields_for_frontend($fields) {
        return array_map(function($field) {
            return [
                'name' => $field['name'],
                'type' => $field['type'],
                'title' => $field['title'] ?? ucfirst(str_replace('_', ' ', $field['name'])),
                'required' => $field['required'] ?? false,
                'pattern' => $field['regexp'] ?? '',
                'placeholder' => $this->get_field_placeholder($field),
                'description' => $this->get_field_description($field)
            ];
        }, $fields);
    }

    /**
     * Get payment method type title
     *
     * @param string $type Payment method type
     * @return string
     */
    private function get_type_title($type) {
        $titles = [
            'cards_international' => __('Credit/Debit Cards', 'payop-woocommerce'),
            'bank_transfer' => __('Bank Transfer', 'payop-woocommerce'),
            'ewallet' => __('E-Wallets', 'payop-woocommerce'),
            'cash' => __('Cash Payments', 'payop-woocommerce'),
            'crypto' => __('Cryptocurrency', 'payop-woocommerce')
        ];

        return $titles[$type] ?? ucfirst(str_replace('_', ' ', $type));
    }

    /**
     * Get field placeholder text
     *
     * @param array $field Field configuration
     * @return string
     */
    private function get_field_placeholder($field) {
        $placeholders = [
            'email' => __('Enter your email address', 'payop-woocommerce'),
            'name' => __('Enter your full name', 'payop-woocommerce'),
            'phone' => __('Enter your phone number', 'payop-woocommerce'),
            'document' => __('Enter your document number', 'payop-woocommerce'),
            'date_of_birth' => __('DD.MM.YYYY', 'payop-woocommerce'),
            'bank_code' => __('Select your bank', 'payop-woocommerce'),
            'bank_type' => __('Select transfer type', 'payop-woocommerce')
        ];

        return $placeholders[$field['name']] ?? '';
    }

    /**
     * Get field description text
     *
     * @param array $field Field configuration
     * @return string
     */
    private function get_field_description($field) {
        $descriptions = [
            'document' => __('Enter your government-issued document number', 'payop-woocommerce'),
            'date_of_birth' => __('Required for European bank transfers', 'payop-woocommerce'),
            'bank_code' => __('Select your bank from the list', 'payop-woocommerce'),
            'bank_type' => __('Choose the type of bank transfer', 'payop-woocommerce')
        ];

        return $descriptions[$field['name']] ?? '';
    }

    /**
     * Validate single field
     *
     * @param string $field_name Field name
     * @param string $field_value Field value
     * @param string $field_type Field type
     * @param string $field_pattern Validation pattern
     * @param bool $required Whether field is required
     * @return array
     */
    private function validate_single_field($field_name, $field_value, $field_type, $field_pattern, $required) {
        $result = ['valid' => true, 'message' => ''];

        // Check required fields
        if ($required && empty($field_value)) {
            return [
                'valid' => false,
                'message' => sprintf(__('%s is required', 'payop-woocommerce'), ucfirst(str_replace('_', ' ', $field_name)))
            ];
        }

        // Skip validation if field is empty and not required
        if (empty($field_value) && !$required) {
            return $result;
        }

        // Type-specific validation
        switch ($field_type) {
            case 'email':
                if (!is_email($field_value)) {
                    return ['valid' => false, 'message' => __('Please enter a valid email address', 'payop-woocommerce')];
                }
                break;

            case 'text':
                // Apply pattern validation if provided
                if (!empty($field_pattern) && !preg_match('/' . $field_pattern . '/', $field_value)) {
                    return ['valid' => false, 'message' => $this->get_pattern_error_message($field_name, $field_pattern)];
                }
                break;
        }

        return $result;
    }

    /**
     * Get pattern-specific error message
     *
     * @param string $field_name Field name
     * @param string $pattern Validation pattern
     * @return string
     */
    private function get_pattern_error_message($field_name, $pattern) {
        $messages = [
            'document' => __('Please enter a valid document number', 'payop-woocommerce'),
            'phone' => __('Please enter a valid phone number', 'payop-woocommerce'),
            'date_of_birth' => __('Please enter date in DD.MM.YYYY format', 'payop-woocommerce')
        ];

        return $messages[$field_name] ?? __('Invalid format', 'payop-woocommerce');
    }
}
