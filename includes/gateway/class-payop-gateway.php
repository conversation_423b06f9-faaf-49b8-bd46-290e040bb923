<?php
/**
 * PayOp WooCommerce Gateway Class
 *
 * @package PayOp\WooCommerce\Gateway
 */

namespace PayOp\WooCommerce\Gateway;

use WC_Payment_Gateway;
use WC_Order;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Payment Gateway
 * 
 * Implements Direct Integration approach - completely bypasses PayOp hosted checkout
 * Supports 122+ payment methods with dynamic field collection
 */
class PayOp_Gateway extends WC_Payment_Gateway {

    /**
     * Gateway ID
     */
    const GATEWAY_ID = 'payop';

    /**
     * API client instance
     *
     * @var \PayOp\WooCommerce\API\PayOp_API_Client
     */
    private $api_client;

    /**
     * Constructor
     */
    public function __construct() {
        $this->id = self::GATEWAY_ID;
        $this->icon = '';
        $this->has_fields = true;
        $this->method_title = __('PayOp Payment Gateway', 'payop-woocommerce');
        $this->method_description = __('Accept payments via PayOp with 122+ payment methods using Direct Integration', 'payop-woocommerce');

        // Supported features - One-time payments only, no subscriptions
        $this->supports = [
            'products'
            // Note: Refunds and withdrawals handled manually outside plugin
            // Note: No subscription support - one-time payments only
        ];

        // Load settings
        $this->init_form_fields();
        $this->init_settings();

        // Define user set variables
        $this->title = $this->get_option('title');
        $this->description = $this->get_option('description');
        $this->enabled = $this->get_option('enabled');
        $this->public_key = $this->get_option('public_key');
        $this->secret_key = $this->get_option('secret_key');
        $this->jwt_token = $this->get_option('jwt_token');
        $this->project_id = $this->get_option('project_id');
        $this->debug = 'yes' === $this->get_option('debug', 'no');

        // Initialize API client (only if credentials are available)
        if (!empty($this->public_key) && !empty($this->secret_key)) {
            $this->init_api_client();
        }

        // Initialize AJAX handler (only on frontend)
        if (!is_admin()) {
            $this->init_ajax_handler();
        }

        // Hooks
        add_action('woocommerce_update_options_payment_gateways_' . $this->id, [$this, 'process_admin_options']);
        add_action('woocommerce_api_payop_ipn', [$this, 'handle_ipn']);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_scripts']);
    }

    /**
     * Initialize API client
     */
    private function init_api_client() {
        $api_client_file = PAYOP_WC_PLUGIN_DIR . 'includes/api/class-payop-api-client.php';

        if (file_exists($api_client_file)) {
            require_once $api_client_file;

            if (class_exists('PayOp\\WooCommerce\\API\\PayOp_API_Client')) {
                $this->api_client = new \PayOp\WooCommerce\API\PayOp_API_Client(
                    $this->public_key,
                    $this->secret_key,
                    $this->jwt_token,
                    $this->project_id,
                    $this->debug
                );
            }
        }
    }

    /**
     * Initialize AJAX handler
     */
    private function init_ajax_handler() {
        $ajax_handler_file = PAYOP_WC_PLUGIN_DIR . 'includes/api/class-payop-ajax-handler.php';

        if (file_exists($ajax_handler_file)) {
            require_once $ajax_handler_file;

            if (class_exists('PayOp\\WooCommerce\\API\\PayOp_AJAX_Handler')) {
                new \PayOp\WooCommerce\API\PayOp_AJAX_Handler();
            }
        }
    }

    /**
     * Initialize gateway settings form fields
     */
    public function init_form_fields() {
        $this->form_fields = [
            'enabled' => [
                'title' => __('Enable/Disable', 'payop-woocommerce'),
                'type' => 'checkbox',
                'label' => __('Enable PayOp Payment Gateway', 'payop-woocommerce'),
                'default' => 'no'
            ],
            'title' => [
                'title' => __('Title', 'payop-woocommerce'),
                'type' => 'text',
                'description' => __('This controls the title which the user sees during checkout.', 'payop-woocommerce'),
                'default' => __('PayOp Payment Gateway', 'payop-woocommerce'),
                'desc_tip' => true,
            ],
            'description' => [
                'title' => __('Description', 'payop-woocommerce'),
                'type' => 'textarea',
                'description' => __('This controls the description which the user sees during checkout.', 'payop-woocommerce'),
                'default' => __('Pay securely using PayOp with 122+ payment methods', 'payop-woocommerce'),
                'desc_tip' => true,
            ],
            'api_credentials' => [
                'title' => __('API Credentials', 'payop-woocommerce'),
                'type' => 'title',
                'description' => __('Enter your PayOp API credentials. You can find these in your PayOp merchant dashboard.', 'payop-woocommerce'),
            ],
            'public_key' => [
                'title' => __('Public Key', 'payop-woocommerce'),
                'type' => 'text',
                'description' => __('Your PayOp public key (e.g., application-606)', 'payop-woocommerce'),
                'default' => '',
                'desc_tip' => true,
            ],
            'secret_key' => [
                'title' => __('Secret Key', 'payop-woocommerce'),
                'type' => 'password',
                'description' => __('Your PayOp secret key for signature generation', 'payop-woocommerce'),
                'default' => '',
                'desc_tip' => true,
            ],
            'jwt_token' => [
                'title' => __('JWT Token', 'payop-woocommerce'),
                'type' => 'textarea',
                'description' => __('Your PayOp JWT token for API authentication', 'payop-woocommerce'),
                'default' => '',
                'desc_tip' => true,
            ],
            'project_id' => [
                'title' => __('Project ID', 'payop-woocommerce'),
                'type' => 'text',
                'description' => __('Your PayOp project ID (e.g., 606)', 'payop-woocommerce'),
                'default' => '',
                'desc_tip' => true,
            ],
            'advanced_settings' => [
                'title' => __('Advanced Settings', 'payop-woocommerce'),
                'type' => 'title',
                'description' => __('Advanced configuration options', 'payop-woocommerce'),
            ],
            'debug' => [
                'title' => __('Debug Log', 'payop-woocommerce'),
                'type' => 'checkbox',
                'label' => __('Enable logging', 'payop-woocommerce'),
                'default' => 'no',
                'description' => sprintf(
                    __('Log PayOp events, such as API requests, inside %s', 'payop-woocommerce'),
                    '<code>' . WC_Log_Handler_File::get_log_file_path('payop') . '</code>'
                ),
            ],
        ];
    }

    /**
     * Check if gateway is available
     *
     * @return bool
     */
    public function is_available() {
        if ('yes' !== $this->enabled) {
            return false;
        }

        // Check required settings
        if (empty($this->public_key) || empty($this->secret_key) || empty($this->jwt_token) || empty($this->project_id)) {
            return false;
        }

        return true;
    }

    /**
     * Enqueue scripts
     */
    public function enqueue_scripts() {
        if (!is_checkout() || !$this->is_available()) {
            return;
        }

        wp_enqueue_script(
            'payop-checkout',
            PAYOP_WC_PLUGIN_URL . 'assets/js/checkout.js',
            ['jquery'],
            PAYOP_WC_VERSION,
            true
        );

        wp_localize_script('payop-checkout', 'payop_params', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('payop_checkout'),
            'gateway_id' => $this->id,
        ]);

        wp_enqueue_style(
            'payop-checkout',
            PAYOP_WC_PLUGIN_URL . 'assets/css/checkout.css',
            [],
            PAYOP_WC_VERSION
        );
    }

    /**
     * Payment form on checkout page
     */
    public function payment_fields() {
        if ($this->description) {
            echo wpautop(wp_kses_post($this->description));
        }

        // Display payment method selection form
        echo '<div id="payop-payment-form">';
        echo '<p>' . esc_html__('Please select a payment method:', 'payop-woocommerce') . '</p>';
        echo '<div id="payop-payment-methods-loading">';
        echo esc_html__('Loading payment methods...', 'payop-woocommerce');
        echo '</div>';
        echo '<div id="payop-payment-methods" style="display:none;"></div>';
        echo '<div id="payop-additional-fields" style="display:none;"></div>';
        echo '</div>';
    }

    /**
     * Validate payment fields
     *
     * @return bool
     */
    public function validate_fields() {
        // Load security and error handling classes
        if (!class_exists('PayOp\\WooCommerce\\Utils\\PayOp_Security')) {
            require_once PAYOP_WC_PLUGIN_DIR . 'includes/utils/class-payop-security.php';
        }

        if (!class_exists('PayOp\\WooCommerce\\Utils\\PayOp_Error_Handler')) {
            require_once PAYOP_WC_PLUGIN_DIR . 'includes/utils/class-payop-error-handler.php';
        }

        // Validate CSRF token
        if (!wp_verify_nonce($_POST['woocommerce-process-checkout-nonce'] ?? '', 'woocommerce-process_checkout')) {
            \PayOp\WooCommerce\Utils\PayOp_Security::log_security_event('invalid_csrf_token', [
                'action' => 'checkout_validation'
            ]);
            wc_add_notice(__('Security validation failed. Please refresh the page and try again.', 'payop-woocommerce'), 'error');
            return false;
        }

        // Rate limiting
        $client_ip = \PayOp\WooCommerce\Utils\PayOp_Security::get_client_ip();
        if (!empty($client_ip) && !\PayOp\WooCommerce\Utils\PayOp_Security::check_rate_limit('checkout_' . $client_ip, 10, 300)) {
            \PayOp\WooCommerce\Utils\PayOp_Security::log_security_event('rate_limit_exceeded', [
                'ip' => $client_ip,
                'action' => 'checkout_validation'
            ]);
            wc_add_notice(__('Too many attempts. Please wait a few minutes and try again.', 'payop-woocommerce'), 'error');
            return false;
        }

        // Sanitize payment data
        $payment_data = \PayOp\WooCommerce\Utils\PayOp_Security::sanitize_payment_data($_POST);

        // Validate payment method selection
        if (empty($payment_data['payop_payment_method'])) {
            wc_add_notice(__('Please select a payment method.', 'payop-woocommerce'), 'error');
            return false;
        }

        // Validate additional fields if present
        if (!empty($_POST['payop_additional_fields'])) {
            $validation_result = $this->validate_additional_fields($_POST['payop_additional_fields'], $payment_data['payop_payment_method']);
            if (!$validation_result['valid']) {
                foreach ($validation_result['errors'] as $error) {
                    wc_add_notice($error, 'error');
                }
                return false;
            }
        }

        return true;
    }

    /**
     * Validate additional payment fields
     *
     * @param array $fields Additional fields data
     * @param int $payment_method_id Payment method ID
     * @return array Validation result
     */
    private function validate_additional_fields($fields, $payment_method_id) {
        if (!class_exists('PayOp\\WooCommerce\\Utils\\PayOp_Field_Manager')) {
            require_once PAYOP_WC_PLUGIN_DIR . 'includes/utils/class-payop-field-manager.php';
        }

        $errors = [];

        // Get payment method configuration
        $method = $this->get_payment_method_config($payment_method_id);
        if (!$method) {
            $errors[] = __('Invalid payment method selected.', 'payop-woocommerce');
            return ['valid' => false, 'errors' => $errors];
        }

        // Get required fields for this method
        $required_fields = \PayOp\WooCommerce\Utils\PayOp_Field_Manager::get_required_fields(
            $method,
            WC()->customer->get_billing_country()
        );

        // Validate each required field
        foreach ($required_fields as $field_config) {
            $field_name = $field_config['name'];
            $field_value = $fields[$field_name] ?? '';

            $validation_result = \PayOp\WooCommerce\Utils\PayOp_Field_Manager::validate_field(
                $field_name,
                $field_value,
                $field_config,
                WC()->customer->get_billing_country()
            );

            if (!$validation_result['valid']) {
                $errors[] = $validation_result['message'];
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Get payment method configuration
     *
     * @param int $method_id Payment method ID
     * @return array|null
     */
    private function get_payment_method_config($method_id) {
        // Initialize payment methods manager
        $credentials = \PayOp\WooCommerce\Utils\PayOp_Config::get_api_credentials();

        if (!class_exists('PayOp\\WooCommerce\\API\\PayOp_Payment_Methods')) {
            require_once PAYOP_WC_PLUGIN_DIR . 'includes/api/class-payop-payment-methods.php';
        }

        $payment_methods = new \PayOp\WooCommerce\API\PayOp_Payment_Methods($this->api_client);
        return $payment_methods->get_method_by_id($method_id);
    }

    /**
     * Process payment
     *
     * @param int $order_id
     * @return array
     */
    public function process_payment($order_id) {
        $order = wc_get_order($order_id);

        if (!$order) {
            return [
                'result' => 'failure',
                'messages' => __('Order not found.', 'payop-woocommerce')
            ];
        }

        try {
            // Load error handler
            if (!class_exists('PayOp\\WooCommerce\\Utils\\PayOp_Error_Handler')) {
                require_once PAYOP_WC_PLUGIN_DIR . 'includes/utils/class-payop-error-handler.php';
            }

            // Validate order data
            $order_validation = \PayOp\WooCommerce\Utils\PayOp_Security::validate_order_data($order);
            if (!$order_validation['valid']) {
                throw new \Exception(implode(', ', $order_validation['errors']));
            }

            // Get and sanitize payment method data
            $payment_data = \PayOp\WooCommerce\Utils\PayOp_Security::sanitize_payment_data($_POST);
            $payment_method_id = $payment_data['payop_payment_method'] ?? '';
            $additional_fields = $_POST['payop_additional_fields'] ?? [];

            if (empty($payment_method_id)) {
                throw new \Exception(__('No payment method selected.', 'payop-woocommerce'));
            }

            $this->log('Processing payment for order: ' . $order_id . ' with method: ' . $payment_method_id);

            // Create PayOp invoice with error handling
            $invoice_result = $this->create_payop_invoice($order, $payment_method_id, $additional_fields);

            if (is_wp_error($invoice_result)) {
                $error_response = \PayOp\WooCommerce\Utils\PayOp_Error_Handler::handle_api_error($invoice_result, [
                    'order_id' => $order_id,
                    'payment_method_id' => $payment_method_id,
                    'step' => 'invoice_creation'
                ]);
                throw new \Exception($error_response['message']);
            }

            // Create checkout transaction with error handling
            $checkout_result = $this->create_payop_checkout($invoice_result['invoice_id'], $order, $payment_method_id, $additional_fields);

            if (is_wp_error($checkout_result)) {
                $error_response = \PayOp\WooCommerce\Utils\PayOp_Error_Handler::handle_api_error($checkout_result, [
                    'order_id' => $order_id,
                    'invoice_id' => $invoice_result['invoice_id'],
                    'payment_method_id' => $payment_method_id,
                    'step' => 'checkout_creation'
                ]);
                throw new \Exception($error_response['message']);
            }

            // Store transaction data
            $this->store_transaction_data($order, $invoice_result, $checkout_result, $payment_method_id);

            // Mark order as pending
            $order->update_status('pending', __('Awaiting PayOp payment', 'payop-woocommerce'));

            // Reduce stock
            wc_reduce_stock_levels($order_id);

            // Remove cart
            WC()->cart->empty_cart();

            // Get redirect URL from checkout status
            $redirect_url = $this->get_payment_redirect_url($invoice_result['invoice_id']);

            // Log successful payment initiation
            $this->log('Payment successfully initiated for order: ' . $order_id . ', invoice: ' . $invoice_result['invoice_id']);

            return [
                'result' => 'success',
                'redirect' => $redirect_url ?: $this->get_return_url($order)
            ];

        } catch (Exception $e) {
            // Handle payment processing error
            return \PayOp\WooCommerce\Utils\PayOp_Error_Handler::handle_payment_error($e, $order);
        }
    }

    /**
     * Handle IPN callback
     */
    public function handle_ipn() {
        // Load IPN handler
        if (!class_exists('PayOp\\WooCommerce\\API\\PayOp_IPN_Handler')) {
            require_once PAYOP_WC_PLUGIN_DIR . 'includes/api/class-payop-ipn-handler.php';
        }

        $ipn_handler = new \PayOp\WooCommerce\API\PayOp_IPN_Handler();
        $ipn_handler->process_ipn();
    }

    /**
     * Get client IP address
     *
     * @return string
     */
    private function get_client_ip() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = trim(explode(',', $_SERVER[$key])[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '';
    }

    /**
     * Create PayOp invoice
     *
     * @param WC_Order $order
     * @param string $payment_method_id
     * @param array $additional_fields
     * @return array|WP_Error
     */
    private function create_payop_invoice($order, $payment_method_id, $additional_fields) {
        $order_data = [
            'order' => [
                'id' => $order->get_order_number(),
                'amount' => $order->get_total(),
                'currency' => $order->get_currency(),
                'description' => sprintf(__('Order #%s', 'payop-woocommerce'), $order->get_order_number()),
                'items' => $this->get_order_items($order)
            ],
            'payer' => [
                'email' => $order->get_billing_email()
            ],
            'paymentMethod' => intval($payment_method_id),
            'resultUrl' => $this->get_return_url($order),
            'failPath' => wc_get_checkout_url(),
            'metadata' => [
                'order_id' => $order->get_id(),
                'customer_id' => $order->get_customer_id(),
                'additional_fields' => $additional_fields
            ]
        ];

        $result = $this->api_client->create_invoice($order_data);

        if (is_wp_error($result)) {
            return $result;
        }

        return [
            'invoice_id' => $result['data'],
            'response' => $result
        ];
    }

    /**
     * Create PayOp checkout transaction
     *
     * @param string $invoice_id
     * @param WC_Order $order
     * @param string $payment_method_id
     * @param array $additional_fields
     * @return array|WP_Error
     */
    private function create_payop_checkout($invoice_id, $order, $payment_method_id, $additional_fields) {
        $customer_data = array_merge([
            'email' => $order->get_billing_email(),
            'name' => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
            'ip' => $order->get_customer_ip_address()
        ], $additional_fields);

        $customer_data['paymentMethod'] = intval($payment_method_id);
        $customer_data['checkStatusUrl'] = add_query_arg('order_id', $order->get_id(), home_url('/payop-status/'));

        return $this->api_client->create_checkout($invoice_id, $customer_data);
    }

    /**
     * Get payment redirect URL
     *
     * @param string $invoice_id
     * @return string|null
     */
    private function get_payment_redirect_url($invoice_id) {
        $status_result = $this->api_client->check_invoice_status($invoice_id);

        if (is_wp_error($status_result)) {
            $this->log('Error checking invoice status: ' . $status_result->get_error_message());
            return null;
        }

        // Check for redirect URL in response
        if (isset($status_result['data']['form']['url'])) {
            return $status_result['data']['form']['url'];
        }

        return null;
    }

    /**
     * Store transaction data
     *
     * @param WC_Order $order
     * @param array $invoice_result
     * @param array $checkout_result
     * @param string $payment_method_id
     */
    private function store_transaction_data($order, $invoice_result, $checkout_result, $payment_method_id) {
        global $wpdb;

        $transaction_id = $checkout_result['data']['txid'] ?? '';

        // Store in custom table
        $wpdb->insert(
            $wpdb->prefix . 'payop_transactions',
            [
                'order_id' => $order->get_id(),
                'invoice_id' => $invoice_result['invoice_id'],
                'transaction_id' => $transaction_id,
                'payment_method_id' => intval($payment_method_id),
                'status' => 'pending'
            ],
            ['%d', '%s', '%s', '%d', '%s']
        );

        // Store in order meta
        $order->update_meta_data('_payop_invoice_id', $invoice_result['invoice_id']);
        $order->update_meta_data('_payop_transaction_id', $transaction_id);
        $order->update_meta_data('_payop_payment_method_id', $payment_method_id);
        $order->save();
    }

    /**
     * Get order items for PayOp
     *
     * @param WC_Order $order
     * @return array
     */
    private function get_order_items($order) {
        $items = [];

        foreach ($order->get_items() as $item) {
            $items[] = [
                'id' => $item->get_product_id(),
                'name' => $item->get_name(),
                'price' => $item->get_total()
            ];
        }

        return $items;
    }

    /**
     * Log messages
     *
     * @param string $message
     */
    private function log($message) {
        if ($this->debug) {
            $logger = wc_get_logger();
            $logger->info($message, ['source' => 'payop']);
        }
    }
}
