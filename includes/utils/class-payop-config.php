<?php
/**
 * PayOp Configuration Manager
 *
 * @package PayOp\WooCommerce\Utils
 */

namespace PayOp\WooCommerce\Utils;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Configuration Manager
 * 
 * Handles secure storage and retrieval of PayOp configuration settings
 */
class PayOp_Config {

    /**
     * Option prefix
     */
    const OPTION_PREFIX = 'payop_wc_';

    /**
     * Encryption key option name
     */
    const ENCRYPTION_KEY_OPTION = 'payop_wc_encryption_key';

    /**
     * Get configuration value
     *
     * @param string $key Configuration key
     * @param mixed $default Default value
     * @param bool $decrypt Whether to decrypt the value
     * @return mixed
     */
    public static function get($key, $default = null, $decrypt = false) {
        $option_name = self::OPTION_PREFIX . $key;
        $value = get_option($option_name, $default);

        if ($decrypt && !empty($value)) {
            $value = self::decrypt($value);
        }

        return $value;
    }

    /**
     * Set configuration value
     *
     * @param string $key Configuration key
     * @param mixed $value Configuration value
     * @param bool $encrypt Whether to encrypt the value
     * @return bool
     */
    public static function set($key, $value, $encrypt = false) {
        $option_name = self::OPTION_PREFIX . $key;

        if ($encrypt && !empty($value)) {
            $value = self::encrypt($value);
        }

        return update_option($option_name, $value);
    }

    /**
     * Delete configuration value
     *
     * @param string $key Configuration key
     * @return bool
     */
    public static function delete($key) {
        $option_name = self::OPTION_PREFIX . $key;
        return delete_option($option_name);
    }

    /**
     * Get PayOp API credentials
     *
     * @return array
     */
    public static function get_api_credentials() {
        return [
            'public_key' => self::get('public_key'),
            'secret_key' => self::get('secret_key', '', true), // Decrypt secret key
            'jwt_token' => self::get('jwt_token', '', true),   // Decrypt JWT token
            'project_id' => self::get('project_id')
        ];
    }

    /**
     * Set PayOp API credentials
     *
     * @param array $credentials
     * @return bool
     */
    public static function set_api_credentials($credentials) {
        $success = true;

        if (isset($credentials['public_key'])) {
            $success &= self::set('public_key', $credentials['public_key']);
        }

        if (isset($credentials['secret_key'])) {
            $success &= self::set('secret_key', $credentials['secret_key'], true); // Encrypt secret key
        }

        if (isset($credentials['jwt_token'])) {
            $success &= self::set('jwt_token', $credentials['jwt_token'], true);   // Encrypt JWT token
        }

        if (isset($credentials['project_id'])) {
            $success &= self::set('project_id', $credentials['project_id']);
        }

        return $success;
    }

    /**
     * Get payment method cache settings
     *
     * @return array
     */
    public static function get_cache_settings() {
        return [
            'enabled' => self::get('cache_enabled', true),
            'ttl' => self::get('cache_ttl', 3600), // 1 hour default
            'max_size' => self::get('cache_max_size', 1000) // Max cached methods
        ];
    }

    /**
     * Get debug settings
     *
     * @return array
     */
    public static function get_debug_settings() {
        return [
            'enabled' => self::get('debug_enabled', false),
            'log_api_requests' => self::get('debug_log_api', true),
            'log_ipn' => self::get('debug_log_ipn', true),
            'log_level' => self::get('debug_log_level', 'info')
        ];
    }

    /**
     * Encrypt sensitive data
     *
     * @param string $data Data to encrypt
     * @return string
     */
    private static function encrypt($data) {
        if (empty($data)) {
            return $data;
        }

        $key = self::get_encryption_key();
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }

    /**
     * Decrypt sensitive data
     *
     * @param string $encrypted_data Encrypted data
     * @return string
     */
    private static function decrypt($encrypted_data) {
        if (empty($encrypted_data)) {
            return $encrypted_data;
        }

        $key = self::get_encryption_key();
        $data = base64_decode($encrypted_data);
        
        if ($data === false) {
            return $encrypted_data; // Return original if base64 decode fails
        }

        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
        
        return $decrypted !== false ? $decrypted : $encrypted_data;
    }

    /**
     * Get or generate encryption key
     *
     * @return string
     */
    private static function get_encryption_key() {
        $key = get_option(self::ENCRYPTION_KEY_OPTION);
        
        if (empty($key)) {
            // Generate new encryption key
            $key = wp_generate_password(32, false);
            update_option(self::ENCRYPTION_KEY_OPTION, $key);
        }
        
        return $key;
    }

    /**
     * Validate API credentials
     *
     * @param array $credentials
     * @return array Array of validation errors
     */
    public static function validate_api_credentials($credentials) {
        $errors = [];

        if (empty($credentials['public_key'])) {
            $errors['public_key'] = __('Public key is required', 'payop-woocommerce');
        }

        if (empty($credentials['secret_key'])) {
            $errors['secret_key'] = __('Secret key is required', 'payop-woocommerce');
        }

        if (empty($credentials['jwt_token'])) {
            $errors['jwt_token'] = __('JWT token is required', 'payop-woocommerce');
        }

        if (empty($credentials['project_id'])) {
            $errors['project_id'] = __('Project ID is required', 'payop-woocommerce');
        } elseif (!is_numeric($credentials['project_id'])) {
            $errors['project_id'] = __('Project ID must be numeric', 'payop-woocommerce');
        }

        return $errors;
    }

    /**
     * Get all configuration as array
     *
     * @return array
     */
    public static function get_all_config() {
        return [
            'api_credentials' => self::get_api_credentials(),
            'cache_settings' => self::get_cache_settings(),
            'debug_settings' => self::get_debug_settings(),
            'gateway_settings' => [
                'enabled' => self::get('gateway_enabled', false),
                'title' => self::get('gateway_title', __('PayOp Payment Gateway', 'payop-woocommerce')),
                'description' => self::get('gateway_description', __('Pay securely using PayOp with 122+ payment methods', 'payop-woocommerce'))
            ]
        ];
    }

    /**
     * Reset all configuration
     *
     * @return bool
     */
    public static function reset_all() {
        global $wpdb;

        // Delete all PayOp options
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                self::OPTION_PREFIX . '%'
            )
        );

        // Delete encryption key
        delete_option(self::ENCRYPTION_KEY_OPTION);

        return true;
    }

    /**
     * Export configuration (for backup/migration)
     *
     * @return array
     */
    public static function export_config() {
        $config = self::get_all_config();
        
        // Remove sensitive data from export
        unset($config['api_credentials']['secret_key']);
        unset($config['api_credentials']['jwt_token']);
        
        return $config;
    }

    /**
     * Import configuration (for backup/migration)
     *
     * @param array $config Configuration array
     * @return bool
     */
    public static function import_config($config) {
        if (!is_array($config)) {
            return false;
        }

        $success = true;

        // Import API credentials (excluding sensitive data)
        if (isset($config['api_credentials'])) {
            $credentials = $config['api_credentials'];
            unset($credentials['secret_key']); // Don't import sensitive data
            unset($credentials['jwt_token']);
            $success &= self::set_api_credentials($credentials);
        }

        // Import cache settings
        if (isset($config['cache_settings'])) {
            foreach ($config['cache_settings'] as $key => $value) {
                $success &= self::set('cache_' . $key, $value);
            }
        }

        // Import debug settings
        if (isset($config['debug_settings'])) {
            foreach ($config['debug_settings'] as $key => $value) {
                $success &= self::set('debug_' . $key, $value);
            }
        }

        // Import gateway settings
        if (isset($config['gateway_settings'])) {
            foreach ($config['gateway_settings'] as $key => $value) {
                $success &= self::set('gateway_' . $key, $value);
            }
        }

        return $success;
    }
}
