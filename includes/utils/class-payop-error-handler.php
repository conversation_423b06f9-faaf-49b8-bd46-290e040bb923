<?php
/**
 * PayOp Error Handler
 *
 * @package PayOp\WooCommerce\Utils
 */

namespace PayOp\WooCommerce\Utils;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Error Handler
 * 
 * Handles error logging, user notifications, and error recovery
 */
class PayOp_Error_Handler {

    /**
     * Error types
     */
    const ERROR_TYPE_API = 'api';
    const ERROR_TYPE_VALIDATION = 'validation';
    const ERROR_TYPE_PAYMENT = 'payment';
    const ERROR_TYPE_SECURITY = 'security';
    const ERROR_TYPE_SYSTEM = 'system';

    /**
     * Log error with context
     *
     * @param string $message Error message
     * @param string $type Error type
     * @param array $context Additional context
     * @param string $level Log level
     */
    public static function log_error($message, $type = self::ERROR_TYPE_SYSTEM, $context = [], $level = 'error') {
        $logger = wc_get_logger();
        
        $log_data = [
            'type' => $type,
            'message' => $message,
            'context' => $context,
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'ip_address' => PayOp_Security::get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? ''
        ];
        
        $formatted_message = sprintf(
            'PayOp Error [%s]: %s | Context: %s',
            strtoupper($type),
            $message,
            wp_json_encode($context)
        );
        
        $logger->log($level, $formatted_message, ['source' => 'payop-errors']);
        
        // Store in database for admin review
        self::store_error_in_database($log_data);
        
        // Send critical errors to admin
        if ($level === 'critical' || $level === 'emergency') {
            self::notify_admin_of_critical_error($log_data);
        }
    }

    /**
     * Handle API errors
     *
     * @param WP_Error $error WP_Error object
     * @param array $context Additional context
     * @return array Formatted error response
     */
    public static function handle_api_error($error, $context = []) {
        $error_code = $error->get_error_code();
        $error_message = $error->get_error_message();
        $error_data = $error->get_error_data();
        
        // Log the error
        self::log_error($error_message, self::ERROR_TYPE_API, array_merge($context, [
            'error_code' => $error_code,
            'error_data' => $error_data
        ]));
        
        // Return user-friendly message
        $user_message = self::get_user_friendly_message($error_code, $error_message);
        
        return [
            'success' => false,
            'message' => $user_message,
            'error_code' => $error_code,
            'retry_possible' => self::is_retryable_error($error_code)
        ];
    }

    /**
     * Handle payment processing errors
     *
     * @param Exception $exception Exception object
     * @param WC_Order $order Order object
     * @return array Error response
     */
    public static function handle_payment_error($exception, $order = null) {
        $context = [];
        
        if ($order) {
            $context['order_id'] = $order->get_id();
            $context['order_total'] = $order->get_total();
            $context['order_currency'] = $order->get_currency();
        }
        
        self::log_error($exception->getMessage(), self::ERROR_TYPE_PAYMENT, $context);
        
        // Update order with error note
        if ($order) {
            $order->add_order_note(
                sprintf(__('PayOp payment error: %s', 'payop-woocommerce'), $exception->getMessage())
            );
        }
        
        return [
            'result' => 'failure',
            'message' => self::get_payment_error_message($exception->getMessage()),
            'reload' => false,
            'refresh' => false
        ];
    }

    /**
     * Handle validation errors
     *
     * @param array $errors Array of validation errors
     * @param array $context Additional context
     * @return array Formatted error response
     */
    public static function handle_validation_errors($errors, $context = []) {
        foreach ($errors as $field => $error) {
            self::log_error($error, self::ERROR_TYPE_VALIDATION, array_merge($context, [
                'field' => $field
            ]), 'warning');
        }
        
        return [
            'success' => false,
            'errors' => $errors,
            'message' => __('Please correct the errors below and try again.', 'payop-woocommerce')
        ];
    }

    /**
     * Get user-friendly error message
     *
     * @param string $error_code Error code
     * @param string $original_message Original error message
     * @return string User-friendly message
     */
    private static function get_user_friendly_message($error_code, $original_message) {
        $messages = [
            'unauthorized' => __('Invalid API credentials. Please contact the store administrator.', 'payop-woocommerce'),
            'forbidden' => __('Access denied. Please contact the store administrator.', 'payop-woocommerce'),
            'not_found' => __('Payment method not available. Please try a different payment method.', 'payop-woocommerce'),
            'validation_error' => __('Invalid payment information. Please check your details and try again.', 'payop-woocommerce'),
            'rate_limit' => __('Too many requests. Please wait a moment and try again.', 'payop-woocommerce'),
            'server_error' => __('Payment service temporarily unavailable. Please try again later.', 'payop-woocommerce'),
            'timeout' => __('Request timeout. Please check your connection and try again.', 'payop-woocommerce'),
            'network_error' => __('Network error. Please check your connection and try again.', 'payop-woocommerce')
        ];
        
        return $messages[$error_code] ?? __('An error occurred while processing your payment. Please try again.', 'payop-woocommerce');
    }

    /**
     * Get payment-specific error message
     *
     * @param string $error_message Original error message
     * @return string User-friendly payment error message
     */
    private static function get_payment_error_message($error_message) {
        // Check for common payment error patterns
        if (strpos($error_message, 'insufficient funds') !== false) {
            return __('Insufficient funds. Please check your account balance or try a different payment method.', 'payop-woocommerce');
        }
        
        if (strpos($error_message, 'card declined') !== false) {
            return __('Your card was declined. Please try a different payment method or contact your bank.', 'payop-woocommerce');
        }
        
        if (strpos($error_message, 'expired') !== false) {
            return __('Your payment method has expired. Please update your payment information.', 'payop-woocommerce');
        }
        
        if (strpos($error_message, 'invalid') !== false) {
            return __('Invalid payment information. Please check your details and try again.', 'payop-woocommerce');
        }
        
        return __('Payment processing failed. Please try again or contact support.', 'payop-woocommerce');
    }

    /**
     * Check if error is retryable
     *
     * @param string $error_code Error code
     * @return bool
     */
    private static function is_retryable_error($error_code) {
        $retryable_errors = [
            'timeout',
            'network_error',
            'server_error',
            'rate_limit'
        ];
        
        return in_array($error_code, $retryable_errors);
    }

    /**
     * Store error in database
     *
     * @param array $error_data Error data
     */
    private static function store_error_in_database($error_data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'payop_error_log';
        
        // Create table if it doesn't exist
        self::create_error_log_table();
        
        $wpdb->insert(
            $table_name,
            [
                'error_type' => $error_data['type'],
                'error_message' => $error_data['message'],
                'error_context' => wp_json_encode($error_data['context']),
                'user_id' => $error_data['user_id'],
                'ip_address' => $error_data['ip_address'],
                'user_agent' => $error_data['user_agent'],
                'request_uri' => $error_data['request_uri'],
                'created_at' => $error_data['timestamp']
            ],
            ['%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s']
        );
    }

    /**
     * Create error log table
     */
    private static function create_error_log_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'payop_error_log';
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            error_type varchar(50) NOT NULL,
            error_message text NOT NULL,
            error_context longtext,
            user_id int(11) DEFAULT 0,
            ip_address varchar(45),
            user_agent text,
            request_uri text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_error_type (error_type),
            KEY idx_created_at (created_at)
        ) $charset_collate;";
        
        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
    }

    /**
     * Notify admin of critical errors
     *
     * @param array $error_data Error data
     */
    private static function notify_admin_of_critical_error($error_data) {
        $admin_email = get_option('admin_email');
        
        if (empty($admin_email)) {
            return;
        }
        
        $subject = sprintf(__('[%s] PayOp Critical Error', 'payop-woocommerce'), get_bloginfo('name'));
        
        $message = sprintf(
            __("A critical error occurred in the PayOp payment gateway:\n\nError Type: %s\nMessage: %s\nTime: %s\nIP Address: %s\n\nPlease check the PayOp logs for more details.", 'payop-woocommerce'),
            $error_data['type'],
            $error_data['message'],
            $error_data['timestamp'],
            $error_data['ip_address']
        );
        
        wp_mail($admin_email, $subject, $message);
    }

    /**
     * Get error statistics for admin
     *
     * @param int $days Number of days to analyze
     * @return array Error statistics
     */
    public static function get_error_statistics($days = 7) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'payop_error_log';
        $since_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
            return [];
        }
        
        $stats = [];
        
        // Total errors
        $stats['total'] = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $table_name WHERE created_at >= %s",
                $since_date
            )
        );
        
        // Errors by type
        $stats['by_type'] = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT error_type, COUNT(*) as count 
                 FROM $table_name 
                 WHERE created_at >= %s 
                 GROUP BY error_type 
                 ORDER BY count DESC",
                $since_date
            ),
            ARRAY_A
        );
        
        // Recent errors
        $stats['recent'] = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table_name 
                 WHERE created_at >= %s 
                 ORDER BY created_at DESC 
                 LIMIT 10",
                $since_date
            ),
            ARRAY_A
        );
        
        return $stats;
    }

    /**
     * Clean up old error logs
     *
     * @param int $days Keep logs for this many days
     */
    public static function cleanup_error_logs($days = 30) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'payop_error_log';
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM $table_name WHERE created_at < %s",
                $cutoff_date
            )
        );
    }

    /**
     * Handle fatal errors
     *
     * @param string $message Error message
     * @param array $context Error context
     */
    public static function handle_fatal_error($message, $context = []) {
        self::log_error($message, self::ERROR_TYPE_SYSTEM, $context, 'emergency');
        
        // Disable gateway if too many fatal errors
        $error_count = get_transient('payop_fatal_error_count');
        if ($error_count === false) {
            $error_count = 0;
        }
        
        $error_count++;
        set_transient('payop_fatal_error_count', $error_count, HOUR_IN_SECONDS);
        
        if ($error_count >= 5) {
            // Disable gateway temporarily
            update_option('woocommerce_payop_enabled', 'no');
            
            self::log_error(
                'PayOp gateway automatically disabled due to repeated fatal errors',
                self::ERROR_TYPE_SYSTEM,
                ['error_count' => $error_count],
                'emergency'
            );
        }
    }

    /**
     * Recovery actions for common errors
     *
     * @param string $error_code Error code
     * @return array Recovery suggestions
     */
    public static function get_recovery_actions($error_code) {
        $actions = [
            'unauthorized' => [
                'check_credentials' => __('Verify API credentials in settings', 'payop-woocommerce'),
                'contact_payop' => __('Contact PayOp support for credential issues', 'payop-woocommerce')
            ],
            'rate_limit' => [
                'wait' => __('Wait a few minutes before retrying', 'payop-woocommerce'),
                'reduce_frequency' => __('Reduce API request frequency', 'payop-woocommerce')
            ],
            'server_error' => [
                'retry_later' => __('Try again in a few minutes', 'payop-woocommerce'),
                'check_status' => __('Check PayOp service status', 'payop-woocommerce')
            ]
        ];
        
        return $actions[$error_code] ?? [];
    }
}
