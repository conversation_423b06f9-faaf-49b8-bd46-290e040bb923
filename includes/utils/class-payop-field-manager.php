<?php
/**
 * PayOp Field Manager
 *
 * @package PayOp\WooCommerce\Utils
 */

namespace PayOp\WooCommerce\Utils;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Field Manager
 * 
 * Handles dynamic field validation and processing for 122+ payment methods
 * Based on factual analysis of field requirements across payment methods
 */
class PayOp_Field_Manager {

    /**
     * Field validation patterns based on regional requirements
     */
    const VALIDATION_PATTERNS = [
        // Colombian/Latin American Documents (39 methods)
        'document_colombia' => '/^\d{6,10}$/',
        
        // Uruguayan CI Documents
        'document_uruguay' => '/^\d{6,8}$/',
        
        // Brazilian CPF/CNPJ (8 methods)
        'document_brazil' => '/^\d{11,14}$/',
        
        // European phone numbers
        'phone_europe' => '/^\+?[1-9]\d{1,14}$/',
        
        // Date of birth (DD.MM.YYYY)
        'date_of_birth' => '/^\d{2}\.\d{2}\.\d{4}$/',
        
        // Email validation (all 122 methods)
        'email' => '/^[^\s@]+@[^\s@]+\.[^\s@]+$/',
        
        // Name validation (120 methods)
        'name' => '/^[a-zA-Z\s\-\'\.]{2,50}$/'
    ];

    /**
     * Country-specific document patterns
     */
    const COUNTRY_DOCUMENT_PATTERNS = [
        'CO' => 'document_colombia',    // Colombia - 39 methods
        'UY' => 'document_uruguay',     // Uruguay
        'BR' => 'document_brazil',      // Brazil - 8 methods
        'AR' => 'document_colombia',    // Argentina - similar pattern
        'PE' => 'document_colombia',    // Peru - similar pattern
        'CL' => 'document_colombia',    // Chile - similar pattern
        'EC' => 'document_colombia',    // Ecuador - similar pattern
        'MX' => 'document_colombia',    // Mexico - similar pattern
    ];

    /**
     * Banking field configurations for SEPA and other transfers
     */
    const BANKING_FIELDS = [
        'bank_code' => [
            'type' => 'select',
            'required' => true,
            'options_source' => 'api' // Options loaded from PayOp API
        ],
        'bank_type' => [
            'type' => 'select',
            'required' => true,
            'options' => [
                'SEPA' => 'SEPA Transfer',
                'SEPA_INSTANT' => 'SEPA Instant Transfer',
                'FPS' => 'Faster Payments (UK)',
                'IBAN_GB' => 'UK IBAN',
                'IBAN_NOT_GB' => 'Non-UK IBAN'
            ]
        ]
    ];

    /**
     * Get required fields for a payment method
     *
     * @param array $method_config Payment method configuration
     * @param string $country Customer country code
     * @return array
     */
    public static function get_required_fields($method_config, $country = '') {
        if (!isset($method_config['config']['fields'])) {
            return [];
        }

        $fields = $method_config['config']['fields'];
        $processed_fields = [];

        foreach ($fields as $field) {
            $processed_field = self::process_field_config($field, $country, $method_config);
            if ($processed_field) {
                $processed_fields[] = $processed_field;
            }
        }

        return $processed_fields;
    }

    /**
     * Process individual field configuration
     *
     * @param array $field Field configuration
     * @param string $country Customer country
     * @param array $method_config Full method configuration
     * @return array|null
     */
    private static function process_field_config($field, $country, $method_config) {
        $field_name = $field['name'];
        $field_type = $field['type'];
        $required = $field['required'] ?? false;

        $processed = [
            'name' => $field_name,
            'type' => $field_type,
            'title' => self::get_field_title($field_name),
            'required' => $required,
            'placeholder' => self::get_field_placeholder($field_name, $country),
            'description' => self::get_field_description($field_name, $country),
            'validation_pattern' => self::get_validation_pattern($field_name, $country),
            'error_message' => self::get_error_message($field_name, $country)
        ];

        // Add field-specific configurations
        switch ($field_name) {
            case 'document':
                $processed = array_merge($processed, self::get_document_field_config($country));
                break;
                
            case 'bank_code':
            case 'bank_type':
                $processed = array_merge($processed, self::get_banking_field_config($field_name, $method_config));
                break;
                
            case 'date_of_birth':
                $processed['input_mask'] = '99.99.9999';
                break;
        }

        return $processed;
    }

    /**
     * Get field title for display
     *
     * @param string $field_name
     * @return string
     */
    private static function get_field_title($field_name) {
        $titles = [
            'email' => __('Email Address', 'payop-woocommerce'),
            'name' => __('Full Name', 'payop-woocommerce'),
            'phone' => __('Phone Number', 'payop-woocommerce'),
            'document' => __('Document Number', 'payop-woocommerce'),
            'date_of_birth' => __('Date of Birth', 'payop-woocommerce'),
            'bank_code' => __('Bank', 'payop-woocommerce'),
            'bank_type' => __('Transfer Type', 'payop-woocommerce'),
            'nationalid' => __('National ID', 'payop-woocommerce'),
            'cpf' => __('CPF Number', 'payop-woocommerce'),
            'cnpj' => __('CNPJ Number', 'payop-woocommerce')
        ];

        return $titles[$field_name] ?? ucfirst(str_replace('_', ' ', $field_name));
    }

    /**
     * Get field placeholder text
     *
     * @param string $field_name
     * @param string $country
     * @return string
     */
    private static function get_field_placeholder($field_name, $country) {
        $placeholders = [
            'email' => __('Enter your email address', 'payop-woocommerce'),
            'name' => __('Enter your full name', 'payop-woocommerce'),
            'phone' => __('Enter your phone number', 'payop-woocommerce'),
            'date_of_birth' => __('DD.MM.YYYY', 'payop-woocommerce'),
            'bank_code' => __('Select your bank', 'payop-woocommerce'),
            'bank_type' => __('Select transfer type', 'payop-woocommerce')
        ];

        // Country-specific placeholders for document fields
        if ($field_name === 'document') {
            switch ($country) {
                case 'CO':
                    return __('Enter your Cédula number (6-10 digits)', 'payop-woocommerce');
                case 'BR':
                    return __('Enter your CPF/CNPJ (11-14 digits)', 'payop-woocommerce');
                case 'UY':
                    return __('Enter your CI number (6-8 digits)', 'payop-woocommerce');
                default:
                    return __('Enter your document number', 'payop-woocommerce');
            }
        }

        return $placeholders[$field_name] ?? '';
    }

    /**
     * Get field description text
     *
     * @param string $field_name
     * @param string $country
     * @return string
     */
    private static function get_field_description($field_name, $country) {
        $descriptions = [
            'date_of_birth' => __('Required for European bank transfers (SEPA)', 'payop-woocommerce'),
            'bank_code' => __('Select your bank from the available options', 'payop-woocommerce'),
            'bank_type' => __('Choose the type of bank transfer', 'payop-woocommerce')
        ];

        if ($field_name === 'document') {
            switch ($country) {
                case 'CO':
                    return __('Enter your Colombian Cédula de Ciudadanía number', 'payop-woocommerce');
                case 'BR':
                    return __('Enter your Brazilian CPF or CNPJ number', 'payop-woocommerce');
                case 'UY':
                    return __('Enter your Uruguayan Cédula de Identidad number', 'payop-woocommerce');
                default:
                    return __('Enter your government-issued document number', 'payop-woocommerce');
            }
        }

        return $descriptions[$field_name] ?? '';
    }

    /**
     * Get validation pattern for field
     *
     * @param string $field_name
     * @param string $country
     * @return string
     */
    private static function get_validation_pattern($field_name, $country) {
        // Document validation based on country
        if ($field_name === 'document' && isset(self::COUNTRY_DOCUMENT_PATTERNS[$country])) {
            $pattern_key = self::COUNTRY_DOCUMENT_PATTERNS[$country];
            return self::VALIDATION_PATTERNS[$pattern_key] ?? '';
        }

        // Standard field patterns
        $pattern_map = [
            'email' => 'email',
            'name' => 'name',
            'phone' => 'phone_europe',
            'date_of_birth' => 'date_of_birth'
        ];

        if (isset($pattern_map[$field_name])) {
            return self::VALIDATION_PATTERNS[$pattern_map[$field_name]] ?? '';
        }

        return '';
    }

    /**
     * Get error message for field validation
     *
     * @param string $field_name
     * @param string $country
     * @return string
     */
    private static function get_error_message($field_name, $country) {
        $messages = [
            'email' => __('Please enter a valid email address', 'payop-woocommerce'),
            'name' => __('Please enter a valid name (2-50 characters)', 'payop-woocommerce'),
            'phone' => __('Please enter a valid phone number', 'payop-woocommerce'),
            'date_of_birth' => __('Please enter date in DD.MM.YYYY format', 'payop-woocommerce')
        ];

        if ($field_name === 'document') {
            switch ($country) {
                case 'CO':
                    return __('Please enter a valid Colombian Cédula (6-10 digits)', 'payop-woocommerce');
                case 'BR':
                    return __('Please enter a valid Brazilian CPF/CNPJ (11-14 digits)', 'payop-woocommerce');
                case 'UY':
                    return __('Please enter a valid Uruguayan CI (6-8 digits)', 'payop-woocommerce');
                default:
                    return __('Please enter a valid document number', 'payop-woocommerce');
            }
        }

        return $messages[$field_name] ?? __('Invalid format', 'payop-woocommerce');
    }

    /**
     * Get document field configuration
     *
     * @param string $country
     * @return array
     */
    private static function get_document_field_config($country) {
        $config = [];

        // Add input mask based on country
        switch ($country) {
            case 'CO':
            case 'AR':
            case 'PE':
            case 'CL':
            case 'EC':
            case 'MX':
                $config['input_mask'] = '********99'; // Up to 10 digits
                $config['max_length'] = 10;
                break;
                
            case 'BR':
                $config['input_mask'] = '********999999'; // Up to 14 digits
                $config['max_length'] = 14;
                break;
                
            case 'UY':
                $config['input_mask'] = '********'; // Up to 8 digits
                $config['max_length'] = 8;
                break;
        }

        return $config;
    }

    /**
     * Get banking field configuration
     *
     * @param string $field_name
     * @param array $method_config
     * @return array
     */
    private static function get_banking_field_config($field_name, $method_config) {
        if (!isset(self::BANKING_FIELDS[$field_name])) {
            return [];
        }

        $config = self::BANKING_FIELDS[$field_name];

        // For bank_code, options would be loaded from API based on method configuration
        if ($field_name === 'bank_code' && $config['options_source'] === 'api') {
            $config['options'] = self::get_bank_options($method_config);
        }

        return $config;
    }

    /**
     * Get bank options for bank_code field
     *
     * @param array $method_config
     * @return array
     */
    private static function get_bank_options($method_config) {
        // This would typically load from PayOp API or cached data
        // For now, return common European banks for SEPA transfers
        return [
            'SEPA_GENERIC' => __('Generic SEPA Bank', 'payop-woocommerce'),
            'SEPA_INSTANT' => __('SEPA Instant Transfer', 'payop-woocommerce'),
            'FPS_UK' => __('UK Faster Payments', 'payop-woocommerce')
        ];
    }

    /**
     * Validate field value
     *
     * @param string $field_name
     * @param string $field_value
     * @param array $field_config
     * @param string $country
     * @return array
     */
    public static function validate_field($field_name, $field_value, $field_config, $country = '') {
        $result = ['valid' => true, 'message' => ''];

        // Check required fields
        if (($field_config['required'] ?? false) && empty($field_value)) {
            return [
                'valid' => false,
                'message' => sprintf(__('%s is required', 'payop-woocommerce'), $field_config['title'] ?? ucfirst($field_name))
            ];
        }

        // Skip validation if field is empty and not required
        if (empty($field_value) && !($field_config['required'] ?? false)) {
            return $result;
        }

        // Get validation pattern
        $pattern = self::get_validation_pattern($field_name, $country);
        
        if (!empty($pattern) && !preg_match($pattern, $field_value)) {
            return [
                'valid' => false,
                'message' => self::get_error_message($field_name, $country)
            ];
        }

        // Additional validation for specific fields
        switch ($field_name) {
            case 'email':
                if (!is_email($field_value)) {
                    return [
                        'valid' => false,
                        'message' => __('Please enter a valid email address', 'payop-woocommerce')
                    ];
                }
                break;

            case 'date_of_birth':
                $result = self::validate_date_of_birth($field_value);
                break;
        }

        return $result;
    }

    /**
     * Validate date of birth
     *
     * @param string $date_value
     * @return array
     */
    private static function validate_date_of_birth($date_value) {
        // Check format DD.MM.YYYY
        if (!preg_match('/^\d{2}\.\d{2}\.\d{4}$/', $date_value)) {
            return [
                'valid' => false,
                'message' => __('Please enter date in DD.MM.YYYY format', 'payop-woocommerce')
            ];
        }

        // Parse and validate date
        $parts = explode('.', $date_value);
        $day = intval($parts[0]);
        $month = intval($parts[1]);
        $year = intval($parts[2]);

        if (!checkdate($month, $day, $year)) {
            return [
                'valid' => false,
                'message' => __('Please enter a valid date', 'payop-woocommerce')
            ];
        }

        // Check age (must be at least 18 years old)
        $birth_date = new \DateTime($year . '-' . $month . '-' . $day);
        $today = new \DateTime();
        $age = $today->diff($birth_date)->y;

        if ($age < 18) {
            return [
                'valid' => false,
                'message' => __('You must be at least 18 years old', 'payop-woocommerce')
            ];
        }

        if ($age > 120) {
            return [
                'valid' => false,
                'message' => __('Please enter a valid birth date', 'payop-woocommerce')
            ];
        }

        return ['valid' => true, 'message' => ''];
    }

    /**
     * Get field statistics for admin
     *
     * @param array $all_methods All payment methods
     * @return array
     */
    public static function get_field_statistics($all_methods) {
        $stats = [
            'total_methods' => count($all_methods),
            'field_requirements' => [],
            'by_country' => [],
            'complexity_levels' => [
                'simple' => 0,      // email + name only
                'medium' => 0,      // + phone/document
                'complex' => 0      // + banking details
            ]
        ];

        foreach ($all_methods as $method) {
            if (!isset($method['config']['fields'])) {
                continue;
            }

            $fields = $method['config']['fields'];
            $required_fields = array_filter($fields, function($field) {
                return $field['required'] ?? false;
            });

            // Count field requirements
            foreach ($required_fields as $field) {
                $field_name = $field['name'];
                $stats['field_requirements'][$field_name] = ($stats['field_requirements'][$field_name] ?? 0) + 1;
            }

            // Determine complexity level
            $required_count = count($required_fields);
            $has_banking = false;
            foreach ($required_fields as $field) {
                if (in_array($field['name'], ['bank_code', 'bank_type', 'date_of_birth'])) {
                    $has_banking = true;
                    break;
                }
            }

            if ($has_banking) {
                $stats['complexity_levels']['complex']++;
            } elseif ($required_count > 2) {
                $stats['complexity_levels']['medium']++;
            } else {
                $stats['complexity_levels']['simple']++;
            }
        }

        return $stats;
    }
}
