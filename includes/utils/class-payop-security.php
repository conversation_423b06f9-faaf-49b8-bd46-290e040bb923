<?php
/**
 * PayOp Security Manager
 *
 * @package PayOp\WooCommerce\Utils
 */

namespace PayOp\WooCommerce\Utils;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Security Manager
 * 
 * Handles security measures, validation, and protection
 */
class PayOp_Security {

    /**
     * Validate IPN request
     *
     * @param array $data IPN data
     * @param string $signature Received signature
     * @param string $secret_key Secret key for validation
     * @return bool
     */
    public static function validate_ipn_signature($data, $signature, $secret_key) {
        // Generate expected signature based on PayOp documentation
        $expected_signature = self::generate_ipn_signature($data, $secret_key);
        
        return hash_equals($expected_signature, $signature);
    }

    /**
     * Generate IPN signature
     *
     * @param array $data IPN data
     * @param string $secret_key Secret key
     * @return string
     */
    public static function generate_ipn_signature($data, $secret_key) {
        // Sort data by keys for consistent signature generation
        ksort($data);
        
        $signature_string = '';
        foreach ($data as $key => $value) {
            if ($key !== 'signature') {
                $signature_string .= $key . '=' . $value . '&';
            }
        }
        
        // Remove trailing &
        $signature_string = rtrim($signature_string, '&');
        
        // Add secret key
        $signature_string .= $secret_key;
        
        return hash('sha256', $signature_string);
    }

    /**
     * Validate client IP address
     *
     * @param string $client_ip Client IP address
     * @return bool
     */
    public static function validate_ipn_ip($client_ip) {
        $allowed_ips = PAYOP_IPN_ALLOWED_IPS;
        
        // Check if IP is in allowed list
        return in_array($client_ip, $allowed_ips, true);
    }

    /**
     * Get real client IP address
     *
     * @return string
     */
    public static function get_client_ip() {
        $ip_keys = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'REMOTE_ADDR'
        ];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                // Validate IP format
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '';
    }

    /**
     * Sanitize and validate payment data
     *
     * @param array $data Raw payment data
     * @return array Sanitized data
     */
    public static function sanitize_payment_data($data) {
        $sanitized = [];
        
        // Define allowed fields and their types
        $allowed_fields = [
            'payop_payment_method' => 'int',
            'email' => 'email',
            'name' => 'text',
            'phone' => 'text',
            'document' => 'text',
            'date_of_birth' => 'text',
            'bank_code' => 'text',
            'bank_type' => 'text'
        ];
        
        foreach ($allowed_fields as $field => $type) {
            if (isset($data[$field])) {
                $sanitized[$field] = self::sanitize_field($data[$field], $type);
            }
        }
        
        return $sanitized;
    }

    /**
     * Sanitize individual field
     *
     * @param mixed $value Field value
     * @param string $type Field type
     * @return mixed Sanitized value
     */
    private static function sanitize_field($value, $type) {
        switch ($type) {
            case 'int':
                return intval($value);
                
            case 'email':
                return sanitize_email($value);
                
            case 'text':
                return sanitize_text_field($value);
                
            case 'textarea':
                return sanitize_textarea_field($value);
                
            default:
                return sanitize_text_field($value);
        }
    }

    /**
     * Validate order data before processing
     *
     * @param WC_Order $order Order object
     * @return array Validation result
     */
    public static function validate_order_data($order) {
        $errors = [];
        
        // Check order exists and is valid
        if (!$order || !$order->get_id()) {
            $errors[] = __('Invalid order', 'payop-woocommerce');
            return ['valid' => false, 'errors' => $errors];
        }
        
        // Check order status
        if ($order->get_status() === 'completed') {
            $errors[] = __('Order is already completed', 'payop-woocommerce');
        }
        
        // Check order total
        if ($order->get_total() <= 0) {
            $errors[] = __('Invalid order total', 'payop-woocommerce');
        }
        
        // Check currency
        $supported_currencies = ['EUR', 'USD', 'GBP', 'CAD', 'AUD', 'BRL', 'DKK', 'PHP'];
        if (!in_array($order->get_currency(), $supported_currencies)) {
            $errors[] = sprintf(__('Currency %s is not supported', 'payop-woocommerce'), $order->get_currency());
        }
        
        // Check customer email
        if (!is_email($order->get_billing_email())) {
            $errors[] = __('Invalid customer email', 'payop-woocommerce');
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Rate limiting for API requests
     *
     * @param string $key Rate limit key
     * @param int $limit Request limit
     * @param int $window Time window in seconds
     * @return bool True if within limits
     */
    public static function check_rate_limit($key, $limit = 60, $window = 3600) {
        $transient_key = 'payop_rate_limit_' . md5($key);
        $current_count = get_transient($transient_key);
        
        if ($current_count === false) {
            // First request in window
            set_transient($transient_key, 1, $window);
            return true;
        }
        
        if ($current_count >= $limit) {
            return false;
        }
        
        // Increment counter
        set_transient($transient_key, $current_count + 1, $window);
        return true;
    }

    /**
     * Log security events
     *
     * @param string $event Event type
     * @param array $data Event data
     * @param string $level Log level
     */
    public static function log_security_event($event, $data = [], $level = 'warning') {
        $logger = wc_get_logger();
        
        $message = sprintf(
            'PayOp Security Event: %s | IP: %s | Data: %s',
            $event,
            self::get_client_ip(),
            wp_json_encode($data)
        );
        
        $logger->log($level, $message, ['source' => 'payop-security']);
    }

    /**
     * Validate webhook timestamp to prevent replay attacks
     *
     * @param int $timestamp Webhook timestamp
     * @param int $tolerance Tolerance in seconds (default 5 minutes)
     * @return bool
     */
    public static function validate_webhook_timestamp($timestamp, $tolerance = 300) {
        $current_time = time();
        $time_diff = abs($current_time - $timestamp);
        
        return $time_diff <= $tolerance;
    }

    /**
     * Generate secure random string
     *
     * @param int $length String length
     * @return string
     */
    public static function generate_secure_string($length = 32) {
        if (function_exists('random_bytes')) {
            return bin2hex(random_bytes($length / 2));
        }
        
        return wp_generate_password($length, false);
    }

    /**
     * Validate CSRF token
     *
     * @param string $token Token to validate
     * @param string $action Action name
     * @return bool
     */
    public static function validate_csrf_token($token, $action) {
        return wp_verify_nonce($token, $action);
    }

    /**
     * Encrypt sensitive data
     *
     * @param string $data Data to encrypt
     * @param string $key Encryption key
     * @return string
     */
    public static function encrypt_data($data, $key) {
        if (empty($data)) {
            return $data;
        }
        
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }

    /**
     * Decrypt sensitive data
     *
     * @param string $encrypted_data Encrypted data
     * @param string $key Encryption key
     * @return string
     */
    public static function decrypt_data($encrypted_data, $key) {
        if (empty($encrypted_data)) {
            return $encrypted_data;
        }
        
        $data = base64_decode($encrypted_data);
        
        if ($data === false) {
            return $encrypted_data;
        }
        
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
        
        return $decrypted !== false ? $decrypted : $encrypted_data;
    }

    /**
     * Check if request is from admin area
     *
     * @return bool
     */
    public static function is_admin_request() {
        return is_admin() && current_user_can('manage_woocommerce');
    }

    /**
     * Validate user permissions for PayOp operations
     *
     * @param string $operation Operation type
     * @return bool
     */
    public static function validate_user_permissions($operation) {
        switch ($operation) {
            case 'manage_settings':
                return current_user_can('manage_woocommerce');
                
            case 'view_transactions':
                return current_user_can('view_woocommerce_reports');
                
            case 'process_payment':
                // Payment processing doesn't require user login
                return true;
                
            default:
                return false;
        }
    }

    /**
     * Sanitize file upload
     *
     * @param array $file File data from $_FILES
     * @return array Validation result
     */
    public static function validate_file_upload($file) {
        $allowed_types = ['text/plain', 'application/json'];
        $max_size = 1024 * 1024; // 1MB
        
        $errors = [];
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = __('File upload error', 'payop-woocommerce');
        }
        
        if ($file['size'] > $max_size) {
            $errors[] = __('File too large', 'payop-woocommerce');
        }
        
        if (!in_array($file['type'], $allowed_types)) {
            $errors[] = __('Invalid file type', 'payop-woocommerce');
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Clean up expired security data
     */
    public static function cleanup_security_data() {
        global $wpdb;
        
        // Clean up old rate limit transients
        $wpdb->query(
            "DELETE FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_payop_rate_limit_%' 
             AND option_value < " . time()
        );
        
        // Clean up old transaction logs (older than 90 days)
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->prefix}payop_transactions 
                 WHERE created_at < %s",
                date('Y-m-d H:i:s', strtotime('-90 days'))
            )
        );
    }
}
