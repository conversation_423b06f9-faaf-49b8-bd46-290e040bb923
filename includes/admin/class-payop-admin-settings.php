<?php
/**
 * PayOp Admin Settings
 *
 * @package PayOp\WooCommerce\Admin
 */

namespace PayOp\WooCommerce\Admin;

use PayOp\WooCommerce\Utils\PayOp_Config;
use PayOp\WooCommerce\API\PayOp_API_Client;
use PayOp\WooCommerce\API\PayOp_Payment_Methods;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Admin Settings
 * 
 * Handles admin interface and settings management
 */
class PayOp_Admin_Settings {

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);
        add_action('wp_ajax_payop_test_connection', [$this, 'test_api_connection']);
        add_action('wp_ajax_payop_refresh_methods', [$this, 'refresh_payment_methods']);
        add_action('wp_ajax_payop_get_method_stats', [$this, 'get_method_statistics']);
        add_action('admin_notices', [$this, 'show_admin_notices']);
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('PayOp Settings', 'payop-woocommerce'),
            __('PayOp Settings', 'payop-woocommerce'),
            'manage_woocommerce',
            'payop-settings',
            [$this, 'admin_page']
        );
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'payop-settings') === false) {
            return;
        }

        wp_enqueue_script(
            'payop-admin',
            PAYOP_WC_PLUGIN_URL . 'assets/js/admin.js',
            ['jquery'],
            PAYOP_WC_VERSION,
            true
        );

        wp_localize_script('payop-admin', 'payopAdmin', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('payop_admin'),
            'strings' => [
                'testing' => __('Testing connection...', 'payop-woocommerce'),
                'success' => __('Connection successful!', 'payop-woocommerce'),
                'error' => __('Connection failed', 'payop-woocommerce'),
                'refreshing' => __('Refreshing payment methods...', 'payop-woocommerce'),
                'refreshed' => __('Payment methods refreshed!', 'payop-woocommerce')
            ]
        ]);

        wp_enqueue_style(
            'payop-admin',
            PAYOP_WC_PLUGIN_URL . 'assets/css/admin.css',
            [],
            PAYOP_WC_VERSION
        );
    }

    /**
     * Admin page content
     */
    public function admin_page() {
        $active_tab = $_GET['tab'] ?? 'settings';
        
        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('PayOp Payment Gateway Settings', 'payop-woocommerce'); ?></h1>
            
            <nav class="nav-tab-wrapper">
                <a href="?page=payop-settings&tab=settings" class="nav-tab <?php echo $active_tab === 'settings' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Settings', 'payop-woocommerce'); ?>
                </a>
                <a href="?page=payop-settings&tab=methods" class="nav-tab <?php echo $active_tab === 'methods' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Payment Methods', 'payop-woocommerce'); ?>
                </a>
                <a href="?page=payop-settings&tab=transactions" class="nav-tab <?php echo $active_tab === 'transactions' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Transactions', 'payop-woocommerce'); ?>
                </a>
                <a href="?page=payop-settings&tab=logs" class="nav-tab <?php echo $active_tab === 'logs' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Logs', 'payop-woocommerce'); ?>
                </a>
            </nav>

            <div class="tab-content">
                <?php
                switch ($active_tab) {
                    case 'settings':
                        $this->render_settings_tab();
                        break;
                    case 'methods':
                        $this->render_methods_tab();
                        break;
                    case 'transactions':
                        $this->render_transactions_tab();
                        break;
                    case 'logs':
                        $this->render_logs_tab();
                        break;
                }
                ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render settings tab
     */
    private function render_settings_tab() {
        $credentials = PayOp_Config::get_api_credentials();
        
        if ($_POST && wp_verify_nonce($_POST['payop_settings_nonce'], 'payop_settings')) {
            $this->save_settings();
        }
        
        ?>
        <form method="post" action="">
            <?php wp_nonce_field('payop_settings', 'payop_settings_nonce'); ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row"><?php esc_html_e('Public Key', 'payop-woocommerce'); ?></th>
                    <td>
                        <input type="text" name="public_key" value="<?php echo esc_attr($credentials['public_key']); ?>" class="regular-text" />
                        <p class="description"><?php esc_html_e('Your PayOp public key (e.g., application-606)', 'payop-woocommerce'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Secret Key', 'payop-woocommerce'); ?></th>
                    <td>
                        <input type="password" name="secret_key" value="<?php echo esc_attr($credentials['secret_key']); ?>" class="regular-text" />
                        <p class="description"><?php esc_html_e('Your PayOp secret key for signature generation', 'payop-woocommerce'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('JWT Token', 'payop-woocommerce'); ?></th>
                    <td>
                        <textarea name="jwt_token" rows="3" class="large-text"><?php echo esc_textarea($credentials['jwt_token']); ?></textarea>
                        <p class="description"><?php esc_html_e('Your PayOp JWT token for API authentication', 'payop-woocommerce'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Project ID', 'payop-woocommerce'); ?></th>
                    <td>
                        <input type="text" name="project_id" value="<?php echo esc_attr($credentials['project_id']); ?>" class="regular-text" />
                        <p class="description"><?php esc_html_e('Your PayOp project ID (e.g., 606)', 'payop-woocommerce'); ?></p>
                    </td>
                </tr>
            </table>

            <h3><?php esc_html_e('Debug Settings', 'payop-woocommerce'); ?></h3>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php esc_html_e('Enable Debug Logging', 'payop-woocommerce'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="debug_enabled" value="1" <?php checked(PayOp_Config::get('debug_enabled')); ?> />
                            <?php esc_html_e('Enable detailed logging for debugging', 'payop-woocommerce'); ?>
                        </label>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <input type="submit" name="submit" class="button-primary" value="<?php esc_attr_e('Save Settings', 'payop-woocommerce'); ?>" />
                <button type="button" id="test-connection" class="button"><?php esc_html_e('Test Connection', 'payop-woocommerce'); ?></button>
            </p>
        </form>

        <div id="connection-result" style="display:none;"></div>
        <?php
    }

    /**
     * Render payment methods tab
     */
    private function render_methods_tab() {
        ?>
        <div class="payop-methods-header">
            <h3><?php esc_html_e('Payment Methods Management', 'payop-woocommerce'); ?></h3>
            <button type="button" id="refresh-methods" class="button"><?php esc_html_e('Refresh Methods', 'payop-woocommerce'); ?></button>
        </div>

        <div id="methods-stats" class="payop-stats-grid">
            <!-- Stats will be loaded via AJAX -->
        </div>

        <div id="methods-list">
            <!-- Methods will be loaded via AJAX -->
        </div>
        <?php
    }

    /**
     * Render transactions tab
     */
    private function render_transactions_tab() {
        global $wpdb;
        
        $transactions = $wpdb->get_results(
            "SELECT t.*, p.post_title as order_title 
             FROM {$wpdb->prefix}payop_transactions t 
             LEFT JOIN {$wpdb->posts} p ON t.order_id = p.ID 
             ORDER BY t.created_at DESC 
             LIMIT 50"
        );
        
        ?>
        <h3><?php esc_html_e('Recent Transactions', 'payop-woocommerce'); ?></h3>
        
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php esc_html_e('Order ID', 'payop-woocommerce'); ?></th>
                    <th><?php esc_html_e('Invoice ID', 'payop-woocommerce'); ?></th>
                    <th><?php esc_html_e('Transaction ID', 'payop-woocommerce'); ?></th>
                    <th><?php esc_html_e('Payment Method', 'payop-woocommerce'); ?></th>
                    <th><?php esc_html_e('Status', 'payop-woocommerce'); ?></th>
                    <th><?php esc_html_e('Created', 'payop-woocommerce'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($transactions)): ?>
                    <tr>
                        <td colspan="6"><?php esc_html_e('No transactions found.', 'payop-woocommerce'); ?></td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($transactions as $transaction): ?>
                        <tr>
                            <td>
                                <a href="<?php echo esc_url(admin_url('post.php?post=' . $transaction->order_id . '&action=edit')); ?>">
                                    #<?php echo esc_html($transaction->order_id); ?>
                                </a>
                            </td>
                            <td><?php echo esc_html($transaction->invoice_id); ?></td>
                            <td><?php echo esc_html($transaction->transaction_id ?: '-'); ?></td>
                            <td><?php echo esc_html($transaction->payment_method_id); ?></td>
                            <td>
                                <span class="status-<?php echo esc_attr($transaction->status); ?>">
                                    <?php echo esc_html(ucfirst($transaction->status)); ?>
                                </span>
                            </td>
                            <td><?php echo esc_html($transaction->created_at); ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
        <?php
    }

    /**
     * Render logs tab
     */
    private function render_logs_tab() {
        $log_file = WC_Log_Handler_File::get_log_file_path('payop');
        
        ?>
        <h3><?php esc_html_e('Debug Logs', 'payop-woocommerce'); ?></h3>
        
        <?php if (file_exists($log_file)): ?>
            <div class="payop-log-viewer">
                <textarea readonly rows="20" style="width: 100%; font-family: monospace;"><?php
                    echo esc_textarea(file_get_contents($log_file));
                ?></textarea>
            </div>
            <p>
                <a href="<?php echo esc_url(wp_nonce_url(admin_url('admin.php?page=payop-settings&tab=logs&action=clear'), 'clear_logs')); ?>" 
                   class="button" onclick="return confirm('<?php esc_attr_e('Are you sure you want to clear the logs?', 'payop-woocommerce'); ?>')">
                    <?php esc_html_e('Clear Logs', 'payop-woocommerce'); ?>
                </a>
            </p>
        <?php else: ?>
            <p><?php esc_html_e('No log file found. Enable debug logging to see logs here.', 'payop-woocommerce'); ?></p>
        <?php endif; ?>
        
        <?php
        if (isset($_GET['action']) && $_GET['action'] === 'clear' && wp_verify_nonce($_GET['_wpnonce'], 'clear_logs')) {
            if (file_exists($log_file)) {
                file_put_contents($log_file, '');
                echo '<div class="notice notice-success"><p>' . esc_html__('Logs cleared successfully.', 'payop-woocommerce') . '</p></div>';
            }
        }
    }

    /**
     * Save settings
     */
    private function save_settings() {
        $credentials = [
            'public_key' => sanitize_text_field($_POST['public_key']),
            'secret_key' => sanitize_text_field($_POST['secret_key']),
            'jwt_token' => sanitize_textarea_field($_POST['jwt_token']),
            'project_id' => sanitize_text_field($_POST['project_id'])
        ];

        $validation_errors = PayOp_Config::validate_api_credentials($credentials);
        
        if (!empty($validation_errors)) {
            foreach ($validation_errors as $error) {
                add_settings_error('payop_settings', 'validation_error', $error);
            }
            return;
        }

        PayOp_Config::set_api_credentials($credentials);
        PayOp_Config::set('debug_enabled', !empty($_POST['debug_enabled']));

        add_settings_error('payop_settings', 'settings_saved', __('Settings saved successfully.', 'payop-woocommerce'), 'updated');
    }

    /**
     * Test API connection via AJAX
     */
    public function test_api_connection() {
        if (!wp_verify_nonce($_POST['nonce'], 'payop_admin')) {
            wp_send_json_error(['message' => __('Security check failed', 'payop-woocommerce')]);
        }

        $credentials = PayOp_Config::get_api_credentials();
        
        if (empty($credentials['public_key']) || empty($credentials['jwt_token'])) {
            wp_send_json_error(['message' => __('Please configure API credentials first', 'payop-woocommerce')]);
        }

        $api_client = new PayOp_API_Client(
            $credentials['public_key'],
            $credentials['secret_key'],
            $credentials['jwt_token'],
            $credentials['project_id'],
            true
        );

        $result = $api_client->get_payment_methods();
        
        if (is_wp_error($result)) {
            wp_send_json_error(['message' => $result->get_error_message()]);
        }

        wp_send_json_success([
            'message' => __('Connection successful!', 'payop-woocommerce'),
            'methods_count' => count($result['data'] ?? [])
        ]);
    }

    /**
     * Refresh payment methods via AJAX
     */
    public function refresh_payment_methods() {
        if (!wp_verify_nonce($_POST['nonce'], 'payop_admin')) {
            wp_send_json_error(['message' => __('Security check failed', 'payop-woocommerce')]);
        }

        $credentials = PayOp_Config::get_api_credentials();
        $api_client = new PayOp_API_Client(
            $credentials['public_key'],
            $credentials['secret_key'],
            $credentials['jwt_token'],
            $credentials['project_id']
        );

        $payment_methods = new PayOp_Payment_Methods($api_client);
        $result = $payment_methods->get_payment_methods(true); // Force refresh

        if (is_wp_error($result)) {
            wp_send_json_error(['message' => $result->get_error_message()]);
        }

        wp_send_json_success([
            'message' => __('Payment methods refreshed successfully!', 'payop-woocommerce'),
            'count' => count($result)
        ]);
    }

    /**
     * Get method statistics via AJAX
     */
    public function get_method_statistics() {
        if (!wp_verify_nonce($_POST['nonce'], 'payop_admin')) {
            wp_send_json_error(['message' => __('Security check failed', 'payop-woocommerce')]);
        }

        $credentials = PayOp_Config::get_api_credentials();
        $api_client = new PayOp_API_Client(
            $credentials['public_key'],
            $credentials['secret_key'],
            $credentials['jwt_token'],
            $credentials['project_id']
        );

        $payment_methods = new PayOp_Payment_Methods($api_client);
        $stats = $payment_methods->get_statistics();

        wp_send_json_success($stats);
    }

    /**
     * Show admin notices
     */
    public function show_admin_notices() {
        settings_errors('payop_settings');
    }
}
