<?php
/**
 * PayOp Checkout Block
 *
 * @package PayOp\WooCommerce\Blocks
 */

namespace PayOp\WooCommerce\Blocks;

use Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType;
use PayOp\WooCommerce\Utils\PayOp_Config;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PayOp Checkout Block Integration
 * 
 * Integrates PayOp payment gateway with WooCommerce Blocks checkout
 */
class PayOp_Checkout_Block extends AbstractPaymentMethodType {

    /**
     * Payment method name
     *
     * @var string
     */
    protected $name = 'payop';

    /**
     * Initialize the payment method type
     */
    public function initialize() {
        $this->settings = get_option('woocommerce_payop_settings', []);
    }

    /**
     * Returns if this payment method should be active
     *
     * @return boolean
     */
    public function is_active() {
        return !empty($this->settings['enabled']) && 'yes' === $this->settings['enabled'];
    }

    /**
     * Returns an array of scripts/handles to be registered for this payment method
     *
     * @return array
     */
    public function get_payment_method_script_handles() {
        $script_path = '/assets/js/blocks/checkout-block.js';
        $script_asset_path = PAYOP_WC_PLUGIN_DIR . 'assets/js/blocks/checkout-block.asset.php';
        $script_asset = file_exists($script_asset_path)
            ? require $script_asset_path
            : [
                'dependencies' => [],
                'version' => PAYOP_WC_VERSION
            ];

        wp_register_script(
            'payop-checkout-block',
            PAYOP_WC_PLUGIN_URL . $script_path,
            $script_asset['dependencies'],
            $script_asset['version'],
            true
        );

        // Localize script with necessary data
        wp_localize_script(
            'payop-checkout-block',
            'payopBlockData',
            [
                'title' => $this->get_setting('title'),
                'description' => $this->get_setting('description'),
                'supports' => $this->get_supported_features(),
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('payop_checkout'),
                'isTestMode' => $this->is_test_mode(),
                'icons' => $this->get_payment_method_icons()
            ]
        );

        return ['payop-checkout-block'];
    }

    /**
     * Returns an array of key=>value pairs of data made available to the payment methods script
     *
     * @return array
     */
    public function get_payment_method_data() {
        return [
            'title' => $this->get_setting('title'),
            'description' => $this->get_setting('description'),
            'supports' => $this->get_supported_features(),
            'showSaveOption' => false, // PayOp doesn't support saved payment methods
            'showDescription' => !empty($this->get_setting('description')),
        ];
    }

    /**
     * Get supported features
     *
     * @return array
     */
    private function get_supported_features() {
        return [
            'products'
            // Note: No subscriptions, refunds handled manually
        ];
    }

    /**
     * Check if in test mode
     *
     * @return bool
     */
    private function is_test_mode() {
        // PayOp doesn't have a separate test mode, but we can check debug setting
        return 'yes' === $this->get_setting('debug');
    }

    /**
     * Get payment method icons
     *
     * @return array
     */
    private function get_payment_method_icons() {
        return [
            'cards' => PAYOP_WC_PLUGIN_URL . 'assets/images/cards.png',
            'bank' => PAYOP_WC_PLUGIN_URL . 'assets/images/bank.png',
            'ewallet' => PAYOP_WC_PLUGIN_URL . 'assets/images/ewallet.png',
            'cash' => PAYOP_WC_PLUGIN_URL . 'assets/images/cash.png',
            'crypto' => PAYOP_WC_PLUGIN_URL . 'assets/images/crypto.png'
        ];
    }

    /**
     * Get setting value
     *
     * @param string $key Setting key
     * @return mixed
     */
    private function get_setting($key) {
        return $this->settings[$key] ?? '';
    }
}

/**
 * Register PayOp Checkout Block
 */
function register_payop_checkout_block() {
    // Check if WooCommerce Blocks is available
    if (!class_exists('Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType')) {
        return;
    }

    // Register the payment method type
    add_action(
        'woocommerce_blocks_payment_method_type_registration',
        function($payment_method_registry) {
            $payment_method_registry->register(new PayOp_Checkout_Block());
        }
    );
}

// Initialize block registration
add_action('woocommerce_blocks_loaded', 'PayOp\WooCommerce\Blocks\register_payop_checkout_block');
