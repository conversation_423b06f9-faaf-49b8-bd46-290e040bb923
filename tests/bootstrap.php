<?php
/**
 * PayOp WooCommerce Plugin Test Bootstrap
 *
 * @package PayOp\WooCommerce\Tests
 */

// Define test environment
define('PAYOP_WC_TESTING', true);

// WordPress test environment
$_tests_dir = getenv('WP_TESTS_DIR');
if (!$_tests_dir) {
    $_tests_dir = '/tmp/wordpress-tests-lib';
}

// Give access to tests_add_filter() function
require_once $_tests_dir . '/includes/functions.php';

/**
 * Manually load the plugin being tested
 */
function _manually_load_plugin() {
    // Load WooCommerce first
    require_once dirname(__FILE__) . '/../../../woocommerce/woocommerce.php';
    
    // Load PayOp plugin
    require_once dirname(__FILE__) . '/../payop-woocommerce-gateway.php';
}
tests_add_filter('muplugins_loaded', '_manually_load_plugin');

// Start up the WP testing environment
require $_tests_dir . '/includes/bootstrap.php';

// Load WooCommerce test framework
require_once dirname(__FILE__) . '/../../../woocommerce/tests/framework/class-wc-unit-test-case.php';

/**
 * PayOp Test Case Base Class
 */
abstract class PayOp_Unit_Test_Case extends WC_Unit_Test_Case {

    /**
     * Test credentials
     */
    protected $test_credentials = [
        'public_key' => 'application-606',
        'secret_key' => 'fd6d7b9d6e14146ba064cd3b7afd7a0e',
        'jwt_token' => 'test_jwt_token',
        'project_id' => '606'
    ];

    /**
     * Setup test environment
     */
    public function setUp(): void {
        parent::setUp();
        
        // Set test credentials
        \PayOp\WooCommerce\Utils\PayOp_Config::set_api_credentials($this->test_credentials);
        \PayOp\WooCommerce\Utils\PayOp_Config::set('debug_enabled', true);
        
        // Enable PayOp gateway
        update_option('woocommerce_payop_enabled', 'yes');
    }

    /**
     * Create test order
     *
     * @param array $args Order arguments
     * @return WC_Order
     */
    protected function create_test_order($args = []) {
        $default_args = [
            'status' => 'pending',
            'customer_id' => 1,
            'billing_email' => '<EMAIL>',
            'billing_first_name' => 'John',
            'billing_last_name' => 'Doe',
            'billing_country' => 'US',
            'currency' => 'USD',
            'total' => '100.00'
        ];

        $args = array_merge($default_args, $args);
        
        $order = wc_create_order($args);
        
        // Add a product
        $product = WC_Helper_Product::create_simple_product();
        $order->add_product($product, 1);
        $order->calculate_totals();
        $order->save();

        return $order;
    }

    /**
     * Create mock API response
     *
     * @param array $data Response data
     * @param int $status_code HTTP status code
     * @return array
     */
    protected function create_mock_api_response($data, $status_code = 200) {
        return [
            'response' => ['code' => $status_code],
            'body' => wp_json_encode($data)
        ];
    }

    /**
     * Mock HTTP request
     *
     * @param string $url Request URL
     * @param array $response Mock response
     */
    protected function mock_http_request($url, $response) {
        add_filter('pre_http_request', function($preempt, $args, $request_url) use ($url, $response) {
            if (strpos($request_url, $url) !== false) {
                return $response;
            }
            return $preempt;
        }, 10, 3);
    }

    /**
     * Assert order status
     *
     * @param WC_Order $order Order object
     * @param string $expected_status Expected status
     */
    protected function assertOrderStatus($order, $expected_status) {
        $order = wc_get_order($order->get_id()); // Refresh order
        $this->assertEquals($expected_status, $order->get_status());
    }

    /**
     * Assert transaction record exists
     *
     * @param int $order_id Order ID
     * @param string $status Expected status
     */
    protected function assertTransactionExists($order_id, $status = null) {
        global $wpdb;
        
        $query = "SELECT * FROM {$wpdb->prefix}payop_transactions WHERE order_id = %d";
        $params = [$order_id];
        
        if ($status) {
            $query .= " AND status = %s";
            $params[] = $status;
        }
        
        $transaction = $wpdb->get_row($wpdb->prepare($query, $params));
        $this->assertNotNull($transaction, 'Transaction record should exist');
        
        if ($status) {
            $this->assertEquals($status, $transaction->status);
        }
    }

    /**
     * Clean up after test
     */
    public function tearDown(): void {
        // Clean up test data
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->prefix}payop_transactions WHERE order_id IN (SELECT ID FROM {$wpdb->posts} WHERE post_type = 'shop_order')");
        
        parent::tearDown();
    }
}
