<?php
/**
 * PayOp Security Tests
 *
 * @package PayOp\WooCommerce\Tests
 */

class Test_PayOp_Security extends PayOp_Unit_Test_Case {

    /**
     * Test IPN signature validation
     */
    public function test_ipn_signature_validation() {
        $data = [
            'invoiceId' => 'test-invoice-123',
            'amount' => '100.00',
            'currency' => 'USD',
            'status' => 'success'
        ];

        $secret_key = 'test-secret-key';
        $signature = \PayOp\WooCommerce\Utils\PayOp_Security::generate_ipn_signature($data, $secret_key);

        $this->assertTrue(
            \PayOp\WooCommerce\Utils\PayOp_Security::validate_ipn_signature($data, $signature, $secret_key)
        );
    }

    /**
     * Test IPN signature validation failure
     */
    public function test_ipn_signature_validation_failure() {
        $data = [
            'invoiceId' => 'test-invoice-123',
            'amount' => '100.00',
            'currency' => 'USD',
            'status' => 'success'
        ];

        $secret_key = 'test-secret-key';
        $wrong_signature = 'wrong-signature';

        $this->assertFalse(
            \PayOp\WooCommerce\Utils\PayOp_Security::validate_ipn_signature($data, $wrong_signature, $secret_key)
        );
    }

    /**
     * Test IP validation for allowed IPs
     */
    public function test_ip_validation_allowed() {
        $allowed_ip = '*************';
        $this->assertTrue(\PayOp\WooCommerce\Utils\PayOp_Security::validate_ipn_ip($allowed_ip));
    }

    /**
     * Test IP validation for disallowed IPs
     */
    public function test_ip_validation_disallowed() {
        $disallowed_ip = '***********';
        $this->assertFalse(\PayOp\WooCommerce\Utils\PayOp_Security::validate_ipn_ip($disallowed_ip));
    }

    /**
     * Test payment data sanitization
     */
    public function test_payment_data_sanitization() {
        $raw_data = [
            'payop_payment_method' => '1',
            'email' => '<EMAIL>',
            'name' => 'John Doe',
            'phone' => '+1234567890',
            'document' => '123456789',
            'malicious_field' => '<script>alert("xss")</script>',
            'sql_injection' => "'; DROP TABLE users; --"
        ];

        $sanitized = \PayOp\WooCommerce\Utils\PayOp_Security::sanitize_payment_data($raw_data);

        $this->assertEquals(1, $sanitized['payop_payment_method']);
        $this->assertEquals('<EMAIL>', $sanitized['email']);
        $this->assertEquals('John Doe', $sanitized['name']);
        $this->assertEquals('+1234567890', $sanitized['phone']);
        $this->assertEquals('123456789', $sanitized['document']);
        $this->assertArrayNotHasKey('malicious_field', $sanitized);
        $this->assertArrayNotHasKey('sql_injection', $sanitized);
    }

    /**
     * Test order data validation success
     */
    public function test_order_data_validation_success() {
        $order = $this->create_test_order([
            'total' => '100.00',
            'currency' => 'USD',
            'billing_email' => '<EMAIL>'
        ]);

        $result = \PayOp\WooCommerce\Utils\PayOp_Security::validate_order_data($order);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
    }

    /**
     * Test order data validation with invalid total
     */
    public function test_order_data_validation_invalid_total() {
        $order = $this->create_test_order(['total' => '0.00']);

        $result = \PayOp\WooCommerce\Utils\PayOp_Security::validate_order_data($order);

        $this->assertFalse($result['valid']);
        $this->assertContains('Invalid order total', $result['errors']);
    }

    /**
     * Test order data validation with unsupported currency
     */
    public function test_order_data_validation_unsupported_currency() {
        $order = $this->create_test_order(['currency' => 'XYZ']);

        $result = \PayOp\WooCommerce\Utils\PayOp_Security::validate_order_data($order);

        $this->assertFalse($result['valid']);
        $this->assertContains('Currency XYZ is not supported', $result['errors']);
    }

    /**
     * Test order data validation with invalid email
     */
    public function test_order_data_validation_invalid_email() {
        $order = $this->create_test_order(['billing_email' => 'invalid-email']);

        $result = \PayOp\WooCommerce\Utils\PayOp_Security::validate_order_data($order);

        $this->assertFalse($result['valid']);
        $this->assertContains('Invalid customer email', $result['errors']);
    }

    /**
     * Test rate limiting
     */
    public function test_rate_limiting() {
        $key = 'test-key';
        $limit = 3;
        $window = 60;

        // First 3 requests should pass
        for ($i = 0; $i < $limit; $i++) {
            $this->assertTrue(\PayOp\WooCommerce\Utils\PayOp_Security::check_rate_limit($key, $limit, $window));
        }

        // 4th request should fail
        $this->assertFalse(\PayOp\WooCommerce\Utils\PayOp_Security::check_rate_limit($key, $limit, $window));
    }

    /**
     * Test webhook timestamp validation
     */
    public function test_webhook_timestamp_validation() {
        $current_time = time();
        
        // Valid timestamp (within tolerance)
        $this->assertTrue(\PayOp\WooCommerce\Utils\PayOp_Security::validate_webhook_timestamp($current_time));
        $this->assertTrue(\PayOp\WooCommerce\Utils\PayOp_Security::validate_webhook_timestamp($current_time - 100));

        // Invalid timestamp (outside tolerance)
        $this->assertFalse(\PayOp\WooCommerce\Utils\PayOp_Security::validate_webhook_timestamp($current_time - 400));
        $this->assertFalse(\PayOp\WooCommerce\Utils\PayOp_Security::validate_webhook_timestamp($current_time + 400));
    }

    /**
     * Test secure string generation
     */
    public function test_secure_string_generation() {
        $string1 = \PayOp\WooCommerce\Utils\PayOp_Security::generate_secure_string(32);
        $string2 = \PayOp\WooCommerce\Utils\PayOp_Security::generate_secure_string(32);

        $this->assertEquals(32, strlen($string1));
        $this->assertEquals(32, strlen($string2));
        $this->assertNotEquals($string1, $string2);
    }

    /**
     * Test CSRF token validation
     */
    public function test_csrf_token_validation() {
        $action = 'test_action';
        $token = wp_create_nonce($action);

        $this->assertTrue(\PayOp\WooCommerce\Utils\PayOp_Security::validate_csrf_token($token, $action));
        $this->assertFalse(\PayOp\WooCommerce\Utils\PayOp_Security::validate_csrf_token('invalid_token', $action));
    }

    /**
     * Test data encryption and decryption
     */
    public function test_data_encryption_decryption() {
        $data = 'sensitive data';
        $key = 'encryption-key-123';

        $encrypted = \PayOp\WooCommerce\Utils\PayOp_Security::encrypt_data($data, $key);
        $decrypted = \PayOp\WooCommerce\Utils\PayOp_Security::decrypt_data($encrypted, $key);

        $this->assertNotEquals($data, $encrypted);
        $this->assertEquals($data, $decrypted);
    }

    /**
     * Test user permission validation
     */
    public function test_user_permission_validation() {
        // Test without user
        $this->assertTrue(\PayOp\WooCommerce\Utils\PayOp_Security::validate_user_permissions('process_payment'));
        $this->assertFalse(\PayOp\WooCommerce\Utils\PayOp_Security::validate_user_permissions('manage_settings'));

        // Test with admin user
        $admin_user = $this->factory->user->create(['role' => 'administrator']);
        wp_set_current_user($admin_user);

        $this->assertTrue(\PayOp\WooCommerce\Utils\PayOp_Security::validate_user_permissions('manage_settings'));
        $this->assertTrue(\PayOp\WooCommerce\Utils\PayOp_Security::validate_user_permissions('view_transactions'));
    }

    /**
     * Test file upload validation
     */
    public function test_file_upload_validation() {
        // Valid file
        $valid_file = [
            'error' => UPLOAD_ERR_OK,
            'size' => 1024,
            'type' => 'text/plain'
        ];

        $result = \PayOp\WooCommerce\Utils\PayOp_Security::validate_file_upload($valid_file);
        $this->assertTrue($result['valid']);

        // Invalid file - too large
        $invalid_file = [
            'error' => UPLOAD_ERR_OK,
            'size' => 2 * 1024 * 1024, // 2MB
            'type' => 'text/plain'
        ];

        $result = \PayOp\WooCommerce\Utils\PayOp_Security::validate_file_upload($invalid_file);
        $this->assertFalse($result['valid']);
        $this->assertContains('File too large', $result['errors']);

        // Invalid file - wrong type
        $invalid_file = [
            'error' => UPLOAD_ERR_OK,
            'size' => 1024,
            'type' => 'application/x-executable'
        ];

        $result = \PayOp\WooCommerce\Utils\PayOp_Security::validate_file_upload($invalid_file);
        $this->assertFalse($result['valid']);
        $this->assertContains('Invalid file type', $result['errors']);
    }

    /**
     * Test client IP detection
     */
    public function test_client_ip_detection() {
        // Test with REMOTE_ADDR
        $_SERVER['REMOTE_ADDR'] = '***********';
        $ip = \PayOp\WooCommerce\Utils\PayOp_Security::get_client_ip();
        $this->assertEquals('***********', $ip);

        // Test with X-Forwarded-For
        $_SERVER['HTTP_X_FORWARDED_FOR'] = '***********, ***********';
        $ip = \PayOp\WooCommerce\Utils\PayOp_Security::get_client_ip();
        $this->assertEquals('***********', $ip);

        // Test with X-Real-IP
        $_SERVER['HTTP_X_REAL_IP'] = '***********';
        $ip = \PayOp\WooCommerce\Utils\PayOp_Security::get_client_ip();
        $this->assertEquals('***********', $ip);
    }
}
