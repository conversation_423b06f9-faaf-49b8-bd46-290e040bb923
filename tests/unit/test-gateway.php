<?php
/**
 * PayOp Gateway Tests
 *
 * @package PayOp\WooCommerce\Tests
 */

class Test_PayOp_Gateway extends PayOp_Unit_Test_Case {

    /**
     * Gateway instance
     *
     * @var \PayOp\WooCommerce\Gateway\PayOp_Gateway
     */
    private $gateway;

    /**
     * Setup test
     */
    public function setUp(): void {
        parent::setUp();
        
        $this->gateway = new \PayOp\WooCommerce\Gateway\PayOp_Gateway();
    }

    /**
     * Test gateway initialization
     */
    public function test_gateway_initialization() {
        $this->assertEquals('payop', $this->gateway->id);
        $this->assertTrue($this->gateway->has_fields);
        $this->assertContains('products', $this->gateway->supports);
    }

    /**
     * Test gateway availability when enabled
     */
    public function test_gateway_available_when_enabled() {
        // Set required settings
        update_option('woocommerce_payop_enabled', 'yes');
        update_option('woocommerce_payop_public_key', 'test-key');
        update_option('woocommerce_payop_secret_key', 'test-secret');
        update_option('woocommerce_payop_jwt_token', 'test-token');
        update_option('woocommerce_payop_project_id', '123');

        $gateway = new \PayOp\WooCommerce\Gateway\PayOp_Gateway();
        $this->assertTrue($gateway->is_available());
    }

    /**
     * Test gateway not available when disabled
     */
    public function test_gateway_not_available_when_disabled() {
        update_option('woocommerce_payop_enabled', 'no');
        
        $gateway = new \PayOp\WooCommerce\Gateway\PayOp_Gateway();
        $this->assertFalse($gateway->is_available());
    }

    /**
     * Test gateway not available with missing credentials
     */
    public function test_gateway_not_available_with_missing_credentials() {
        update_option('woocommerce_payop_enabled', 'yes');
        update_option('woocommerce_payop_public_key', '');
        
        $gateway = new \PayOp\WooCommerce\Gateway\PayOp_Gateway();
        $this->assertFalse($gateway->is_available());
    }

    /**
     * Test field validation success
     */
    public function test_validate_fields_success() {
        // Mock valid POST data
        $_POST['payop_payment_method'] = '1';
        $_POST['woocommerce-process-checkout-nonce'] = wp_create_nonce('woocommerce-process_checkout');

        $this->assertTrue($this->gateway->validate_fields());
    }

    /**
     * Test field validation failure - missing payment method
     */
    public function test_validate_fields_missing_payment_method() {
        $_POST['woocommerce-process-checkout-nonce'] = wp_create_nonce('woocommerce-process_checkout');
        
        $this->assertFalse($this->gateway->validate_fields());
    }

    /**
     * Test field validation failure - invalid nonce
     */
    public function test_validate_fields_invalid_nonce() {
        $_POST['payop_payment_method'] = '1';
        $_POST['woocommerce-process-checkout-nonce'] = 'invalid-nonce';

        $this->assertFalse($this->gateway->validate_fields());
    }

    /**
     * Test successful payment processing
     */
    public function test_process_payment_success() {
        // Create test order
        $order = $this->create_test_order();

        // Mock successful API responses
        $this->mock_successful_api_responses();

        // Mock POST data
        $_POST['payop_payment_method'] = '1';
        $_POST['payop_additional_fields'] = [];

        $result = $this->gateway->process_payment($order->get_id());

        $this->assertEquals('success', $result['result']);
        $this->assertArrayHasKey('redirect', $result);
        $this->assertOrderStatus($order, 'pending');
        $this->assertTransactionExists($order->get_id(), 'pending');
    }

    /**
     * Test payment processing with invalid order
     */
    public function test_process_payment_invalid_order() {
        $result = $this->gateway->process_payment(999999);

        $this->assertEquals('failure', $result['result']);
    }

    /**
     * Test payment processing with API error
     */
    public function test_process_payment_api_error() {
        $order = $this->create_test_order();

        // Mock API error
        $mock_response = $this->create_mock_api_response([
            'message' => 'API Error'
        ], 500);

        $this->mock_http_request('api.payop.com', $mock_response);

        $_POST['payop_payment_method'] = '1';
        $_POST['payop_additional_fields'] = [];

        $result = $this->gateway->process_payment($order->get_id());

        $this->assertEquals('failure', $result['result']);
    }

    /**
     * Test IPN handling
     */
    public function test_ipn_handling() {
        // Mock IPN data
        $ipn_data = [
            'invoiceId' => 'test-invoice-123',
            'txid' => 'test-transaction-123',
            'status' => 'success',
            'amount' => '100.00',
            'currency' => 'USD',
            'timestamp' => time()
        ];

        // Mock valid IP
        $_SERVER['REMOTE_ADDR'] = '*************';

        // Mock file_get_contents
        $this->mock_ipn_input(wp_json_encode($ipn_data));

        // Create order with invoice ID
        $order = $this->create_test_order();
        $order->update_meta_data('_payop_invoice_id', 'test-invoice-123');
        $order->save();

        // Create transaction record
        global $wpdb;
        $wpdb->insert(
            $wpdb->prefix . 'payop_transactions',
            [
                'order_id' => $order->get_id(),
                'invoice_id' => 'test-invoice-123',
                'payment_method_id' => 1,
                'status' => 'pending'
            ]
        );

        // Process IPN
        ob_start();
        $this->gateway->handle_ipn();
        $output = ob_get_clean();

        $this->assertEquals('OK', $output);
        $this->assertOrderStatus($order, 'processing');
    }

    /**
     * Test IPN with invalid IP
     */
    public function test_ipn_invalid_ip() {
        $_SERVER['REMOTE_ADDR'] = '***********';

        $this->mock_ipn_input('{}');

        ob_start();
        $this->gateway->handle_ipn();
        $output = ob_get_clean();

        $this->assertEquals('Security validation failed', $output);
    }

    /**
     * Test order creation with PayOp data
     */
    public function test_create_payop_invoice() {
        $order = $this->create_test_order();

        // Mock successful invoice creation
        $mock_response = $this->create_mock_api_response([
            'data' => 'invoice-123'
        ]);

        $this->mock_http_request('api.payop.com/v1/invoices/create', $mock_response);

        $reflection = new ReflectionClass($this->gateway);
        $method = $reflection->getMethod('create_payop_invoice');
        $method->setAccessible(true);

        $result = $method->invoke($this->gateway, $order, '1', []);

        $this->assertIsArray($result);
        $this->assertEquals('invoice-123', $result['invoice_id']);
    }

    /**
     * Test checkout creation
     */
    public function test_create_payop_checkout() {
        $order = $this->create_test_order();

        // Mock successful checkout creation
        $mock_response = $this->create_mock_api_response([
            'data' => [
                'txid' => 'transaction-123'
            ]
        ]);

        $this->mock_http_request('api.payop.com/v1/checkout/create', $mock_response);

        $reflection = new ReflectionClass($this->gateway);
        $method = $reflection->getMethod('create_payop_checkout');
        $method->setAccessible(true);

        $result = $method->invoke($this->gateway, 'invoice-123', $order, '1', []);

        $this->assertIsArray($result);
        $this->assertEquals('transaction-123', $result['data']['txid']);
    }

    /**
     * Test transaction data storage
     */
    public function test_store_transaction_data() {
        $order = $this->create_test_order();

        $invoice_result = ['invoice_id' => 'invoice-123'];
        $checkout_result = ['data' => ['txid' => 'transaction-123']];

        $reflection = new ReflectionClass($this->gateway);
        $method = $reflection->getMethod('store_transaction_data');
        $method->setAccessible(true);

        $method->invoke($this->gateway, $order, $invoice_result, $checkout_result, '1');

        // Check database record
        $this->assertTransactionExists($order->get_id());

        // Check order meta
        $this->assertEquals('invoice-123', $order->get_meta('_payop_invoice_id'));
        $this->assertEquals('transaction-123', $order->get_meta('_payop_transaction_id'));
    }

    /**
     * Mock successful API responses
     */
    private function mock_successful_api_responses() {
        // Mock invoice creation
        add_filter('pre_http_request', function($preempt, $args, $url) {
            if (strpos($url, '/invoices/create') !== false) {
                return $this->create_mock_api_response(['data' => 'invoice-123']);
            }
            if (strpos($url, '/checkout/create') !== false) {
                return $this->create_mock_api_response(['data' => ['txid' => 'transaction-123']]);
            }
            if (strpos($url, '/check-invoice-status') !== false) {
                return $this->create_mock_api_response([
                    'data' => [
                        'form' => ['url' => 'https://payment.provider.com/pay']
                    ]
                ]);
            }
            return $preempt;
        }, 10, 3);
    }

    /**
     * Mock IPN input
     */
    private function mock_ipn_input($data) {
        // Create temporary file with IPN data
        $temp_file = tempnam(sys_get_temp_dir(), 'payop_ipn_test');
        file_put_contents($temp_file, $data);

        // Mock file_get_contents('php://input')
        add_filter('payop_test_ipn_input', function() use ($temp_file) {
            return file_get_contents($temp_file);
        });
    }
}
