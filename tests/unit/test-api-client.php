<?php
/**
 * PayOp API Client Tests
 *
 * @package PayOp\WooCommerce\Tests
 */

class Test_PayOp_API_Client extends PayOp_Unit_Test_Case {

    /**
     * API client instance
     *
     * @var \PayOp\WooCommerce\API\PayOp_API_Client
     */
    private $api_client;

    /**
     * Setup test
     */
    public function setUp(): void {
        parent::setUp();
        
        $this->api_client = new \PayOp\WooCommerce\API\PayOp_API_Client(
            $this->test_credentials['public_key'],
            $this->test_credentials['secret_key'],
            $this->test_credentials['jwt_token'],
            $this->test_credentials['project_id'],
            true
        );
    }

    /**
     * Test signature generation
     */
    public function test_signature_generation() {
        $reflection = new ReflectionClass($this->api_client);
        $method = $reflection->getMethod('generate_signature');
        $method->setAccessible(true);

        $signature = $method->invoke($this->api_client, '100.00', 'USD', 'test-order-123', 'test-secret');
        
        // Expected signature for these values
        $expected = hash('sha256', '100.00:USD:test-order-123:test-secret');
        
        $this->assertEquals($expected, $signature);
    }

    /**
     * Test get payment methods success
     */
    public function test_get_payment_methods_success() {
        $mock_response = $this->create_mock_api_response([
            'data' => [
                [
                    'identifier' => 1,
                    'title' => 'Test Payment Method',
                    'type' => 'cards_international',
                    'currencies' => ['USD', 'EUR'],
                    'countries' => ['US', 'GB'],
                    'config' => ['fields' => []]
                ]
            ]
        ]);

        $this->mock_http_request('api.payop.com/v1/instrument-settings/payment-methods', $mock_response);

        $result = $this->api_client->get_payment_methods();
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertCount(1, $result['data']);
        $this->assertEquals('Test Payment Method', $result['data'][0]['title']);
    }

    /**
     * Test get payment methods API error
     */
    public function test_get_payment_methods_api_error() {
        $mock_response = $this->create_mock_api_response([
            'message' => 'Unauthorized'
        ], 401);

        $this->mock_http_request('api.payop.com/v1/instrument-settings/payment-methods', $mock_response);

        $result = $this->api_client->get_payment_methods();
        
        $this->assertInstanceOf('WP_Error', $result);
        $this->assertEquals('api_error', $result->get_error_code());
    }

    /**
     * Test create invoice success
     */
    public function test_create_invoice_success() {
        $mock_response = $this->create_mock_api_response([
            'data' => 'invoice-123'
        ]);

        $this->mock_http_request('api.payop.com/v1/invoices/create', $mock_response);

        $order_data = [
            'order' => [
                'id' => 'test-order-123',
                'amount' => '100.00',
                'currency' => 'USD',
                'description' => 'Test order'
            ],
            'payer' => [
                'email' => '<EMAIL>'
            ]
        ];

        $result = $this->api_client->create_invoice($order_data);
        
        $this->assertIsArray($result);
        $this->assertEquals('invoice-123', $result['data']);
    }

    /**
     * Test create checkout success
     */
    public function test_create_checkout_success() {
        $mock_response = $this->create_mock_api_response([
            'data' => [
                'txid' => 'transaction-123'
            ]
        ]);

        $this->mock_http_request('api.payop.com/v1/checkout/create', $mock_response);

        $customer_data = [
            'email' => '<EMAIL>',
            'name' => 'John Doe',
            'paymentMethod' => 1
        ];

        $result = $this->api_client->create_checkout('invoice-123', $customer_data);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertEquals('transaction-123', $result['data']['txid']);
    }

    /**
     * Test check invoice status
     */
    public function test_check_invoice_status() {
        $mock_response = $this->create_mock_api_response([
            'data' => [
                'status' => 'success',
                'form' => [
                    'url' => 'https://payment.provider.com/pay'
                ]
            ]
        ]);

        $this->mock_http_request('api.payop.com/v1/checkout/check-invoice-status', $mock_response);

        $result = $this->api_client->check_invoice_status('invoice-123');
        
        $this->assertIsArray($result);
        $this->assertEquals('success', $result['data']['status']);
        $this->assertEquals('https://payment.provider.com/pay', $result['data']['form']['url']);
    }

    /**
     * Test get transaction details
     */
    public function test_get_transaction_details() {
        $mock_response = $this->create_mock_api_response([
            'data' => [
                'txid' => 'transaction-123',
                'status' => 'success',
                'amount' => '100.00',
                'currency' => 'USD'
            ]
        ]);

        $this->mock_http_request('api.payop.com/v2/transactions', $mock_response);

        $result = $this->api_client->get_transaction_details('transaction-123');
        
        $this->assertIsArray($result);
        $this->assertEquals('transaction-123', $result['data']['txid']);
        $this->assertEquals('success', $result['data']['status']);
    }

    /**
     * Test void transaction
     */
    public function test_void_transaction() {
        $mock_response = $this->create_mock_api_response([
            'data' => [
                'status' => 'cancelled'
            ]
        ]);

        $this->mock_http_request('api.payop.com/v1/checkout/void', $mock_response);

        $result = $this->api_client->void_transaction('invoice-123');
        
        $this->assertIsArray($result);
        $this->assertEquals('cancelled', $result['data']['status']);
    }

    /**
     * Test network error handling
     */
    public function test_network_error_handling() {
        // Mock network error
        add_filter('pre_http_request', function() {
            return new WP_Error('http_request_failed', 'Network error');
        });

        $result = $this->api_client->get_payment_methods();
        
        $this->assertInstanceOf('WP_Error', $result);
        $this->assertEquals('network_error', $result->get_error_code());
    }

    /**
     * Test timeout error handling
     */
    public function test_timeout_error_handling() {
        // Mock timeout error
        add_filter('pre_http_request', function() {
            return new WP_Error('http_request_timeout', 'Request timeout');
        });

        $result = $this->api_client->get_payment_methods();
        
        $this->assertInstanceOf('WP_Error', $result);
        $this->assertEquals('timeout', $result->get_error_code());
    }

    /**
     * Test invalid JSON response
     */
    public function test_invalid_json_response() {
        $mock_response = [
            'response' => ['code' => 200],
            'body' => 'invalid json{'
        ];

        $this->mock_http_request('api.payop.com/v1/instrument-settings/payment-methods', $mock_response);

        $result = $this->api_client->get_payment_methods();
        
        $this->assertInstanceOf('WP_Error', $result);
        $this->assertEquals('json_error', $result->get_error_code());
    }

    /**
     * Test rate limit error
     */
    public function test_rate_limit_error() {
        $mock_response = $this->create_mock_api_response([
            'message' => 'Rate limit exceeded'
        ], 429);

        $this->mock_http_request('api.payop.com/v1/instrument-settings/payment-methods', $mock_response);

        $result = $this->api_client->get_payment_methods();
        
        $this->assertInstanceOf('WP_Error', $result);
        $this->assertEquals('rate_limit', $result->get_error_code());
    }

    /**
     * Test server error handling
     */
    public function test_server_error_handling() {
        $mock_response = $this->create_mock_api_response([
            'message' => 'Internal server error'
        ], 500);

        $this->mock_http_request('api.payop.com/v1/instrument-settings/payment-methods', $mock_response);

        $result = $this->api_client->get_payment_methods();
        
        $this->assertInstanceOf('WP_Error', $result);
        $this->assertEquals('server_error', $result->get_error_code());
    }
}
