#!/bin/bash

# PayOp WooCommerce Plugin Test Runner
# 
# This script sets up the test environment and runs all tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
WP_VERSION=${WP_VERSION:-latest}
WC_VERSION=${WC_VERSION:-latest}
PHP_VERSION=${PHP_VERSION:-8.2}

echo -e "${YELLOW}PayOp WooCommerce Plugin Test Runner${NC}"
echo "=================================="

# Check if PHPUnit is available
if ! command -v phpunit &> /dev/null; then
    echo -e "${RED}PHPUnit is not installed. Please install PHPUnit first.${NC}"
    echo "You can install it via Composer: composer global require phpunit/phpunit"
    exit 1
fi

# Check if WP-CLI is available
if ! command -v wp &> /dev/null; then
    echo -e "${YELLOW}WP-CLI is not installed. Some tests may not work properly.${NC}"
fi

# Set up test database
DB_NAME="payop_wc_test"
DB_USER="root"
DB_PASS=""
DB_HOST="localhost"

echo -e "${YELLOW}Setting up test database...${NC}"

# Create test database
mysql -u${DB_USER} -p${DB_PASS} -h${DB_HOST} -e "DROP DATABASE IF EXISTS ${DB_NAME};" 2>/dev/null || true
mysql -u${DB_USER} -p${DB_PASS} -h${DB_HOST} -e "CREATE DATABASE ${DB_NAME};"

# Set environment variables
export WP_TESTS_DIR="/tmp/wordpress-tests-lib"
export WP_CORE_DIR="/tmp/wordpress"

# Download WordPress test suite if not exists
if [ ! -d "$WP_TESTS_DIR" ]; then
    echo -e "${YELLOW}Downloading WordPress test suite...${NC}"
    
    # Download test suite
    svn co --quiet https://develop.svn.wordpress.org/trunk/tests/phpunit/includes/ $WP_TESTS_DIR/includes
    svn co --quiet https://develop.svn.wordpress.org/trunk/tests/phpunit/data/ $WP_TESTS_DIR/data
    
    # Download wp-tests-config.php
    if [ ! -f "$WP_TESTS_DIR/wp-tests-config.php" ]; then
        wget -nv -O "$WP_TESTS_DIR/wp-tests-config.php" \
            https://develop.svn.wordpress.org/trunk/wp-tests-config-sample.php
        
        # Configure database
        sed -i "s/youremptytestdbnamehere/$DB_NAME/" "$WP_TESTS_DIR/wp-tests-config.php"
        sed -i "s/yourusernamehere/$DB_USER/" "$WP_TESTS_DIR/wp-tests-config.php"
        sed -i "s/yourpasswordhere/$DB_PASS/" "$WP_TESTS_DIR/wp-tests-config.php"
        sed -i "s|localhost|$DB_HOST|" "$WP_TESTS_DIR/wp-tests-config.php"
    fi
fi

# Download WordPress core if not exists
if [ ! -d "$WP_CORE_DIR" ]; then
    echo -e "${YELLOW}Downloading WordPress core...${NC}"
    
    if [ "$WP_VERSION" == "latest" ]; then
        local ARCHIVE_NAME='latest'
    else
        local ARCHIVE_NAME="wordpress-$WP_VERSION"
    fi
    
    wget -nv -O /tmp/wordpress.tar.gz https://wordpress.org/${ARCHIVE_NAME}.tar.gz
    tar --strip-components=1 -zxmf /tmp/wordpress.tar.gz -C $WP_CORE_DIR
fi

# Download WooCommerce if not exists
WC_DIR="$WP_CORE_DIR/wp-content/plugins/woocommerce"
if [ ! -d "$WC_DIR" ]; then
    echo -e "${YELLOW}Downloading WooCommerce...${NC}"
    
    if [ "$WC_VERSION" == "latest" ]; then
        WC_DOWNLOAD_URL="https://downloads.wordpress.org/plugin/woocommerce.latest-stable.zip"
    else
        WC_DOWNLOAD_URL="https://downloads.wordpress.org/plugin/woocommerce.${WC_VERSION}.zip"
    fi
    
    wget -nv -O /tmp/woocommerce.zip $WC_DOWNLOAD_URL
    unzip -q /tmp/woocommerce.zip -d "$WP_CORE_DIR/wp-content/plugins/"
fi

# Copy plugin to test environment
PLUGIN_DIR="$WP_CORE_DIR/wp-content/plugins/payop-direct-payment-woo"
if [ -d "$PLUGIN_DIR" ]; then
    rm -rf "$PLUGIN_DIR"
fi

echo -e "${YELLOW}Copying plugin to test environment...${NC}"
cp -r "$(dirname "$0")/.." "$PLUGIN_DIR"

# Run tests
echo -e "${YELLOW}Running tests...${NC}"

# Set PHPUnit configuration
PHPUNIT_CONFIG="$(dirname "$0")/phpunit.xml"

if [ ! -f "$PHPUNIT_CONFIG" ]; then
    echo -e "${YELLOW}Creating PHPUnit configuration...${NC}"
    cat > "$PHPUNIT_CONFIG" << EOF
<?xml version="1.0"?>
<phpunit
    bootstrap="bootstrap.php"
    backupGlobals="false"
    colors="true"
    convertErrorsToExceptions="true"
    convertNoticesToExceptions="true"
    convertWarningsToExceptions="true"
    >
    <testsuites>
        <testsuite name="PayOp WooCommerce Plugin Test Suite">
            <directory>./unit/</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist>
            <directory suffix=".php">../includes/</directory>
        </whitelist>
    </filter>
</phpunit>
EOF
fi

# Run the tests
cd "$(dirname "$0")"

echo -e "${GREEN}Starting test execution...${NC}"

if phpunit --configuration="$PHPUNIT_CONFIG" --verbose; then
    echo -e "${GREEN}✓ All tests passed!${NC}"
    exit 0
else
    echo -e "${RED}✗ Some tests failed!${NC}"
    exit 1
fi
