# StablecoinPay Assets

This directory contains assets for the StablecoinPay WooCommerce plugin.

## Images

- `stablecoinpay-icon.svg` - Plugin icon in SVG format
- `stablecoinpay-icon.png` - Plugin icon in PNG format (32x32px)

## Token Icons

For production use, you should add token icons:

- `tokens/usdt.png` - USDT token icon
- `tokens/usdc.png` - USDC token icon  
- `tokens/dai.png` - DAI token icon
- `tokens/pyusd.png` - PYUSD token icon
- `tokens/busd.png` - BUSD token icon

## CSS Files

- `css/payment-page.css` - Styles for the self-hosted payment page
- `css/admin.css` - Admin interface styles
- `css/checkout.css` - Checkout page styles

## JavaScript Files

- `js/payment-page.js` - Payment page functionality
- `js/admin.js` - Admin interface functionality

## Notes

- All images should be optimized for web use
- Icons should be provided in multiple sizes (16x16, 32x32, 64x64)
- SVG icons are preferred for scalability
- Token icons should be sourced from official sources when possible
