/**
 * StablecoinPay Payment Page JavaScript
 *
 * Handles real-time payment monitoring and UI interactions
 */

class StablecoinPayPaymentPage {
  constructor() {
    this.paymentData = window.stablecoinpayPayment || {};
    this.config = window.stablecoinpay_payment || {};
    this.pollInterval = null;
    this.countdownInterval = null;

    // Enhanced error tracking from minified version
    this.errorCount = 0;
    this.lastSuccessTime = Date.now();

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.setupAccordion();
    this.initCustomDropdown();

    // Wait a bit for DOM to be fully ready
    setTimeout(() => {
      this.initializePaymentMethodSelector();
      this.updateSelectedIcon();
    }, 100);

    this.startPaymentPolling();
    this.startCountdown();
    this.updateStatusDisplay();
  }

  setupEventListeners() {
    this.attachCopyListeners();

    // Store event handlers for proper cleanup
    this.visibilityChangeHandler = () => {
      if (document.hidden) {
        this.stopPaymentPolling();
      } else {
        this.startPaymentPolling();
      }
    };

    this.beforeUnloadHandler = () => {
      this.cleanup();
    };

    this.popStateHandler = () => {
      this.cleanup();
    };

    this.pageHideHandler = () => {
      this.cleanup();
    };

    // Add event listeners
    document.addEventListener("visibilitychange", this.visibilityChangeHandler);
    window.addEventListener("beforeunload", this.beforeUnloadHandler);
    window.addEventListener("popstate", this.popStateHandler);
    window.addEventListener("pagehide", this.pageHideHandler);
  }

  setupAccordion() {
    const accordionToggle = document.querySelector(".accordion-toggle");
    if (accordionToggle) {
      accordionToggle.addEventListener("click", () => {
        const isExpanded =
          accordionToggle.getAttribute("aria-expanded") === "true";
        accordionToggle.setAttribute("aria-expanded", !isExpanded);
      });
    }
  }

  updateSelectedIcon() {
    const paymentMethodSelect = document.getElementById(
      "payment-method-select"
    );
    const dropdown = document.getElementById("payment-method-dropdown");

    if (!paymentMethodSelect) return;

    const selectedOption =
      paymentMethodSelect.options[paymentMethodSelect.selectedIndex];
    if (!selectedOption) return;

    const token = selectedOption.getAttribute("data-token");
    const blockchain = selectedOption.getAttribute("data-blockchain");
    const label = selectedOption.getAttribute("data-label") || token;

    // Get plugin URL from existing icon in the page or construct it
    const existingIcon = document.querySelector(".dropdown-selected-icon");
    let pluginUrl = "";
    if (existingIcon && existingIcon.src) {
      // Extract plugin URL from existing icon src
      const srcParts = existingIcon.src.split("/assets/icons/");
      if (srcParts.length > 1) {
        pluginUrl = srcParts[0] + "/";
      }
    }

    // Fallback: try to get from config or construct from current location
    if (!pluginUrl) {
      pluginUrl =
        this.config.plugin_url ||
        window.location.origin + "/wp-content/plugins/stablecoinpay-gateway/";
    }

    const iconUrl = `${pluginUrl}assets/icons/${token.toLowerCase()}.svg`;

    // Update custom dropdown display
    if (dropdown) {
      const selectedIcon = dropdown.querySelector(".dropdown-selected-icon");
      const selectedToken = dropdown.querySelector(".dropdown-selected-token");
      const selectedNetwork = dropdown.querySelector(
        ".dropdown-selected-network"
      );

      if (selectedIcon) {
        selectedIcon.src = iconUrl;
        selectedIcon.alt = token;
      }
      if (selectedToken) {
        selectedToken.textContent = label;
      }
      if (selectedNetwork) {
        selectedNetwork.textContent = this.getSimplifiedNetworkText(
          blockchain,
          this.paymentData.network
        );
      }

      // Update selected state in dropdown options
      dropdown.querySelectorAll(".dropdown-option").forEach(option => {
        const optionToken = option.getAttribute("data-token");
        const optionBlockchain = option.getAttribute("data-blockchain");

        if (optionToken === token && optionBlockchain === blockchain) {
          option.classList.add("selected");
        } else {
          option.classList.remove("selected");
        }
      });
    }

    // Update legacy icon (if it exists)
    const legacyIcon = document.getElementById("selected-method-icon");
    if (legacyIcon) {
      legacyIcon.src = iconUrl;
      legacyIcon.alt = token;
    }
  }

  attachCopyListeners() {
    // Copy icons and buttons - use event delegation for dynamically added elements
    document.addEventListener("click", e => {
      if (
        e.target.closest(".copy-icon") ||
        e.target.closest(".copy-btn-small")
      ) {
        const element =
          e.target.closest(".copy-icon") || e.target.closest(".copy-btn-small");
        const textToCopy = element.getAttribute("data-copy");
        if (textToCopy) {
          this.copyToClipboard(textToCopy, element);
        }
      }
    });
  }

  initCustomDropdown() {
    const dropdown = document.getElementById("payment-method-dropdown");
    const dropdownButton = dropdown?.querySelector(".dropdown-button");
    const dropdownMenu = dropdown?.querySelector(".dropdown-menu");
    const hiddenSelect = document.getElementById("payment-method-select");

    if (!dropdown || !dropdownButton || !dropdownMenu || !hiddenSelect) {
      return;
    }

    // Toggle dropdown
    dropdownButton.addEventListener("click", e => {
      e.preventDefault();
      e.stopPropagation();

      const isOpen = dropdown.getAttribute("aria-expanded") === "true";

      if (isOpen) {
        this.closeDropdown();
      } else {
        this.openDropdown();
      }
    });

    // Handle option selection
    dropdownMenu.addEventListener("click", e => {
      const option = e.target.closest(".dropdown-option");
      if (!option) return;

      e.preventDefault();
      e.stopPropagation();

      this.selectDropdownOption(option);
    });

    // Close dropdown when clicking outside
    document.addEventListener("click", e => {
      if (!dropdown.contains(e.target)) {
        this.closeDropdown();
      }
    });

    // Handle keyboard navigation
    dropdown.addEventListener("keydown", e => {
      this.handleDropdownKeydown(e);
    });
  }

  openDropdown() {
    const dropdown = document.getElementById("payment-method-dropdown");
    const dropdownButton = dropdown?.querySelector(".dropdown-button");
    const dropdownMenu = dropdown?.querySelector(".dropdown-menu");

    if (!dropdown || !dropdownButton || !dropdownMenu) return;

    dropdown.setAttribute("aria-expanded", "true");
    dropdownButton.classList.add("active");
    dropdownMenu.style.display = "block";

    // Force reflow then add active class for animation
    dropdownMenu.offsetHeight;
    dropdownMenu.classList.add("active");

    // Focus first option
    const firstOption = dropdownMenu.querySelector(".dropdown-option");
    if (firstOption) {
      firstOption.focus();
    }
  }

  closeDropdown() {
    const dropdown = document.getElementById("payment-method-dropdown");
    const dropdownButton = dropdown?.querySelector(".dropdown-button");
    const dropdownMenu = dropdown?.querySelector(".dropdown-menu");

    if (!dropdown || !dropdownButton || !dropdownMenu) return;

    dropdown.setAttribute("aria-expanded", "false");
    dropdownButton.classList.remove("active");
    dropdownMenu.classList.remove("active");

    // Hide after animation completes
    setTimeout(() => {
      if (!dropdownMenu.classList.contains("active")) {
        dropdownMenu.style.display = "none";
      }
    }, 300);
  }

  selectDropdownOption(option) {
    const dropdown = document.getElementById("payment-method-dropdown");
    const hiddenSelect = document.getElementById("payment-method-select");
    const selectedIcon = dropdown?.querySelector(".dropdown-selected-icon");
    const selectedToken = dropdown?.querySelector(".dropdown-selected-token");
    const selectedNetwork = dropdown?.querySelector(
      ".dropdown-selected-network"
    );

    if (
      !dropdown ||
      !hiddenSelect ||
      !selectedIcon ||
      !selectedToken ||
      !selectedNetwork
    )
      return;

    const value = option.getAttribute("data-value");
    const token = option.getAttribute("data-token");
    const blockchain = option.getAttribute("data-blockchain");
    const label = option.getAttribute("data-label") || token;
    const iconSrc = option.querySelector(".dropdown-option-icon")?.src;

    // Update visual selection
    dropdown
      .querySelectorAll(".dropdown-option")
      .forEach(opt => opt.classList.remove("selected"));
    option.classList.add("selected");

    // Update displayed selection
    if (iconSrc) {
      selectedIcon.src = iconSrc;
      selectedIcon.alt = token;
    }
    selectedToken.textContent = label;
    selectedNetwork.textContent = this.getSimplifiedNetworkText(
      blockchain,
      this.paymentData.network
    );

    // Update hidden select
    hiddenSelect.value = value;

    // Trigger change event
    const changeEvent = new Event("change", {bubbles: true});
    hiddenSelect.dispatchEvent(changeEvent);

    this.closeDropdown();
  }

  handleDropdownKeydown(e) {
    const dropdown = document.getElementById("payment-method-dropdown");
    const isOpen = dropdown?.getAttribute("aria-expanded") === "true";
    const options = dropdown?.querySelectorAll(".dropdown-option");

    if (!dropdown || !options) return;

    switch (e.key) {
      case "Enter":
      case " ":
        e.preventDefault();
        if (!isOpen) {
          this.openDropdown();
        } else {
          const focused = document.activeElement;
          if (focused && focused.classList.contains("dropdown-option")) {
            this.selectDropdownOption(focused);
          }
        }
        break;

      case "Escape":
        e.preventDefault();
        this.closeDropdown();
        dropdown.querySelector(".dropdown-button")?.focus();
        break;

      case "ArrowDown":
        e.preventDefault();
        if (!isOpen) {
          this.openDropdown();
        } else {
          const focused = document.activeElement;
          const currentIndex = Array.from(options).indexOf(focused);
          const nextIndex =
            currentIndex < options.length - 1 ? currentIndex + 1 : 0;
          options[nextIndex].focus();
        }
        break;

      case "ArrowUp":
        e.preventDefault();
        if (isOpen) {
          const focused = document.activeElement;
          const currentIndex = Array.from(options).indexOf(focused);
          const prevIndex =
            currentIndex > 0 ? currentIndex - 1 : options.length - 1;
          options[prevIndex].focus();
        }
        break;
    }
  }

  startPaymentPolling() {
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
    }

    // Don't poll if payment is already completed or expired
    if (
      ["CONFIRMED", "EXPIRED", "CANCELED"].includes(this.paymentData.status)
    ) {
      return;
    }

    // Use adaptive polling interval from minified version for better performance
    const interval = this.getAdaptivePollingInterval();
    this.pollInterval = setInterval(() => {
      this.checkPaymentStatus();

      // Recalculate interval in case conditions changed
      const newInterval = this.getAdaptivePollingInterval();
      if (newInterval !== interval) {
        this.startPaymentPolling(); // Restart with new interval
      }
    }, interval);

    // Initial check
    this.checkPaymentStatus();
  }

  /**
   * Get adaptive polling interval based on payment state and error count
   * More frequent polling when payment is detected or when errors occur
   */
  getAdaptivePollingInterval() {
    // Initialize defaults
    this.errorCount = this.errorCount || 0;
    this.lastSuccessTime = this.lastSuccessTime || Date.now();

    // Exponential backoff on errors (from minified version)
    if (this.errorCount > 0) {
      const backoffMultiplier = Math.min(Math.pow(2, this.errorCount), 16);
      return Math.min(5000 * backoffMultiplier, 60000); // Max 60 seconds
    }

    // Faster polling when payment is detected
    if (this.paymentData.status === "DETECTED") {
      return 5000; // 5 seconds
    }

    // Dynamic polling based on time remaining
    if (this.paymentData.timeRemainingSeconds !== undefined) {
      const timeRemaining = this.paymentData.timeRemainingSeconds;

      if (timeRemaining <= 30) return 5000; // 5 seconds - final countdown
      if (timeRemaining <= 120) return 10000; // 10 seconds - under 2 minutes
      if (timeRemaining <= 300) return 15000; // 15 seconds - under 5 minutes
      if (timeRemaining <= 600) return 20000; // 20 seconds - under 10 minutes
      return 30000; // 30 seconds - default for longer periods
    }

    // Default fallback
    return 10000; // 10 seconds
  }

  stopPaymentPolling() {
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
      this.pollInterval = null;
    }
  }

  async checkPaymentStatus() {
    try {
      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

      const response = await fetch(this.config.ajax_url, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          action: "stablecoinpay_get_payment_status",
          order_id: this.config.order_id,
          payment_id: this.config.payment_id,
          nonce: this.config.nonce,
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Get response text first to debug what we're actually receiving
      const responseText = await response.text();

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("Payment status JSON parse error:", parseError);
        console.error("Response text that failed to parse:", responseText);
        throw new Error("Invalid JSON response from server");
      }

      if (data.success) {
        // Reset error tracking on successful response
        this.errorCount = 0;
        this.lastSuccessTime = Date.now();

        // Handle successful response
        this.handleStatusUpdate(data.data);
      } else {
        throw new Error(data.data || "Payment status check failed");
      }
    } catch (error) {
      // Enhanced error handling from minified version
      this.errorCount = (this.errorCount || 0) + 1;

      if (error.name === "AbortError") {
        console.warn("Payment status check timed out");
      } else if (
        error.message.includes("NetworkError") ||
        error.message.includes("Failed to fetch")
      ) {
        console.warn("Network error checking payment status:", error.message);
      } else {
        console.error("Error checking payment status:", error.message);
      }

      // Show user-friendly messages for persistent errors
      if (this.errorCount >= 3) {
        this.showMessage("Connection issues detected. Retrying...", "warning");
      }

      if (this.errorCount >= 10) {
        this.stopPaymentPolling();
        this.showMessage(
          "Unable to check payment status. Please refresh the page.",
          "error"
        );
      }
    }
  }

  handleStatusUpdate(statusData) {
    const oldStatus = this.paymentData.status;
    const newStatus = statusData.status;

    // Always update payment data from backend
    this.paymentData.status = newStatus;
    this.paymentData.txHash = statusData.tx_hash;

    // Track confirmation changes for real-time updates
    const oldConfirmations = this.paymentData.confirmations || 0;
    const newConfirmations = statusData.confirmations || 0;
    const oldRequiredConfirmations = this.paymentData.requiredConfirmations;
    const newRequiredConfirmations =
      statusData.required_confirmations ||
      this.getBlockchainDefaultConfirmations(
        statusData.blockchain,
        statusData.network
      );

    this.paymentData.confirmations = newConfirmations;
    this.paymentData.requiredConfirmations = newRequiredConfirmations;
    this.paymentData.blockchain =
      statusData.blockchain || this.paymentData.blockchain;
    this.paymentData.network = statusData.network || this.paymentData.network;

    // Update server-side timer data
    if (statusData.time_remaining_seconds !== undefined) {
      this.paymentData.timeRemainingSeconds = statusData.time_remaining_seconds;
      this.paymentData.isExpired = statusData.is_expired || false;
      this.paymentData.serverTime = statusData.server_time;

      // Sync smooth timer with server data
      this.updateCountdownFromServer();
    }

    // Sync expiration time if provided by backend
    if (statusData.expires_at) {
      this.paymentData.expiresAt = statusData.expires_at;
    }

    // Update UI for status changes OR confirmation changes
    const confirmationsChanged =
      oldConfirmations !== newConfirmations ||
      oldRequiredConfirmations !== newRequiredConfirmations;

    if (oldStatus !== newStatus) {
      this.updateStatusDisplay();
      this.handleStatusChange(newStatus, statusData);
    } else if (
      confirmationsChanged &&
      (newStatus === "DETECTED" || newStatus === "CONFIRMED")
    ) {
      // Update confirmation display even if status hasn't changed

      this.updateConfirmationDisplay();
    }
  }

  handleStatusChange(status, statusData) {
    switch (status) {
      case "DETECTED":
        this.showMessage(this.config.strings.detected, "info");
        break;

      case "CONFIRMED":
        this.showMessage(this.config.strings.confirmed, "success");
        this.stopPaymentPolling();
        this.stopCountdown();
        this.showSuccessAnimation();

        // Redirect after delay
        if (statusData.redirect_url) {
          setTimeout(() => {
            this.showMessage(this.config.strings.redirecting, "info");
            window.location.href = statusData.redirect_url;
          }, 3000);
        }
        break;

      case "EXPIRED":
        this.showMessage(this.config.strings.expired, "error");
        this.stopPaymentPolling();
        this.stopCountdown();
        break;

      case "CANCELED":
        this.showMessage(this.config.strings.canceled, "error");
        this.stopPaymentPolling();
        this.stopCountdown();
        break;
    }
  }

  updateStatusDisplay() {
    const statusIndicator = document.querySelector(".status-indicator");
    const statusIcon = document.querySelector(".status-emoji");
    const statusTitle = document.querySelector(".status-title");
    const statusDescription = document.querySelector(".status-description");
    const timerSection = document.querySelector(".status-timer-right");
    const paymentDetails = document.querySelector(".payment-details");
    const cancelButtonContainer = document.getElementById(
      "cancel-button-container"
    );

    if (!statusIndicator) return;

    // Update status attribute
    statusIndicator.setAttribute("data-status", this.paymentData.status);

    // Hide timer when payment is detected, confirmed, expired, or canceled
    if (timerSection) {
      if (
        ["DETECTED", "CONFIRMED", "EXPIRED", "CANCELED"].includes(
          this.paymentData.status
        )
      ) {
        timerSection.style.display = "none";
      } else {
        timerSection.style.display = "flex";
      }
    }

    // Hide payment details when payment is detected, confirmed, expired, or canceled
    if (paymentDetails) {
      if (
        ["DETECTED", "CONFIRMED", "EXPIRED", "CANCELED"].includes(
          this.paymentData.status
        )
      ) {
        paymentDetails.style.display = "none";
      } else {
        paymentDetails.style.display = "block";
      }
    }

    // Show cancel button only when payment status is WAITING
    if (cancelButtonContainer) {
      if (this.paymentData.status === "WAITING") {
        cancelButtonContainer.classList.remove("hidden");
      } else {
        cancelButtonContainer.classList.add("hidden");
      }
    }

    // Hide payment instructions accordion when payment is detected, confirmed, expired, or canceled
    const paymentInstructionsAccordion = document.querySelector(
      ".payment-instructions-accordion"
    );
    if (paymentInstructionsAccordion) {
      if (
        ["DETECTED", "CONFIRMED", "EXPIRED", "CANCELED"].includes(
          this.paymentData.status
        )
      ) {
        paymentInstructionsAccordion.style.display = "none";
      } else {
        paymentInstructionsAccordion.style.display = "block";
      }
    }

    // Hide payment method selector when payment is detected, confirmed, expired, or canceled
    const paymentMethodSelectorExternal = document.querySelector(
      ".payment-method-selector-external"
    );
    if (paymentMethodSelectorExternal) {
      if (
        ["DETECTED", "CONFIRMED", "EXPIRED", "CANCELED"].includes(
          this.paymentData.status
        )
      ) {
        paymentMethodSelectorExternal.style.display = "none";
      } else {
        paymentMethodSelectorExternal.style.display = "flex";
      }
    }

    // Update or create confirmation display
    this.updateConfirmationDisplay();

    // Update visibility of UI elements based on status
    this.updatePaymentInstructionsVisibility();
    this.updatePaymentMethodSelectorVisibility();

    // Update content based on status
    switch (this.paymentData.status) {
      case "WAITING":
        statusIcon.textContent = "⏳";
        statusTitle.textContent = this.config.strings.waiting;
        statusDescription.textContent =
          "Send the exact amount to the address below";
        break;

      case "DETECTED":
        statusIcon.textContent = "👀";
        statusTitle.textContent = this.config.strings.detected;
        statusDescription.textContent =
          "Payment detected, waiting for confirmation";
        break;

      case "CONFIRMED":
        statusIcon.textContent = "✅";
        statusTitle.textContent = this.config.strings.confirmed;
        statusDescription.textContent =
          "Payment successful! Redirecting to order confirmation...";
        document.body.classList.add("payment-success");
        break;

      case "EXPIRED":
        statusIcon.textContent = "⏰";
        statusTitle.textContent = this.config.strings.expired;
        statusDescription.textContent = "Payment time has expired";
        break;

      case "CANCELED":
        statusIcon.textContent = "❌";
        statusTitle.textContent = this.config.strings.canceled;
        statusDescription.textContent = "Payment was canceled";
        break;
    }

    // Update action button and footer visibility
    this.updateActionButton(this.paymentData.status);
  }

  updateActionButton(status) {
    const actionArea = document.getElementById("payment-action-area");
    const actionBtn = document.getElementById("payment-action-btn");
    const footer = document.querySelector(".payment-footer");

    if (!actionArea || !actionBtn) return;

    // Show action button only for expired or canceled payments (not confirmed)
    if (["EXPIRED", "CANCELED"].includes(status)) {
      // Update button text and URL based on status
      const btnText = actionBtn.querySelector(".btn-text");
      btnText.textContent = "Back to Shop";
      actionBtn.href = this.getShopUrl();

      // Show action area
      actionArea.style.display = "block";
    } else {
      // Hide action area for waiting/detected/confirmed payments
      actionArea.style.display = "none";
    }

    // Footer is always visible now since cancel button moved to payment content area
    if (footer) {
      footer.classList.remove("hidden");
    }
  }

  getShopUrl() {
    // Use shop URL from payment data, fallback to home page
    return this.paymentData.shopUrl || window.location.origin + "/shop/";
  }

  /**
   * Get blockchain explorer URL for a transaction
   * @param {string} blockchain - Blockchain name (ETHEREUM, BSC, TRON, SOLANA, TON)
   * @param {string} network - Network type (MAINNET, TESTNET)
   * @param {string} txHash - Transaction hash
   * @returns {string} Explorer URL
   */
  getBlockchainExplorerUrl(blockchain, network, txHash) {
    if (!blockchain || !network || !txHash) {
      return "#";
    }

    const blockchainUpper = blockchain.toUpperCase();
    const networkUpper = network.toUpperCase();
    const isMainnet = networkUpper.includes("MAINNET");

    switch (blockchainUpper) {
      case "ETHEREUM":
      case "ETH":
        return isMainnet
          ? `https://etherscan.io/tx/${txHash}`
          : `https://sepolia.etherscan.io/tx/${txHash}`;

      case "BSC":
      case "BINANCE":
        return isMainnet
          ? `https://bscscan.com/tx/${txHash}`
          : `https://testnet.bscscan.com/tx/${txHash}`;

      case "TRON":
      case "TRX":
        return isMainnet
          ? `https://tronscan.org/#/transaction/${txHash}`
          : `https://shasta.tronscan.org/#/transaction/${txHash}`;

      case "SOLANA":
      case "SOL":
        return isMainnet
          ? `https://solscan.io/tx/${txHash}`
          : `https://solscan.io/tx/${txHash}?cluster=testnet`;

      case "TON":
        return isMainnet
          ? `https://tonscan.org/tx/${txHash}`
          : `https://testnet.tonscan.org/tx/${txHash}`;

      default:
        return "#";
    }
  }

  updateConfirmationDisplay() {
    const status = this.paymentData.status;
    const statusIndicator = document.querySelector(".status-indicator");

    // Remove existing confirmation display
    const existingConfirmations = statusIndicator.querySelector(
      ".status-confirmations"
    );
    if (existingConfirmations) {
      existingConfirmations.remove();
    }

    // Show confirmations only for DETECTED and CONFIRMED status
    if (status === "DETECTED" || status === "CONFIRMED") {
      const confirmations = this.paymentData.confirmations || 0;
      const required =
        this.paymentData.requiredConfirmations ||
        this.getBlockchainDefaultConfirmations(
          this.paymentData.blockchain,
          this.paymentData.network
        );

      const confirmationDiv = document.createElement("div");
      confirmationDiv.className = "status-confirmations";
      confirmationDiv.innerHTML = `
                <span class="confirmation-label">Confirmations:</span>
                <span class="confirmation-count">${confirmations}/${required}</span>
            `;

      statusIndicator.appendChild(confirmationDiv);

      // Add detailed confirmation info below status when payment details are hidden
      this.showDetailedConfirmationInfo(confirmations, required, status);
    }
  }

  showDetailedConfirmationInfo(confirmations, required, status) {
    // Remove existing detailed confirmation info
    const existingDetailedInfo = document.querySelector(
      ".detailed-confirmation-info"
    );
    if (existingDetailedInfo) {
      existingDetailedInfo.remove();
    }

    // Create detailed confirmation section
    const detailedInfo = document.createElement("div");
    detailedInfo.className = "detailed-confirmation-info";

    const progressPercentage = Math.round((confirmations / required) * 100);

    detailedInfo.innerHTML = `
            <div class="confirmation-card">
                <div class="confirmation-header">
                    <h3>${
                      status === "CONFIRMED"
                        ? "✅ Payment Confirmed"
                        : "🔍 Payment Detected"
                    }</h3>
                    <p>${
                      status === "CONFIRMED"
                        ? "Your payment has been successfully processed!"
                        : "Waiting for blockchain confirmations..."
                    }</p>
                </div>
                <div class="confirmation-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progressPercentage}%"></div>
                    </div>
                    <div class="progress-text">
                        <span class="progress-count">${confirmations} of ${required} confirmations</span>
                        <span class="progress-percentage">${progressPercentage}%</span>
                    </div>
                </div>
                ${
                  this.paymentData.txHash
                    ? `
                <div class="transaction-info">
                    <label>Transaction Hash:</label>
                    <div class="tx-hash">
                        <span class="tx-value">${this.paymentData.txHash}</span>
                        <a href="${this.getBlockchainExplorerUrl(
                          this.paymentData.blockchain,
                          this.paymentData.network,
                          this.paymentData.txHash
                        )}"
                           target="_blank"
                           rel="noopener noreferrer"
                           class="explorer-btn">
                            View on Blockchain
                        </a>
                    </div>
                </div>
                `
                    : ""
                }
            </div>
        `;

    // Insert after status indicator
    const statusIndicator = document.querySelector(".status-indicator");
    statusIndicator.parentNode.insertBefore(
      detailedInfo,
      statusIndicator.nextSibling
    );
  }

  startCountdown() {
    const timerElement = document.getElementById("countdown-timer");
    if (!timerElement) {
      return;
    }

    // Initialize with current server data
    this.updateCountdownFromServer();

    // Track current display time to prevent flickering
    this.currentDisplayTime = this.paymentData.timeRemainingSeconds || 0;
    this.lastUpdateTime = Date.now();

    // Start stable countdown display
    this.countdownInterval = setInterval(() => {
      // Stop countdown if payment is no longer waiting
      if (this.paymentData.status !== "WAITING") {
        this.stopCountdown();
        return;
      }

      // Only decrement if enough time has passed and no recent server update
      const timeSinceUpdate = Date.now() - this.lastUpdateTime;

      if (timeSinceUpdate >= 1000 && this.currentDisplayTime > 0) {
        this.currentDisplayTime = Math.max(0, this.currentDisplayTime - 1);
        this.lastUpdateTime = Date.now();

        // console.log('Timer tick:', this.currentDisplayTime); // Debug disabled
        this.updateCountdownDisplay(this.currentDisplayTime);
      }

      // If display time reaches zero, trigger immediate expiry
      if (
        this.currentDisplayTime <= 0 &&
        this.paymentData.status === "WAITING"
      ) {
        console.log("Display timer reached zero, triggering immediate expiry");
        this.stopCountdown(); // Stop the countdown to prevent multiple triggers
        this.triggerImmediateExpiry();
      }
    }, 100); // Check every 100ms for smoother timing
  }

  updateCountdownFromServer() {
    if (this.paymentData.timeRemainingSeconds !== undefined) {
      const serverTime = this.paymentData.timeRemainingSeconds;
      // console.log('Server sync - Current display:', this.currentDisplayTime, 'Server time:', serverTime); // Debug disabled

      // Only update if server time is significantly different (avoid flickering)
      const timeDiff = Math.abs((this.currentDisplayTime || 0) - serverTime);

      if (timeDiff > 2 || this.currentDisplayTime === undefined) {
        // Significant difference or first sync - update immediately
        // console.log('Syncing display time to server:', serverTime); // Debug disabled
        this.currentDisplayTime = serverTime;
        this.lastUpdateTime = Date.now();
        this.updateCountdownDisplay(serverTime);
      } else if (serverTime < this.currentDisplayTime) {
        // Server is behind display (normal), but sync if difference is growing
        if (timeDiff >= 1) {
          // console.log('Adjusting display time to server:', serverTime); // Debug disabled
          this.currentDisplayTime = serverTime;
          this.lastUpdateTime = Date.now();
          this.updateCountdownDisplay(serverTime);
        }
      }

      // Fallback: If server reports expired time, trigger immediate expiry
      if (serverTime <= 0 && this.paymentData.status === "WAITING") {
        console.log("Server reports expired time, triggering immediate expiry");
        this.stopCountdown();
        this.triggerImmediateExpiry();
      }

      // If server time is ahead of display, let display catch up naturally
    }
  }

  updateCountdownDisplay(remainingSeconds) {
    const timerElement = document.getElementById("countdown-timer");
    if (!timerElement) {
      return;
    }

    if (remainingSeconds <= 0) {
      timerElement.textContent = "00:00";
      timerElement.classList.add("timer-expired");
    } else {
      const minutes = Math.floor(remainingSeconds / 60);
      const seconds = remainingSeconds % 60;

      timerElement.textContent = `${minutes
        .toString()
        .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;

      // Remove expired class if timer is running
      timerElement.classList.remove("timer-expired");

      // Add warning class when less than 5 minutes
      if (remainingSeconds < 300) {
        // 5 minutes
        timerElement.classList.add("timer-warning");
      } else {
        timerElement.classList.remove("timer-warning");
      }
    }
  }

  stopCountdown() {
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
      this.countdownInterval = null;
    }
  }

  increasePollingFrequency() {
    // When frontend timer expires, poll more frequently to sync with backend
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
    }

    // console.log('Increasing polling frequency for backend sync'); // Debug disabled
    this.pollInterval = setInterval(() => {
      this.checkPaymentStatus();
    }, 1000); // Poll every 1 second for faster sync

    // Reset to normal frequency after 30 seconds
    setTimeout(() => {
      if (this.pollInterval && this.paymentData.status === "WAITING") {
        // console.log('Resetting to normal polling frequency'); // Debug disabled
        clearInterval(this.pollInterval);
        this.pollInterval = setInterval(() => {
          this.checkPaymentStatus();
        }, this.config.poll_interval || 10000);
      }
    }, 30000); // 30 seconds
  }

  async triggerImmediateExpiry() {
    // Prevent multiple simultaneous expiry triggers
    if (this.expiryTriggered) {
      console.log("Expiry already triggered, skipping");
      return;
    }

    this.expiryTriggered = true;

    try {
      const startTime = Date.now();
      console.log(
        "🚨 TRIGGERING IMMEDIATE EXPIRY for payment:",
        this.paymentData.id
      );
      console.log("Current payment status:", this.paymentData.status);
      console.log("Display time:", this.currentDisplayTime);
      console.log("Server time:", this.paymentData.timeRemainingSeconds);

      // Use fetch with timeout for guaranteed response
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.log("⏰ Expiry request timeout after 1 second");
        controller.abort();
      }, 1000); // 1-second timeout

      const response = await fetch(this.config.ajax_url, {
        method: "POST",
        headers: {"Content-Type": "application/x-www-form-urlencoded"},
        body: new URLSearchParams({
          action: "stablecoinpay_trigger_immediate_expiry",
          payment_id: this.paymentData.id,
          order_id: this.config.order_id,
          nonce: this.config.nonce,
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const responseTime = Date.now() - startTime;

      console.log(`✅ Expiry response received in ${responseTime}ms:`, data);

      if (data.success) {
        console.log("🎯 Payment successfully expired:", data.data);

        // Update payment data immediately
        this.paymentData.status = data.data.status || "EXPIRED";
        this.paymentData.isExpired = true;
        this.paymentData.timeRemainingSeconds = 0;
        this.currentDisplayTime = 0;

        // Update UI immediately
        this.updateStatusDisplay();
        this.updateCountdownDisplay(0);

        // Stop all timers
        this.stopCountdown();
        this.stopPaymentPolling();

        // Show expiry message
        this.showMessage(
          this.config.strings.expired || "Payment expired",
          "error"
        );

        // Redirect if needed
        if (data.data.redirect_url) {
          console.log("Redirecting to:", data.data.redirect_url);
          setTimeout(() => {
            window.location.href = data.data.redirect_url;
          }, 2000);
        }
      } else {
        console.error("❌ Immediate expiry failed:", data);
        // Reset flag and try regular status check
        this.expiryTriggered = false;
        this.checkPaymentStatus();
      }
    } catch (error) {
      console.error("💥 Failed to trigger immediate expiry:", error);
      // Reset flag and fallback to increased polling
      this.expiryTriggered = false;
      this.increasePollingFrequency();
    }
  }

  async copyToClipboard(text, icon = null) {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
      } else {
        // Fallback for older browsers
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand("copy");
        textArea.remove();
      }

      // Show success feedback on icon if provided
      if (icon) {
        this.showCopySuccess(icon);
      } else {
        this.showMessage(this.config.strings.copied, "success");
      }
    } catch (error) {
      console.error("Copy failed:", error);
      this.showMessage(this.config.strings.copy_failed, "error");
    }
  }

  showCopySuccess(icon) {
    // Add copied class to trigger animation
    icon.classList.add("copied");

    // Show brief success message
    this.showMessage("Copied!", "success");

    // Remove the class after animation completes
    setTimeout(() => {
      icon.classList.remove("copied");
    }, 600);
  }

  showMessage(message, type = "info") {
    // Remove existing messages
    document.querySelectorAll(".copy-success, .error-message").forEach(el => {
      el.style.display = "none";
    });

    let messageElement;

    if (type === "success") {
      messageElement = document.getElementById("copy-success");
      messageElement.textContent = message;
    } else if (type === "error") {
      messageElement = document.getElementById("error-message");
      messageElement.querySelector(".error-text").textContent = message;
    } else {
      // Create temporary info message
      messageElement = document.createElement("div");
      messageElement.className = "copy-success";
      messageElement.textContent = message;
      messageElement.style.background = "#3b82f6";
      document.body.appendChild(messageElement);
    }

    messageElement.style.display = "block";

    // Auto-hide after 3 seconds
    setTimeout(() => {
      messageElement.style.display = "none";
      if (type === "info" && messageElement.parentNode) {
        messageElement.parentNode.removeChild(messageElement);
      }
    }, 3000);
  }

  showSuccessAnimation() {
    // Create confetti effect
    this.createConfetti();

    // Add success class to body
    document.body.classList.add("payment-success");
  }

  createConfetti() {
    const colors = [
      "#ff6b6b",
      "#4ecdc4",
      "#45b7d1",
      "#96ceb4",
      "#feca57",
      "#ff9ff3",
    ];

    for (let i = 0; i < 50; i++) {
      setTimeout(() => {
        const confetti = document.createElement("div");
        confetti.className = "confetti";
        confetti.style.left = Math.random() * 100 + "vw";
        confetti.style.backgroundColor =
          colors[Math.floor(Math.random() * colors.length)];
        confetti.style.animationDelay = Math.random() * 3 + "s";
        confetti.style.animationDuration = Math.random() * 3 + 2 + "s";

        document.body.appendChild(confetti);

        // Remove confetti after animation
        setTimeout(() => {
          if (confetti.parentNode) {
            confetti.parentNode.removeChild(confetti);
          }
        }, 5000);
      }, i * 50);
    }
  }

  initializePaymentMethodSelector() {
    const paymentMethodSelect = document.getElementById(
      "payment-method-select"
    );

    if (!paymentMethodSelect) {
      console.error(
        "Payment method select element not found during initialization"
      );
      return;
    }

    // Set current selections and update icon
    this.setCurrentSelections();
    this.updateSelectedIcon();

    // Add event listeners
    this.setupSelectorEventListeners();
  }

  setupSelectorEventListeners() {
    const paymentMethodSelect = document.getElementById(
      "payment-method-select"
    );
    const updateBtn = document.getElementById("update-payment-btn");

    if (!paymentMethodSelect) {
      console.error("Payment method select element not found");
      return;
    }

    // Payment method selection change - auto switch
    paymentMethodSelect.addEventListener("change", () => {
      this.handleAutoPaymentMethodSwitch();
    });

    // Update button click
    if (updateBtn) {
      updateBtn.addEventListener("click", () => {
        this.handleUpdatePayment();
      });
    }
  }

  async handleAutoPaymentMethodSwitch() {
    const paymentMethodSelect = document.getElementById(
      "payment-method-select"
    );
    const selectedOption =
      paymentMethodSelect.options[paymentMethodSelect.selectedIndex];

    if (!selectedOption || !selectedOption.value) {
      console.log("No valid option selected");
      return;
    }

    const selectedToken = selectedOption.getAttribute("data-token");
    const selectedBlockchain = selectedOption.getAttribute("data-blockchain");
    const currentToken = this.paymentData.token;
    const currentBlockchain = this.paymentData.blockchain;

    // Only switch if selection is different from current
    if (
      selectedToken !== currentToken ||
      selectedBlockchain !== currentBlockchain
    ) {
      console.log("Starting payment method switch...");

      // Show switching indicator
      this.showSwitchingIndicator(true);

      try {
        await this.updatePaymentSelection(selectedToken, selectedBlockchain);
        this.updateSelectedIcon();
        console.log("Payment method switch completed successfully");
      } catch (error) {
        console.error("Error auto-switching payment method:", error);
        this.showMessage(
          "Failed to switch payment method. Please try again.",
          "error"
        );

        // Revert selection on error
        this.setCurrentSelections();
        this.updateSelectedIcon();
      } finally {
        this.showSwitchingIndicator(false);
      }
    } else {
      console.log("Same payment method selected, just updating icon");
      // Just update the icon if same selection
      this.updateSelectedIcon();
    }
  }

  handlePaymentMethodSelection() {
    // Legacy method - kept for backward compatibility
    // Now just updates the icon
    this.updateSelectedIcon();
  }

  validateSelection() {
    const paymentMethodSelect = document.getElementById(
      "payment-method-select"
    );
    const updateBtn = document.getElementById("update-payment-btn");
    const selectedOption =
      paymentMethodSelect.options[paymentMethodSelect.selectedIndex];

    if (!selectedOption || !selectedOption.value) {
      updateBtn.disabled = true;
      return;
    }

    const selectedToken = selectedOption.getAttribute("data-token");
    const selectedBlockchain = selectedOption.getAttribute("data-blockchain");
    const currentToken = this.paymentData.token;
    const currentBlockchain = this.paymentData.blockchain;

    // Enable button only if selection is different from current
    const isDifferent =
      selectedToken !== currentToken || selectedBlockchain !== currentBlockchain;
    const isValid = selectedToken && selectedBlockchain;

    updateBtn.disabled = !isDifferent || !isValid;
  }

  setCurrentSelections() {
    const paymentMethodSelect = document.getElementById(
      "payment-method-select"
    );
    const updateActions = document.getElementById("update-actions");

    if (!paymentMethodSelect) {
      console.error("Payment method select not found in setCurrentSelections");
      return;
    }

    // Find and select the current payment method
    const currentToken = this.paymentData.token;
    const currentBlockchain = this.paymentData.blockchain;

    console.log("Setting current selections for:", {
      token: currentToken,
      blockchain: currentBlockchain,
    });

    let found = false;
    for (let i = 0; i < paymentMethodSelect.options.length; i++) {
      const option = paymentMethodSelect.options[i];
      const optionToken = option.getAttribute("data-token");
      const optionBlockchain = option.getAttribute("data-blockchain");

      if (
        optionToken === currentToken &&
        optionBlockchain === currentBlockchain
      ) {
        paymentMethodSelect.selectedIndex = i;
        found = true;
        console.log("Found and selected option:", option.textContent);
        break;
      }
    }

    if (!found) {
      console.warn("Could not find matching option for current payment method");
    }

    // Hide update actions
    if (updateActions) {
      updateActions.style.display = "none";
    }
  }

  showSwitchingIndicator(show) {
    const updateActions = document.getElementById("update-actions");
    const paymentMethodSelect = document.getElementById(
      "payment-method-select"
    );

    if (show) {
      updateActions.style.display = "block";
      if (paymentMethodSelect) paymentMethodSelect.disabled = true;
    } else {
      updateActions.style.display = "none";
      if (paymentMethodSelect) paymentMethodSelect.disabled = false;
    }
  }

  async handleUpdatePayment() {
    const paymentMethodSelect = document.getElementById(
      "payment-method-select"
    );
    const selectedOption =
      paymentMethodSelect.options[paymentMethodSelect.selectedIndex];

    if (!selectedOption || !selectedOption.value) {
      this.showMessage("Please select a payment method", "error");
      return;
    }

    const selectedToken = selectedOption.getAttribute("data-token");
    const selectedBlockchain = selectedOption.getAttribute("data-blockchain");

    if (!selectedToken || !selectedBlockchain) {
      this.showMessage("Invalid payment method selection", "error");
      return;
    }

    // Show loading state
    this.showUpdateLoading(true);

    try {
      await this.updatePaymentSelection(selectedToken, selectedBlockchain);
    } catch (error) {
      console.error("Error updating payment:", error);
      this.showMessage(
        "Failed to update payment method. Please try again.",
        "error"
      );
    } finally {
      this.showUpdateLoading(false);
    }
  }

  showUpdateLoading(isLoading) {
    const updateBtn = document.getElementById("update-payment-btn");
    const btnText = updateBtn.querySelector(".btn-text");
    const btnLoading = updateBtn.querySelector(".btn-loading");

    if (isLoading) {
      btnText.style.display = "none";
      btnLoading.style.display = "flex";
      updateBtn.disabled = true;
    } else {
      btnText.style.display = "block";
      btnLoading.style.display = "none";
      this.validateSelection(); // Re-enable button if selection is valid
    }
  }

  async updatePaymentSelection(token, blockchain) {
    try {
      console.log("🌐 Making AJAX request to switch payment method:", {
        url: this.config.ajax_url,
        order_id: this.config.order_id || this.paymentData.orderId,
        token: token,
        blockchain: blockchain,
        nonce: this.config.nonce,
      });
      console.log("🌐 Full config object:", this.config);

      // Create new payment with selected token/blockchain
      const response = await fetch(this.config.ajax_url, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          action: "stablecoinpay_switch_payment_method",
          order_id: this.config.order_id || this.paymentData.orderId,
          token: token,
          blockchain: blockchain,
          nonce: this.config.nonce || "",
        }),
      });

      // Check if response is ok first
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log("AJAX response:", data);

      if (data.success) {
        console.log("Payment method switch successful, updating data...");

        // Update payment data with server-side timer info
        const paymentInfo = data.data.payment_info;
        this.paymentData = {
          ...this.paymentData,
          ...paymentInfo,
          timeRemainingSeconds: paymentInfo.timeRemainingSeconds,
          isExpired: paymentInfo.isExpired,
          serverTime: paymentInfo.serverTime,
        };

        // CRITICAL FIX: Update config payment_id for polling
        if (paymentInfo.id) {
          this.config.payment_id = paymentInfo.id;
          console.log("Updated config payment_id to:", this.config.payment_id);
        }

        console.log(
          "Updated payment data with server timer:",
          this.paymentData
        );

        // Update UI
        this.updatePaymentDisplay();

        // CRITICAL FIX: Restart polling with new payment ID
        this.stopPaymentPolling();
        this.startPaymentPolling();

        // Restart countdown with new server-side timer
        this.stopCountdown();
        this.startCountdown();

        // Show success message
        this.showMessage("Payment method switched successfully!", "success");

        // Update icon to match new selection (don't reset selection)
        this.updateSelectedIcon();
      } else {
        // Handle error response properly - WordPress sends error message in data.data
        const errorMessage =
          typeof data.data === "string"
            ? data.data
            : data.message || "Failed to update payment method";
        console.error("Payment method switch failed:", errorMessage);
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error("Error updating payment method:", error);
      this.showMessage(
        "Failed to update payment method. Please try again.",
        "error"
      );

      // Revert selections
      this.setCurrentSelections();
      throw error;
    }
  }

  updatePaymentDisplay() {
    // Get the proper label for the amount row (matches PHP logic)
    const stablecoins = ["USDT", "USDC", "DAI", "PYUSD", "BUSD"];
    const token = this.paymentData.token;
    const blockchain = this.paymentData.blockchain;
    const network = this.paymentData.network;
    const isStablecoin = stablecoins.includes(token);
    const isTestnet =
      (typeof network === "string" &&
        (network.toLowerCase().includes("test") || network.toLowerCase() !== "mainnet"));
    let label;
    if (isStablecoin) {
      label = `${this.getTokenLabel(token, blockchain)} on ${blockchain}`;
    } else {
      label = `${token} on ${blockchain}`;
    }
    if (isTestnet) {
      label += " (Testnet)";
    }

    // Update amount display
    const amountValue = document.querySelector(".amount-value");
    const amountLabel = document.querySelector(".amount-label");
    if (amountValue) {
      amountValue.textContent = this.paymentData.amount;
    }
    if (amountLabel) amountLabel.textContent = label;

    // Update address display
    const addressValue = document.querySelector(".address-value");
    if (addressValue) addressValue.textContent = this.paymentData.walletAddress;

    // Update QR code
    const qrCodeImg = document.querySelector(".qr-code img");
    if (qrCodeImg && this.paymentData.qrCode) {
      qrCodeImg.src = this.paymentData.qrCode;
    }

    // Update copy button data - USE EXACT API AMOUNT
    const amountCopyBtn = document.querySelector(".amount-display .copy-icon");
    const addressCopyBtn = document.querySelector(
      ".address-display .copy-icon"
    );

    if (amountCopyBtn)
      amountCopyBtn.setAttribute("data-copy", this.paymentData.amount);
    if (addressCopyBtn)
      addressCopyBtn.setAttribute("data-copy", this.paymentData.walletAddress);

    // Update instructions
    this.updateInstructions();
  }

  updateInstructions() {
    // Get the proper token label with network info
    const tokenLabel = this.getTokenLabel(
      this.paymentData.token,
      this.paymentData.blockchain
    );

    const instructions = document.querySelector(".instructions-content ol");
    if (instructions) {
      // CRITICAL FIX: Use exact amount from API without manipulation
      instructions.innerHTML = `
                <li>Open your crypto wallet and select <strong>${tokenLabel}</strong> on <strong>${this.getSimplifiedNetworkText(
        this.paymentData.blockchain,
        this.paymentData.network
      )}</strong> network</li>
                <li>Send exactly <strong>${
                  this.paymentData.amount
                } ${tokenLabel}</strong> to address:<br><code class="instruction-address">${
        this.paymentData.walletAddress
      }</code></li>
                <li>Verify amount, address and network before confirming</li>
                <li>Wait for confirmation (usually 1-5 minutes)</li>
            `;
    }

    const warningBox = document.querySelector(".warning-box");
    if (warningBox) {
      warningBox.innerHTML = `
                <strong>Important:</strong>
                Send the exact amount, otherwise payment will fail.
            `;
    }
  }

  /**
   * Get simplified network display text
   *
   * @param {string} blockchain Blockchain name
   * @param {string} network Network type (MAINNET, TESTNET, etc.)
   * @returns {string} Simplified network text
   */
  getSimplifiedNetworkText(blockchain, network) {
    const blockchainName = blockchain || "Unknown";
    const networkLower = (network || "mainnet").toLowerCase();
    const isTestnet =
      networkLower.includes("testnet") || networkLower.includes("test");

    // For mainnet, just show blockchain name
    // For testnet, add (Testnet) suffix
    return isTestnet ? `${blockchainName} (Testnet)` : blockchainName;
  }

  /**
   * Get token label with network information (e.g., "USDT TRC20")
   *
   * @param {string} token Token symbol
   * @param {string} blockchain Blockchain name
   * @returns {string} Token label with network
   */
  getTokenLabel(token, blockchain) {
    // Handle native coins (no network suffix needed)
    const nativeCoins = {
      ETH: "Ethereum",
      BNB: "Binance Coin",
      TRX: "Tron",
      SOL: "Solana",
      TON: "TON",
    };

    if (nativeCoins[token]) {
      return nativeCoins[token];
    }

    // Handle stablecoins with network suffixes
    const networkSuffixes = {
      ETHEREUM: "ERC20",
      BSC: "BEP20",
      TRON: "TRC20",
      SOLANA: "SPL",
      TON: "TON",
    };

    const networkSuffix = networkSuffixes[blockchain.toUpperCase()];

    if (networkSuffix) {
      return `${token} ${networkSuffix}`;
    }

    // Fallback to just token symbol
    return token;
  }

  showPaymentMethodSelectorLoading() {
    // Disable the selector during update
    const paymentMethodSelect = document.getElementById(
      "payment-method-select"
    );

    if (paymentMethodSelect) paymentMethodSelect.disabled = true;
  }

  hidePaymentMethodSelectorLoading() {
    // Re-enable the selector
    const paymentMethodSelect = document.getElementById(
      "payment-method-select"
    );

    if (paymentMethodSelect) paymentMethodSelect.disabled = false;

    // Reset to current selections and update icon
    this.setCurrentSelections();
    this.updateSelectedIcon();
  }

  cleanup() {
    // Stop all intervals
    this.stopPaymentPolling();
    this.stopCountdown();

    // Remove all event listeners to prevent memory leaks
    if (this.visibilityChangeHandler) {
      document.removeEventListener(
        "visibilitychange",
        this.visibilityChangeHandler
      );
      this.visibilityChangeHandler = null;
    }

    if (this.beforeUnloadHandler) {
      window.removeEventListener("beforeunload", this.beforeUnloadHandler);
      this.beforeUnloadHandler = null;
    }

    if (this.popStateHandler) {
      window.removeEventListener("popstate", this.popStateHandler);
      this.popStateHandler = null;
    }

    if (this.pageHideHandler) {
      window.removeEventListener("pagehide", this.pageHideHandler);
      this.pageHideHandler = null;
    }

    // Clear any pending timeouts
    if (this.redirectTimeout) {
      clearTimeout(this.redirectTimeout);
      this.redirectTimeout = null;
    }

    if (this.messageTimeout) {
      clearTimeout(this.messageTimeout);
      this.messageTimeout = null;
    }

    // Clear references to prevent memory leaks
    this.paymentData = null;
    this.config = null;

    console.log("StablecoinPay: Cleanup completed");
  }

  /**
   * Get blockchain-specific default confirmations
   */
  getBlockchainDefaultConfirmations(blockchain, network) {
    const blockchainLower = (blockchain || "ethereum").toLowerCase();
    const networkLower = (network || "mainnet").toLowerCase();
    const isTestnet = networkLower === "testnet";

    const blockchainDefaults = {
      ethereum: isTestnet ? 3 : 12,
      bsc: isTestnet ? 3 : 6,
      tron: isTestnet ? 1 : 3,
      solana: isTestnet ? 1 : 1,
      ton: isTestnet ? 1 : 1,
    };

    return blockchainDefaults[blockchainLower] || 6;
  }

  /**
   * Get token-specific decimal precision
   *
   * @param {string} token Token symbol
   * @returns {number} Decimal precision
   */
  getTokenPrecision(token) {
    const tokenPrecision = {
      USDT: 6, // Tether uses 6 decimals
      USDC: 6, // USD Coin uses 6 decimals
      DAI: 18, // DAI uses 18 decimals
      PYUSD: 6, // PayPal USD uses 6 decimals
      BUSD: 18, // Binance USD uses 18 decimals
      ETH: 18, // Ethereum uses 18 decimals
      BNB: 18, // Binance Coin uses 18 decimals
      TRX: 6, // Tron uses 6 decimals
      SOL: 9, // Solana uses 9 decimals
      TON: 9, // TON uses 9 decimals
    };

    return tokenPrecision[token?.toUpperCase()] || 8; // Default to 8 decimals
  }

  /**
   * DEPRECATED: DO NOT USE - This function manipulates API amounts incorrectly
   * The API returns exact amounts that should be used as-is without modification
   *
   * @deprecated This function causes critical amount display issues
   * @param {number|string} amount Amount to standardize
   * @param {string} token Token type
   * @returns {string} Standardized amount
   */
  standardizeAmount(amount, token) {
    // CRITICAL WARNING: This function should not be used for display purposes
    // API amounts should be used exactly as returned
    console.warn(
      "standardizeAmount() is deprecated - use exact API amounts instead"
    );

    if (!amount || isNaN(amount)) {
      return "0";
    }

    // Return the amount as-is to prevent manipulation
    return String(amount);
  }

  /**
   * Compare amounts with token-specific precision
   *
   * @param {number|string} amount1 First amount
   * @param {number|string} amount2 Second amount
   * @param {string} token Token type
   * @returns {boolean} True if amounts are equal within token precision
   */
  amountsEqual(amount1, amount2, token) {
    const standardized1 = this.standardizeAmount(amount1, token);
    const standardized2 = this.standardizeAmount(amount2, token);

    return standardized1 === standardized2;
  }

  updatePaymentInstructionsVisibility() {
    const paymentInstructionsAccordion = document.querySelector(
      ".payment-instructions-accordion"
    );
    if (paymentInstructionsAccordion) {
      if (
        ["DETECTED", "CONFIRMED", "EXPIRED", "CANCELED"].includes(
          this.paymentData.status
        )
      ) {
        paymentInstructionsAccordion.style.display = "none";
      } else {
        paymentInstructionsAccordion.style.display = "block";
      }
    }
  }

  updatePaymentMethodSelectorVisibility() {
    const paymentMethodSelectorExternal = document.querySelector(
      ".payment-method-selector-external"
    );
    if (paymentMethodSelectorExternal) {
      if (
        ["DETECTED", "CONFIRMED", "EXPIRED", "CANCELED"].includes(
          this.paymentData.status
        )
      ) {
        paymentMethodSelectorExternal.style.display = "none";
      } else {
        paymentMethodSelectorExternal.style.display = "flex";
      }
    }
  }
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  const instance = new StablecoinPayPaymentPage();

  // Set both global variable names for compatibility
  window.stablecoinPayInstance = instance; // Used by main version
  window.stablecoinPayPaymentPage = instance; // Used by minified version
});
