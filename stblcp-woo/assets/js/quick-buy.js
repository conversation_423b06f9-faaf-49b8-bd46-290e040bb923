(function ($) {
  "use strict";

  class StablecoinPayQuickBuy {
    constructor() {
      this.config = window.stablecoinpay_quick_buy || {};
      this.selectedMethod = null;
      this.currentContext = null;
      this.currentProductData = null;
      this.emailModalData = null;
      this.currentAjaxRequest = null; // Track AJAX requests for cleanup
      this.previouslyFocusedElement = null; // Track focus for accessibility

      this.init();
    }

    init() {
      this.bindEvents();
      this.initializeButtons();

      // BLOCK-BASED CART FIX: Re-initialize when blocks are updated
      this.initBlockSupport();
    }

    bindEvents() {
      // Quick buy button clicks - use more specific selector to avoid conflicts
      $(document).on(
        "click",
        ".stablecoinpay-quick-buy-container .quick-buy-primary",
        this.handleQuickBuy.bind(this)
      );

      // Dropdown toggle
      $(document).on(
        "click",
        ".stablecoinpay-quick-buy-container .quick-buy-dropdown-toggle",
        this.toggleDropdown.bind(this)
      );

      // Dropdown option selection
      $(document).on(
        "click",
        ".stablecoinpay-quick-buy-container .quick-buy-dropdown-option",
        this.selectDropdownOption.bind(this)
      );

      // BLOCK-BASED CART FIX: Use more robust event delegation for dynamic content
      // These events will work even when modal is added dynamically by blocks
      $(document).on(
        "click",
        ".stablecoinpay-email-modal .email-modal-close, .stablecoinpay-email-modal .btn-cancel",
        e => {
          e.preventDefault();
          e.stopPropagation();
          this.closeEmailModal();
        }
      );

      $(document).on("click", ".stablecoinpay-email-modal .btn-continue", e => {
        e.preventDefault();
        e.stopPropagation();
        this.handleEmailSubmission(e);
      });

      // Enhanced modal keyboard navigation
      $(document).on("keydown", this.handleModalKeydown.bind(this));

      // Click outside modal to close
      $(document).on(
        "click",
        ".stablecoinpay-email-modal .email-modal-overlay",
        this.handleModalOverlayClick.bind(this)
      );

      // UX IMPROVEMENT: Clear saved email button
      $(document).on(
        "click",
        ".stablecoinpay-email-modal .clear-saved-email",
        e => {
          e.preventDefault();
          e.stopPropagation();
          this.handleClearSavedEmail();
        }
      );

      // UX IMPROVEMENT: Show/hide clear button based on email input
      $(document).on(
        "input",
        ".stablecoinpay-email-modal #customer_email",
        e => {
          const $input = $(e.target);
          const $clearButton = $input.siblings(".clear-saved-email");
          const email = $input.val().trim();

          if (email && this.isValidEmail(email)) {
            $clearButton.show();
          } else {
            $clearButton.hide();
          }
        }
      );

      // Initialize modal isolation if modal exists
      const $modal = $("#stablecoinpay-email-modal");
      if ($modal.length) {
        this.isolateEmailModal($modal);
      }

      // Close dropdown when clicking outside - but not on WooCommerce buttons
      $(document).on("click", this.handleOutsideClick.bind(this));

      // Product variation changes (for variable products)
      $("form.variations_form").on(
        "found_variation",
        this.handleVariationChange.bind(this)
      );
      $("form.variations_form").on(
        "reset_data",
        this.handleVariationReset.bind(this)
      );
    }

    isolateEmailModal($modal) {
      // Prevent modal form from interfering with other forms
      $modal.on("submit", function (e) {
        e.stopPropagation();
      });

      // Ensure modal inputs don't trigger validation on other forms
      $modal.find("input").on("invalid", function (e) {
        e.stopPropagation();
      });

      // CRITICAL FIX: Ensure novalidate is present to prevent HTML5 validation
      $modal.find("form").attr("novalidate", "novalidate");
    }

    initializeButtons() {
      $(".stablecoinpay-quick-buy-container").each((_, container) => {
        const $container = $(container);
        const context = $container.data("context");

        // Set initial selected method
        this.updateSelectedMethod($container);

        // Show/hide based on product availability
        if (context === "product") {
          this.checkProductAvailability($container);
        }
      });
    }

    handleQuickBuy(e) {
      e.preventDefault();

      const $button = $(e.currentTarget);
      const $container = $button.closest(".stablecoinpay-quick-buy-container");

      this.currentContext = $container.data("context");

      // Show loading state
      this.setButtonLoading($button, true);

      // Collect product data if on product page
      if (this.currentContext === "product") {
        this.currentProductData = this.collectProductData($container);

        // Validate product data
        if (!this.validateProductData(this.currentProductData)) {
          this.setButtonLoading($button, false);
          return;
        }
      }

      // Get selected payment method
      const selectedMethod = this.getSelectedMethod($container);

      // Store selected method for use in AJAX
      this.selectedMethod = selectedMethod;

      // Make AJAX request
      this.processQuickBuy(selectedMethod, $button);
    }

    collectProductData($container) {
      const productId = $container.data("product-id");
      const $form = $("form.cart, form.variations_form").first();

      let quantity = 1;
      let variationId = 0;
      let variationData = {};

      // Get quantity - check multiple possible selectors
      const $quantityInput = $form.find('input[name="quantity"], .qty');
      if ($quantityInput.length) {
        quantity = Math.max(1, parseInt($quantityInput.val()) || 1);
      }

      // Get variation data for variable products
      const $variationIdInput = $form.find('input[name="variation_id"]');
      if ($variationIdInput.length) {
        variationId = parseInt($variationIdInput.val()) || 0;

        // Collect variation attributes
        $form
          .find('select[name^="attribute_"], input[name^="attribute_"]')
          .each(function () {
            const name = $(this).attr("name");
            const value = $(this).val();
            if (value) {
              variationData[name] = value;
            }
          });
      }

      return {
        product_id: productId,
        quantity: quantity,
        variation_id: variationId,
        variation_data: variationData,
      };
    }

    validateProductData(productData) {
      // Check if product is selected
      if (!productData.product_id) {
        this.showError(this.config.strings.error);
        return false;
      }

      // Check if variation is selected for variable products
      const $variationForm = $("form.variations_form");
      if ($variationForm.length && !productData.variation_id) {
        this.showError(this.config.strings.select_options);
        return false;
      }

      // Check quantity
      if (productData.quantity < 1) {
        this.showError(this.config.strings.invalid_quantity);
        return false;
      }

      return true;
    }

    processQuickBuy(selectedMethod, $button) {
      const requestData = {
        action: "stablecoinpay_quick_buy",
        nonce: this.config.nonce,
        context: this.currentContext,
        method_key: selectedMethod.method_key,
        token: selectedMethod.token,
        blockchain: selectedMethod.blockchain,
      };

      // Add product data if applicable
      if (this.currentContext === "product" && this.currentProductData) {
        Object.assign(requestData, this.currentProductData);
      }

      $.ajax({
        url: this.config.ajax_url,
        type: "POST",
        data: requestData,
        success: response => {
          this.setButtonLoading($button, false);

          if (response.success) {
            if (response.data.requires_email) {
              this.showEmailModal(response.data);
            } else if (response.data.redirect_url) {
              window.location.href = response.data.redirect_url;
            }
          } else {
            this.showError(response.data.message || this.config.strings.error);
          }
        },
        error: (xhr, status) => {
          this.setButtonLoading($button, false);

          // STANDARDIZED ERROR HANDLING: Provide specific error messages
          let errorMessage = this.config.strings.error;
          if (status === "timeout") {
            errorMessage = "Request timed out. Please try again.";
          } else if (status === "abort") {
            errorMessage = "Request was cancelled. Please try again.";
          } else if (xhr.status === 0) {
            errorMessage =
              "No internet connection. Please check your connection and try again.";
          } else if (xhr.status >= 500) {
            errorMessage = "Server error. Please try again in a few moments.";
          }

          this.showError(errorMessage);
        },
      });
    }

    showEmailModal(data) {
      const $modal = $("#stablecoinpay-email-modal");

      if ($modal.length === 0) {
        console.error("StablecoinPay: Email modal not found in DOM");
        return;
      }

      // Store the currently focused element to restore later
      this.previouslyFocusedElement = document.activeElement;

      // Store data for later use
      this.emailModalData = data;

      // Clear any previous form data and errors
      const $form = $modal.find("form");
      if ($form.length && $form[0]) {
        $form[0].reset();
      } else {
        // Fallback: manually clear inputs
        $modal.find("input").val("");
      }
      this.clearFormErrors();

      // UX IMPROVEMENT: Pre-fill email from localStorage if available
      this.prefillSavedEmail($modal);

      // Show modal
      $modal.show().attr("aria-hidden", "false");

      // Enhanced modal setup with better timing
      setTimeout(() => {
        // Focus management and accessibility
        this.setupModalFocus($modal);

        // Remove duplicate event handlers (document handlers already exist)
        // Focus on email input
        const $emailInput = $modal.find("#customer_email");
        if ($emailInput.length && $emailInput.is(":visible")) {
          $emailInput.focus();
        }
      }, 100);

      // Prevent body scroll
      $("body").addClass("stablecoinpay-modal-open");
    }

    closeEmailModal() {
      const $modal = $("#stablecoinpay-email-modal");
      $modal.hide().attr("aria-hidden", "true");
      $("body").removeClass("stablecoinpay-modal-open");

      // Clear form
      const $form = $modal.find("form");
      if ($form.length && $form[0]) {
        $form[0].reset();
      } else {
        // Fallback: manually clear inputs
        $modal.find("input").val("");
      }
      this.clearFormErrors();

      // CRITICAL FIX: Reset all button states when modal is closed
      this.resetAllButtonStates();

      // Restore focus to previously focused element
      if (
        this.previouslyFocusedElement &&
        this.previouslyFocusedElement.focus
      ) {
        try {
          this.previouslyFocusedElement.focus();
        } catch (e) {
          // Fallback if element is no longer focusable
          // Focus restoration failed, continue silently
        }
      }
      this.previouslyFocusedElement = null;

      // Clear stored modal data
      this.emailModalData = null;

      // Clear any pending AJAX requests - OpenNode pattern
      if (this.currentAjaxRequest) {
        this.currentAjaxRequest.abort();
        this.currentAjaxRequest = null;
      }
    }

    handleEmailSubmission(e) {
      e.preventDefault();
      e.stopPropagation();

      const $submitBtn = $(e.currentTarget);
      const $form = $submitBtn.closest("form");

      // Get form data
      const email = $form.find("#customer_email").val().trim();
      const $nameField = $form.find("#customer_name");
      const name = $nameField.length ? $nameField.val().trim() : "";

      // Validate email
      if (!email) {
        this.showFieldError("email-error", "Email address is required");
        $form.find("#customer_email").focus();
        return;
      }

      if (!this.isValidEmail(email)) {
        this.showFieldError(
          "email-error",
          this.config.strings.invalid_email ||
            "Please enter a valid email address"
        );
        $form.find("#customer_email").focus();
        return;
      }

      // Clear previous errors
      this.clearFormErrors();

      // Show loading state
      this.setSubmitButtonLoading($submitBtn, true);

      // UX IMPROVEMENT: Save email to localStorage for future use
      this.saveEmailToStorage(email);

      // Submit email data
      this.submitEmailData(email, name, $submitBtn);
    }

    submitEmailData(email, name, $submitBtn) {
      const requestData = {
        action: "stablecoinpay_email_collection",
        nonce: this.config.nonce,
        email: email,
        name: name,
        context: this.currentContext,
      };

      // Add method_key to request data
      if (this.emailModalData && this.emailModalData.method_key) {
        requestData.method_key = this.emailModalData.method_key;
      }

      // Add stored data
      if (this.emailModalData) {
        if (this.emailModalData.product_data) {
          requestData.product_data = this.emailModalData.product_data;
        }
        if (this.emailModalData.cart_data) {
          requestData.cart_data = this.emailModalData.cart_data;
        }
      }

      // Track AJAX request for cleanup
      this.currentAjaxRequest = $.ajax({
        url: this.config.ajax_url,
        type: "POST",
        data: requestData,
        timeout: 30000, // 30 second timeout
        success: response => {
          this.setSubmitButtonLoading($submitBtn, false);
          this.currentAjaxRequest = null;

          if (response.success) {
            if (response.data && response.data.redirect_url) {
              // Direct redirect - OpenNode pattern
              window.location.href = response.data.redirect_url;
            } else {
              this.showError(
                "Payment processing failed - no redirect URL provided"
              );
            }
          } else {
            this.showError(
              response.data && response.data.message
                ? response.data.message
                : this.config.strings.error
            );
          }
        },
        error: (_, status, error) => {
          this.setSubmitButtonLoading($submitBtn, false);
          this.currentAjaxRequest = null;

          if (status === "timeout") {
            this.showError("Request timed out. Please try again.");
          } else {
            this.showError("Network error: " + error);
          }
        },
      });
    }

    resetAllButtonStates() {
      // Reset all quick buy buttons to normal state - OpenNode pattern
      $(".stablecoinpay-quick-buy-container .quick-buy-primary").each(
        (_, button) => {
          const $button = $(button);

          // Remove loading state
          $button.prop("disabled", false).removeClass("loading");

          // Restore original button text structure
          this.restoreButtonText($button);
        }
      );
    }

    /**
     * Enhanced modal keyboard navigation and accessibility
     */
    handleModalKeydown(e) {
      const $modal = $("#stablecoinpay-email-modal");

      // Only handle if modal is visible
      if (!$modal.is(":visible")) {
        return;
      }

      switch (e.key) {
        case "Escape":
          e.preventDefault();
          this.closeEmailModal();
          break;

        case "Tab":
          this.handleModalTabNavigation(e, $modal);
          break;

        case "Enter":
          // If focus is on continue button or email input, submit form
          const $target = $(e.target);
          if (
            $target.hasClass("btn-continue") ||
            $target.is("#customer_email")
          ) {
            e.preventDefault();
            const $continueBtn = $modal.find(".btn-continue");
            if ($continueBtn.length && !$continueBtn.prop("disabled")) {
              this.handleEmailSubmission(e);
            }
          }
          break;
      }
    }

    /**
     * Handle Tab key navigation within modal for accessibility
     */
    handleModalTabNavigation(e, $modal) {
      const focusableElements = $modal.find(
        'input:visible, button:visible, [tabindex]:not([tabindex="-1"])'
      );
      const firstElement = focusableElements.first();
      const lastElement = focusableElements.last();

      if (e.shiftKey) {
        // Shift + Tab (backward)
        if (document.activeElement === firstElement[0]) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        // Tab (forward)
        if (document.activeElement === lastElement[0]) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    }

    /**
     * Handle clicking on modal overlay to close modal
     */
    handleModalOverlayClick(e) {
      // Only close if clicking directly on the overlay, not on modal content
      if (e.target === e.currentTarget) {
        this.closeEmailModal();
      }
    }

    /**
     * Setup modal focus management and accessibility
     */
    setupModalFocus($modal) {
      // Ensure modal content is focusable for screen readers
      const $modalContent = $modal.find(".email-modal-content");
      if (!$modalContent.attr("tabindex")) {
        $modalContent.attr("tabindex", "-1");
      }
    }

    /**
     * Restore button text to original state with token information - OpenNode pattern
     */
    restoreButtonText($button) {
      const $container = $button.closest(".stablecoinpay-quick-buy-container");
      const $textSpan = $button.find(".quick-buy-text");
      const buttonText = this.config.strings.button_text || "Quick Pay with";

      // Get currently selected method
      const methodKey = $container.find(".quick-buy-selected-method").val();

      if (
        methodKey &&
        this.config.enabled_methods &&
        this.config.enabled_methods[methodKey]
      ) {
        const method = this.config.enabled_methods[methodKey];
        const displayLabel = method.label || method.token;

        // Restore full button text with token name
        $textSpan.html(
          buttonText +
            ' <span class="quick-buy-token-name">' +
            displayLabel +
            "</span>"
        );
      } else {
        // Fallback to basic text
        $textSpan.text(buttonText);
      }
    }

    // Utility methods
    toggleDropdown(e) {
      e.preventDefault();
      e.stopPropagation();

      const $toggle = $(e.currentTarget);
      const $dropdown = $toggle.siblings(".quick-buy-dropdown-menu");

      // Close other dropdowns
      $(".quick-buy-dropdown-menu").not($dropdown).hide();

      // Toggle current dropdown
      $dropdown.toggle();

      // Update ARIA attributes
      const isOpen = $dropdown.is(":visible");
      $toggle.attr("aria-expanded", isOpen);
    }

    selectDropdownOption(e) {
      e.preventDefault();

      const $option = $(e.currentTarget);
      const $container = $option.closest(".stablecoinpay-quick-buy-container");
      const $dropdown = $option.closest(".quick-buy-dropdown-menu");

      // Update selected method data
      $container
        .find(".quick-buy-selected-method")
        .val($option.data("method-key"));
      $container.find(".quick-buy-selected-token").val($option.data("token"));
      $container
        .find(".quick-buy-selected-blockchain")
        .val($option.data("blockchain"));

      // Update button display
      const $primaryButton = $container.find(".quick-buy-primary");
      const $icon = $primaryButton.find(".quick-buy-token-icon");
      const $tokenName = $primaryButton.find(".quick-buy-token-name");

      // Update icon
      $icon.attr("src", $option.find(".quick-buy-option-icon").attr("src"));
      $icon.attr("alt", $option.data("token"));

      // Update token name
      if ($tokenName.length > 0) {
        $tokenName.text($option.data("label"));
      }

      // Close dropdown
      $dropdown.hide();
      $container
        .find(".quick-buy-dropdown-toggle")
        .attr("aria-expanded", "false");
    }

    handleOutsideClick(e) {
      const $target = $(e.target);

      // CART PAGE FIX: Allow modal button clicks even within .cart elements
      if ($target.closest(".stablecoinpay-email-modal").length) {
        return; // Don't interfere with modal interactions
      }

      // Don't interfere with WooCommerce buttons
      if (
        $target.closest(".single_add_to_cart_button, .cart, .checkout").length
      ) {
        return;
      }

      if (!$target.closest(".quick-buy-split-button").length) {
        $(".quick-buy-dropdown-menu").hide();
        $(".quick-buy-dropdown-toggle").attr("aria-expanded", "false");
      }
    }

    handleVariationChange(_, __) {
      // Update product data when variation changes
      const $container = $(
        '.stablecoinpay-quick-buy-container[data-context="product"]'
      );
      if ($container.length) {
        this.checkProductAvailability($container);
      }
    }

    handleVariationReset(_) {
      // Hide quick buy button when variation is reset
      const $container = $(
        '.stablecoinpay-quick-buy-container[data-context="product"]'
      );
      if ($container.length) {
        $container.hide();
      }
    }

    updateSelectedMethod($container) {
      const methodKey = $container.find(".quick-buy-selected-method").val();
      if (methodKey && this.config.enabled_methods[methodKey]) {
        this.selectedMethod = this.config.enabled_methods[methodKey];
      }
    }

    getSelectedMethod($container) {
      return {
        method_key: $container.find(".quick-buy-selected-method").val(),
        token: $container.find(".quick-buy-selected-token").val(),
        blockchain: $container.find(".quick-buy-selected-blockchain").val(),
      };
    }

    checkProductAvailability($container) {
      // Check if add to cart button is available
      const $form = $("form.cart, form.variations_form").first();
      const $addToCartBtn = $form.find(".single_add_to_cart_button");

      if (
        $addToCartBtn.hasClass("disabled") ||
        $addToCartBtn.prop("disabled")
      ) {
        $container.hide();
      } else {
        $container.show();
      }
    }

    setButtonLoading($button, loading) {
      if (loading) {
        $button.prop("disabled", true).addClass("loading");
        $button.find(".quick-buy-text").text(this.config.strings.processing);

        // Force spinner positioning for cart page - JavaScript fallback
        this.ensureSpinnerCentering($button);
      } else {
        $button.prop("disabled", false).removeClass("loading");

        // Trigger cleanup event for fallback spinner
        $button.trigger("remove-loading.stablecoinpay");

        // Remove data attribute
        $button.removeAttr("data-loading-context");

        // Use the new restoreButtonText method for consistency
        this.restoreButtonText($button);
      }
    }

    /**
     * Ensure spinner is properly centered - JavaScript fallback for CSS issues
     */
    ensureSpinnerCentering($button) {
      // Add a small delay to ensure CSS has been applied
      setTimeout(() => {
        const $container = $button.closest(
          ".stablecoinpay-quick-buy-container"
        );
        const context = $container.data("context");

        // Apply additional inline styles for cart page if needed
        if (
          context === "cart" ||
          $button.closest(".woocommerce-cart, .cart_totals").length
        ) {
          // Force button positioning
          $button.css({
            position: "relative",
            display: "flex",
            "align-items": "center",
            "justify-content": "center",
          });

          // Add a data attribute to help with CSS targeting
          $button.attr("data-loading-context", "cart");

          // Ultimate fallback: Create a real DOM spinner element if CSS pseudo-element fails
          this.createFallbackSpinner($button);
        }
      }, 10);
    }

    /**
     * Create a real DOM spinner element as ultimate fallback
     */
    createFallbackSpinner($button) {
      // Check if fallback spinner already exists
      if ($button.find(".js-fallback-spinner").length > 0) {
        return;
      }

      // Create spinner element
      const $spinner = $(
        '<div class="js-fallback-spinner" style="' +
          "position: absolute !important; " +
          "right: 16px !important; " +
          "top: 50% !important; " +
          "transform: translateY(-50%) !important; " +
          "width: 16px !important; " +
          "height: 16px !important; " +
          "border: 2px solid rgba(255, 255, 255, 0.3) !important; " +
          "border-top: 2px solid white !important; " +
          "border-radius: 50% !important; " +
          "animation: spin 1s linear infinite !important; " +
          "z-index: 30 !important; " +
          "pointer-events: none !important; " +
          "margin: 0 !important; " +
          "padding: 0 !important;" +
          '"></div>'
      );

      // Add to button
      $button.append($spinner);

      // Remove when loading stops
      $button.on("remove-loading.stablecoinpay", () => {
        $spinner.remove();
      });
    }

    updateButtonText($button) {
      const $container = $button.closest(".stablecoinpay-quick-buy-container");
      const $tokenNameSpan = $button.find(".quick-buy-token-name");

      if ($tokenNameSpan.length > 0) {
        // Get the current selected token label
        const selectedLabel = $container
          .find(".quick-buy-selected-token")
          .val();
        const methodKey = $container.find(".quick-buy-selected-method").val();

        // Find the actual label from enabled methods
        let displayLabel = selectedLabel;
        if (
          this.config.enabled_methods &&
          this.config.enabled_methods[methodKey]
        ) {
          displayLabel = this.config.enabled_methods[methodKey].label;
        }

        $tokenNameSpan.text(displayLabel);
      }
    }

    setSubmitButtonLoading($button, loading) {
      const $text = $button.find(".btn-text");
      const $spinner = $button.find(".btn-spinner");

      if (loading) {
        $button.prop("disabled", true);
        $text.hide();
        $spinner.show();

        // Add a safety timeout to prevent permanent freezing
        setTimeout(() => {
          if ($button.prop("disabled") && $spinner.is(":visible")) {
            this.setSubmitButtonLoading($button, false);
            this.showError("Request is taking too long. Please try again.");
          }
        }, 35000); // 35 seconds safety timeout
      } else {
        $button.prop("disabled", false);
        $text.show();
        $spinner.hide();
      }
    }

    showError(message) {
      // STANDARDIZED ERROR HANDLING: Use professional notification instead of alert
      this.showNotification(message, "error");
    }

    /**
     * Show professional notification to user
     */
    showNotification(message, type = "info") {
      // Remove any existing notifications
      $(".stablecoinpay-notification").remove();

      // Create notification element
      const $notification = $(`
                <div class="stablecoinpay-notification stablecoinpay-notification-${type}">
                    <div class="stablecoinpay-notification-content">
                        <span class="stablecoinpay-notification-message">${message}</span>
                        <button class="stablecoinpay-notification-close" aria-label="Close">&times;</button>
                    </div>
                </div>
            `);

      // Add to page
      $("body").append($notification);

      // Auto-remove after 5 seconds
      setTimeout(() => {
        $notification.fadeOut(300, function () {
          $(this).remove();
        });
      }, 5000);

      // Manual close
      $notification
        .find(".stablecoinpay-notification-close")
        .on("click", function () {
          $notification.fadeOut(300, function () {
            $(this).remove();
          });
        });
    }

    showFieldError(fieldId, message) {
      const $error = $("#" + fieldId);
      $error.text(message).show();
    }

    clearFormErrors() {
      $(".field-error").hide().text("");
    }

    isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    }

    /**
     * UX IMPROVEMENT: Pre-fill email from localStorage
     */
    prefillSavedEmail($modal) {
      try {
        const savedEmail = localStorage.getItem("stablecoinpay_user_email");
        const $emailInput = $modal.find("#customer_email");
        const $clearButton = $modal.find(".clear-saved-email");

        if (savedEmail && this.isValidEmail(savedEmail) && $emailInput.length) {
          $emailInput.val(savedEmail);

          // Show clear button when email is pre-filled
          $clearButton.show();

          // Also focus on name field if email is pre-filled and name field exists
          const $nameInput = $modal.find("#customer_name");
          if ($nameInput.length && $nameInput.is(":visible")) {
            // Small delay to ensure modal is fully shown
            setTimeout(() => {
              $nameInput.focus();
            }, 150);
          }
        } else {
          // Hide clear button if no saved email
          $clearButton.hide();
        }
      } catch (e) {
        // localStorage might not be available (private browsing, etc.)
        // Fail silently and continue without pre-filling
      }
    }

    /**
     * UX IMPROVEMENT: Save email to localStorage for future use
     */
    saveEmailToStorage(email) {
      try {
        if (email && this.isValidEmail(email)) {
          localStorage.setItem("stablecoinpay_user_email", email);
        }
      } catch (e) {
        // localStorage might not be available (private browsing, etc.)
        // Fail silently and continue without saving
      }
    }

    /**
     * UX IMPROVEMENT: Clear saved email from localStorage
     */
    clearSavedEmail() {
      try {
        localStorage.removeItem("stablecoinpay_user_email");
      } catch (e) {
        // localStorage might not be available
        // Fail silently
      }
    }

    /**
     * UX IMPROVEMENT: Handle clear saved email button click
     */
    handleClearSavedEmail() {
      const $modal = $("#stablecoinpay-email-modal");
      const $emailInput = $modal.find("#customer_email");
      const $clearButton = $modal.find(".clear-saved-email");

      // Clear the input field
      $emailInput.val("").focus();

      // Hide the clear button
      $clearButton.hide();

      // Remove from localStorage
      this.clearSavedEmail();
    }

    /**
     * BLOCK-BASED CART FIX: Initialize support for WooCommerce blocks
     */
    initBlockSupport() {
      // Watch for block updates and re-bind modal events
      if (typeof window.wp !== "undefined" && window.wp.data) {
        // Subscribe to block editor changes
        const unsubscribe = window.wp.data.subscribe(() => {
          // Re-initialize modal events when blocks update
          this.reinitializeModalEvents();
        });

        // Store unsubscribe function for cleanup
        this.blockUnsubscribe = unsubscribe;
      }

      // Also watch for DOM mutations (fallback for non-block contexts)
      this.initMutationObserver();
    }

    /**
     * BLOCK-BASED CART FIX: Re-initialize modal events for dynamically added content
     */
    reinitializeModalEvents() {
      const $modal = $("#stablecoinpay-email-modal");
      if ($modal.length && !$modal.data("events-bound")) {
        // Mark as bound to prevent duplicate binding
        $modal.data("events-bound", true);

        // Re-isolate modal
        this.isolateEmailModal($modal);
      }
    }

    /**
     * BLOCK-BASED CART FIX: Watch for DOM changes and re-initialize as needed
     */
    initMutationObserver() {
      if (typeof MutationObserver !== "undefined") {
        const observer = new MutationObserver(mutations => {
          let shouldReinit = false;

          mutations.forEach(mutation => {
            // Check if modal was added to DOM
            mutation.addedNodes.forEach(node => {
              if (node.nodeType === 1) {
                // Element node
                if (
                  node.id === "stablecoinpay-email-modal" ||
                  $(node).find("#stablecoinpay-email-modal").length
                ) {
                  shouldReinit = true;
                }
              }
            });
          });

          if (shouldReinit) {
            // Delay to ensure DOM is fully updated
            setTimeout(() => {
              this.reinitializeModalEvents();
            }, 100);
          }
        });

        // Start observing
        observer.observe(document.body, {
          childList: true,
          subtree: true,
        });

        // Store observer for cleanup
        this.mutationObserver = observer;
      }
    }
  }

  // Initialize when DOM is ready
  $(document).ready(function () {
    new StablecoinPayQuickBuy();
  });
})(jQuery);
