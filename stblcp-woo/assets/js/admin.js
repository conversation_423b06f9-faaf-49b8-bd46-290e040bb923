/**
 * StablecoinPay Admin JavaScript
 *
 * Handles admin interface interactions
 */

jQuery(document).ready(function ($) {
  // Test API Connection
  $("#test-api-connection").on("click", function (e) {
    e.preventDefault();

    const $button = $(this);
    const originalText = $button.text();

    // Disable button and show loading
    $button
      .prop("disabled", true)
      .html(
        '<span class="stablecoinpay-spinner"></span>' +
          stablecoinpay_admin.strings.testing_connection
      );

    // Make AJAX request
    $.ajax({
      url: stablecoinpay_admin.ajax_url,
      type: "POST",
      data: {
        action: "stablecoinpay_test_connection",
        nonce: stablecoinpay_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          showNotice(stablecoinpay_admin.strings.connection_success, "success");
          updateConnectionStatus("success", response.data.message);
        } else {
          showNotice(
            stablecoinpay_admin.strings.connection_failed + " " + response.data,
            "error"
          );
          updateConnectionStatus("error", response.data);
        }
      },
      error: function (xhr, status, error) {
        showNotice(
          stablecoinpay_admin.strings.connection_failed + " " + error,
          "error"
        );
        updateConnectionStatus("error", error);
      },
      complete: function () {
        // Re-enable button
        $button.prop("disabled", false).text(originalText);
      },
    });
  });

  // Handle Cloudflare IP ranges display
  $("#show-cloudflare-ips").on("click", function (e) {
    e.preventDefault();
    const $button = $(this);
    const $section = $("#cloudflare-ips-section");
    const $content = $("#cloudflare-ips-content");

    if ($section.is(":visible")) {
      $section.slideUp();
      $button.find(".dashicons").removeClass("dashicons-arrow-up").addClass("dashicons-cloud");
      return;
    }

    // Show section and load IPs
    $section.slideDown();
    $button.find(".dashicons").removeClass("dashicons-cloud").addClass("dashicons-arrow-up");

    // Load Cloudflare IPs
    $content.html(
      '<p><span class="dashicons dashicons-update stablecoinpay-spin"></span> ' +
        (stablecoinpay_admin.strings.loading_cloudflare_ips || "Loading Cloudflare IP ranges...") +
        "</p>"
    );

    $.ajax({
      url: stablecoinpay_admin.ajax_url,
      type: "POST",
      data: {
        action: "stablecoinpay_get_cloudflare_ips",
        nonce: stablecoinpay_admin.cloudflare_nonce || stablecoinpay_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          const data = response.data;
          let html = '<div class="cloudflare-ip-info">';

          if (data.is_cloudflare_detected) {
            html +=
              '<div class="notice notice-info inline"><p><strong>✅ Cloudflare Detected!</strong> Your site appears to be using Cloudflare.</p></div>';
          } else {
            html +=
              '<div class="notice notice-warning inline"><p><strong>⚠️ Cloudflare Not Detected</strong> - These IPs are still useful if you plan to use Cloudflare.</p></div>';
          }

          html += "<p><strong>Add these " + data.count + " IP ranges to your StablecoinPay API key whitelist:</strong></p>";
          html +=
            '<textarea readonly style="width: 100%; height: 200px; font-family: monospace; font-size: 12px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">';
          html += data.formatted_ips;
          html += "</textarea>";
          html +=
            "<p><em>💡 Copy the above IP ranges and paste them into your StablecoinPay backend API key IP whitelist settings.</em></p>";
          html +=
            '<button type="button" class="button stablecoinpay-copy-cloudflare-ips">Copy to Clipboard</button>';
          html += "</div>";

          $content.html(html);

          // Add copy functionality
          $content.find(".stablecoinpay-copy-cloudflare-ips").on("click", function () {
            const $btn = $(this);
            const textToCopy = $content.find("textarea").val();
            const originalText = $btn.text();

            copyToClipboard(textToCopy)
              .then(function () {
                $btn.text("Copied!").addClass("copied");
                setTimeout(function () {
                  $btn.text(originalText).removeClass("copied");
                }, 2000);
              })
              .catch(function () {
                showNotice("Failed to copy to clipboard", "error");
              });
          });
        } else {
          $content.html(
            '<div class="notice notice-error inline"><p>' +
              (response.data || stablecoinpay_admin.strings.cloudflare_ips_error || "Failed to load Cloudflare IP ranges.") +
              "</p></div>"
          );
        }
      },
      error: function () {
        $content.html(
          '<div class="notice notice-error inline"><p>' +
            (stablecoinpay_admin.strings.cloudflare_ips_error || "Failed to load Cloudflare IP ranges.") +
            "</p></div>"
        );
      },
    });
  });

  // Visual feedback for form changes (without annoying notifications)
  let hasUnsavedChanges = false;
  $(
    ".stablecoinpay-form-table input, .stablecoinpay-form-table select, .stablecoinpay-form-table textarea, .form-table input, .form-table select, .form-table textarea"
  ).on("change input", function () {
    if (!hasUnsavedChanges) {
      hasUnsavedChanges = true;
      // Add subtle visual indicator to save button
      const $saveButton = $('.button-primary[type="submit"]');
      if ($saveButton.length) {
        $saveButton.addClass("stablecoinpay-has-changes");
      }
    }
  });

  // Reset unsaved changes flag when form is submitted
  $("form").on("submit", function () {
    hasUnsavedChanges = false;
    $('.button-primary[type="submit"]').removeClass(
      "stablecoinpay-has-changes"
    );
  });

  // Copy to clipboard functionality
  $(".stablecoinpay-copy-btn").on("click", function (e) {
    e.preventDefault();

    const textToCopy = $(this).data("copy");
    const $button = $(this);
    const originalText = $button.text();

    copyToClipboard(textToCopy)
      .then(function () {
        $button.text("Copied!").addClass("copied");
        setTimeout(function () {
          $button.text(originalText).removeClass("copied");
        }, 2000);
      })
      .catch(function () {
        showNotice("Failed to copy to clipboard", "error");
      });
  });

  // Validate API URL format
  $('input[name="stablecoinpay_settings[api_url]"]').on("blur", function () {
    const url = $(this).val().trim();
    if (url && !isValidUrl(url)) {
      showNotice("Please enter a valid API URL", "warning");
      $(this).focus();
    }
  });

  // Validate payment expiry range
  $('input[name="stablecoinpay_settings[payment_expiry]"]').on(
    "change",
    function () {
      const value = parseInt($(this).val());
      if (value < 1 || value > 1440) {
        showNotice(
          "Payment expiry must be between 1 and 1440 minutes",
          "warning"
        );
        $(this).val(30);
      }
    }
  );

  // Show/hide advanced settings
  $(".stablecoinpay-toggle-advanced").on("click", function (e) {
    e.preventDefault();
    $(".stablecoinpay-advanced-settings").slideToggle();
    $(this).text(
      $(this).text() === "Show Advanced" ? "Hide Advanced" : "Show Advanced"
    );
  });

  // Initialize simple tooltips (without jQuery UI dependency)
  $(".stablecoinpay-tooltip").each(function () {
    const $element = $(this);
    const tooltipText = $element.attr("title") || $element.data("tooltip");

    if (tooltipText) {
      // Remove the title attribute to prevent default browser tooltip
      $element.removeAttr("title");

      // Add hover events for custom tooltip
      $element
        .on("mouseenter", function () {
          const $tooltip = $(
            '<div class="stablecoinpay-custom-tooltip">' +
              tooltipText +
              "</div>"
          );
          $("body").append($tooltip);

          const elementOffset = $element.offset();
          const elementWidth = $element.outerWidth();

          $tooltip.css({
            position: "absolute",
            top: elementOffset.top - $tooltip.outerHeight() - 5,
            left: elementOffset.left + elementWidth + 10,
            zIndex: 9999,
          });

          $tooltip.fadeIn(200);
        })
        .on("mouseleave", function () {
          $(".stablecoinpay-custom-tooltip").fadeOut(200, function () {
            $(this).remove();
          });
        });
    }
  });

  // Functions
  function showNotice(message, type, duration) {
    type = type || "info";
    duration = duration || 5000;

    const noticeClass =
      type === "success"
        ? "notice-success"
        : type === "error"
        ? "notice-error"
        : type === "warning"
        ? "notice-warning"
        : "notice-info";

    const $notice = $(
      '<div class="notice ' +
        noticeClass +
        ' is-dismissible stablecoinpay-notice">' +
        "<p>" +
        message +
        "</p>" +
        '<button type="button" class="notice-dismiss">' +
        '<span class="screen-reader-text">Dismiss this notice.</span>' +
        "</button>" +
        "</div>"
    );

    // Remove existing notices
    $(".stablecoinpay-notice").remove();

    // Add new notice
    $(".wrap h1").after($notice);

    // Auto-dismiss after duration
    if (duration > 0) {
      setTimeout(function () {
        $notice.fadeOut(function () {
          $(this).remove();
        });
      }, duration);
    }

    // Handle dismiss button
    $notice.find(".notice-dismiss").on("click", function () {
      $notice.fadeOut(function () {
        $(this).remove();
      });
    });
  }

  function updateConnectionStatus(status, message) {
    const $statusDiv = $("#stablecoinpay-connection-status");

    if ($statusDiv.length === 0) {
      return;
    }

    const statusClass =
      status === "success"
        ? "notice-success"
        : status === "error"
        ? "notice-error"
        : "notice-info";

    $statusDiv
      .removeClass("notice-success notice-error notice-info notice-warning")
      .addClass(statusClass)
      .find("p")
      .text(message);
  }

  function copyToClipboard(text) {
    return new Promise(function (resolve, reject) {
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(resolve).catch(reject);
      } else {
        // Fallback for older browsers
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
          document.execCommand("copy");
          textArea.remove();
          resolve();
        } catch (error) {
          textArea.remove();
          reject(error);
        }
      }
    });
  }

  function isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }

  // Dashboard widget functionality
  if ($(".stablecoinpay-dashboard-widget").length > 0) {
    loadDashboardStats();
  }

  function loadDashboardStats() {
    $.ajax({
      url: stablecoinpay_admin.ajax_url,
      type: "POST",
      data: {
        action: "stablecoinpay_get_dashboard_stats",
        nonce: stablecoinpay_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          updateDashboardStats(response.data);
        }
      },
    });
  }

  function updateDashboardStats(stats) {
    $('.stablecoinpay-stat[data-stat="total"] .stablecoinpay-stat-value').text(
      stats.total || 0
    );
    $(
      '.stablecoinpay-stat[data-stat="confirmed"] .stablecoinpay-stat-value'
    ).text(stats.confirmed || 0);
    $('.stablecoinpay-stat[data-stat="amount"] .stablecoinpay-stat-value').text(
      "$" + (stats.total_amount || 0)
    );
    $('.stablecoinpay-stat[data-stat="rate"] .stablecoinpay-stat-value').text(
      (stats.success_rate || 0) + "%"
    );
  }

  // Order actions
  $(".wc-order-action select").on("change", function () {
    const action = $(this).val();
    if (action === "stablecoinpay_check_payment") {
      const $button = $(".wc-order-action .button");
      $button.text("Check Payment Status");
    }
  });

  // Real-time order status updates
  if ($(".stablecoinpay-order-meta").length > 0) {
    const orderId = $(".stablecoinpay-order-meta").data("order-id");
    if (orderId) {
      setInterval(function () {
        checkOrderPaymentStatus(orderId);
      }, 30000); // Check every 30 seconds
    }
  }

  function checkOrderPaymentStatus(orderId) {
    $.ajax({
      url: stablecoinpay_admin.ajax_url,
      type: "POST",
      data: {
        action: "stablecoinpay_check_order_status",
        order_id: orderId,
        nonce: stablecoinpay_admin.nonce,
      },
      success: function (response) {
        if (response.success && response.data.updated) {
          // Refresh the page to show updated status
          location.reload();
        }
      },
    });
  }

  // Export functionality
  $(".stablecoinpay-export-btn").on("click", function (e) {
    e.preventDefault();

    const days = $(this).data("days") || 30;
    const url =
      stablecoinpay_admin.ajax_url +
      "?action=stablecoinpay_export_data&days=" +
      days +
      "&nonce=" +
      stablecoinpay_admin.nonce;

    // Create temporary link and trigger download
    const link = document.createElement("a");
    link.href = url;
    link.download =
      "stablecoinpay-export-" + new Date().toISOString().split("T")[0] + ".csv";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  });

  // Form validation
  $("form").on("submit", function (e) {
    const $form = $(this);

    // Validate required fields
    let hasErrors = false;
    $form.find("input[required], select[required]").each(function () {
      if (!$(this).val().trim()) {
        $(this).addClass("error");
        hasErrors = true;
      } else {
        $(this).removeClass("error");
      }
    });

    if (hasErrors) {
      e.preventDefault();
      showNotice("Please fill in all required fields", "error");
      return false;
    }
  });

  // Auto-refresh payment status indicators
  setInterval(function () {
    $('.stablecoinpay-payment-status[data-auto-refresh="true"]').each(
      function () {
        const $status = $(this);
        const paymentId = $status.data("payment-id");

        if (paymentId) {
          updatePaymentStatus($status, paymentId);
        }
      }
    );
  }, 60000); // Check every minute

  function updatePaymentStatus($element, paymentId) {
    $.ajax({
      url: stablecoinpay_admin.ajax_url,
      type: "POST",
      data: {
        action: "stablecoinpay_get_payment_status_admin",
        payment_id: paymentId,
        nonce: stablecoinpay_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          const status = response.data.status.toLowerCase();
          $element
            .removeClass("waiting confirmed expired canceled")
            .addClass(status)
            .text(status.toUpperCase());
        }
      },
    });
  }
});
