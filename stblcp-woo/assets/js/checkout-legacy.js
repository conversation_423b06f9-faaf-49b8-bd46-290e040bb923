/**
 * StablecoinPay Legacy Checkout Dropdown Handler - Simplified Version
 * Maximum compatibility for production environments
 * 
 * @package StablecoinPay
 * @since 2.0.0
 */

// Simple function-based approach for maximum compatibility
function initStablecoinPayDropdown() {
    const dropdown = document.getElementById('stablecoinpay-payment-method-dropdown');
    
    if (!dropdown) {
        setTimeout(initStablecoinPayDropdown, 2000);
        return;
    }

    const dropdownButton = dropdown.querySelector('.dropdown-button');
    const dropdownMenu = dropdown.querySelector('.dropdown-menu');
    const hiddenSelect = document.getElementById('stablecoinpay_payment_method');

    if (!dropdownButton || !dropdownMenu || !hiddenSelect) {
        return;
    }

    // Toggle dropdown function
    function toggleDropdown(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const isOpen = dropdown.getAttribute('aria-expanded') === 'true';

        if (isOpen) {
            closeDropdown();
        } else {
            openDropdown();
        }
    }

    // Open dropdown function
    function openDropdown() {
        dropdown.setAttribute('aria-expanded', 'true');
        dropdownButton.classList.add('active');
        dropdownMenu.style.display = 'block';
        
        // Force reflow then add active class for animation
        dropdownMenu.offsetHeight;
        dropdownMenu.classList.add('active');

        // Focus first option
        const firstOption = dropdownMenu.querySelector('.dropdown-option');
        if (firstOption) {
            firstOption.focus();
        }
    }

    // Close dropdown function
    function closeDropdown() {
        dropdown.setAttribute('aria-expanded', 'false');
        dropdownButton.classList.remove('active');
        dropdownMenu.classList.remove('active');

        // Hide after animation completes
        setTimeout(function() {
            if (!dropdownMenu.classList.contains('active')) {
                dropdownMenu.style.display = 'none';
            }
        }, 300);
    }

    // Select option function
    function selectOption(option) {
        const value = option.getAttribute('data-value');
        const token = option.getAttribute('data-token');
        const blockchain = option.getAttribute('data-blockchain');
        const label = option.getAttribute('data-label') || token;
        const iconSrc = option.querySelector('.dropdown-option-icon')?.src;

        const selectedIcon = dropdown.querySelector('.dropdown-selected-icon');
        const selectedToken = dropdown.querySelector('.dropdown-selected-token');
        const selectedNetwork = dropdown.querySelector('.dropdown-selected-network');

        // Update visual selection
        dropdown.querySelectorAll('.dropdown-option').forEach(function(opt) {
            opt.classList.remove('selected');
        });
        option.classList.add('selected');

        // Update displayed selection
        if (iconSrc && selectedIcon) {
            selectedIcon.src = iconSrc;
            selectedIcon.alt = token;
            selectedIcon.style.display = 'block';
        }
        if (selectedToken) {
            selectedToken.textContent = label;
        }
        if (selectedNetwork) {
            selectedNetwork.textContent = blockchain + ' Network';
            selectedNetwork.style.display = 'block';
        }

        // Update hidden select
        hiddenSelect.value = value;

        // Trigger change event for WooCommerce
        const changeEvent = new Event('change', { bubbles: true });
        hiddenSelect.dispatchEvent(changeEvent);

        closeDropdown();
    }

    // Add click event to dropdown button
    dropdownButton.addEventListener('click', toggleDropdown);

    // Add click events to options
    const options = dropdownMenu.querySelectorAll('.dropdown-option');
    
    options.forEach(function(option) {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            selectOption(option);
        });
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!dropdown.contains(e.target)) {
            closeDropdown();
        }
    });

    // Keyboard navigation
    dropdown.addEventListener('keydown', function(e) {
        const isOpen = dropdown.getAttribute('aria-expanded') === 'true';
        const options = dropdown.querySelectorAll('.dropdown-option');

        switch (e.key) {
            case 'Enter':
            case ' ':
                e.preventDefault();
                if (!isOpen) {
                    openDropdown();
                } else {
                    const focused = document.activeElement;
                    if (focused && focused.classList.contains('dropdown-option')) {
                        selectOption(focused);
                    }
                }
                break;

            case 'Escape':
                e.preventDefault();
                closeDropdown();
                dropdownButton.focus();
                break;

            case 'ArrowDown':
                e.preventDefault();
                if (!isOpen) {
                    openDropdown();
                } else {
                    const focused = document.activeElement;
                    const currentIndex = Array.from(options).indexOf(focused);
                    const nextIndex = currentIndex < options.length - 1 ? currentIndex + 1 : 0;
                    options[nextIndex].focus();
                }
                break;

            case 'ArrowUp':
                e.preventDefault();
                if (isOpen) {
                    const focused = document.activeElement;
                    const currentIndex = Array.from(options).indexOf(focused);
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
                    options[prevIndex].focus();
                }
                break;
        }
    });
}

// Initialize when DOM is ready - Multiple approaches for maximum compatibility
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initStablecoinPayDropdown);
} else {
    initStablecoinPayDropdown();
}

// Backup initialization with jQuery if available
if (typeof jQuery !== 'undefined') {
    jQuery(document).ready(function() {
        // Small delay to ensure WooCommerce has finished loading
        setTimeout(initStablecoinPayDropdown, 500);
    });
}

// Additional fallback - try initialization after a delay
setTimeout(function() {
    if (document.getElementById('stablecoinpay-payment-method-dropdown')) {
        initStablecoinPayDropdown();
    }
}, 1000);
