/**
 * StablecoinPay Unified Checkout Block
 * Modern WooCommerce Blocks implementation with legacy accessibility features
 * 
 * @package StablecoinPay
 * @since 2.0.0
 */

/**
 * External dependencies
 */
const { registerPaymentMethod } = window.wc.wcBlocksRegistry;
const { createElement, useState, useEffect } = window.wp.element;
const { __ } = window.wp.i18n;
const { decodeEntities } = window.wp.htmlEntities;
const { getSetting } = window.wc.wcSettings;

/**
 * Internal dependencies
 */
const settings = getSetting("stablecoinpay_data", {});
const defaultLabel = __("StablecoinPay", "stablecoinpay-gateway");
const label = decodeEntities(settings.title) || defaultLabel;

/**
 * Custom Dropdown Component with Accessibility
 */
const StablecoinPayDropdown = ({ 
  options, 
  selectedValue, 
  onSelectionChange, 
  placeholder = __("Select payment method...", "stablecoinpay-gateway") 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);

  // Get icon URL helper
  const getIconUrl = (token) => {
    if (!token) return "";
    const pluginUrl = settings.pluginUrl || "/wp-content/plugins/stblcp-woo/";
    const baseUrl = pluginUrl.endsWith('/') ? pluginUrl : pluginUrl + '/';
    return `${baseUrl}assets/icons/${token.toLowerCase()}.svg`;
  };

  // Get selected option data
  const selectedOption = options.find(option => option.value === selectedValue);

  // Handle keyboard navigation
  const handleKeyDown = (event) => {
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
          setFocusedIndex(0);
        } else if (focusedIndex >= 0) {
          onSelectionChange(options[focusedIndex].value);
          setIsOpen(false);
          setFocusedIndex(-1);
        }
        break;
      
      case 'Escape':
        event.preventDefault();
        setIsOpen(false);
        setFocusedIndex(-1);
        break;
      
      case 'ArrowDown':
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
          setFocusedIndex(0);
        } else {
          setFocusedIndex(prev => 
            prev < options.length - 1 ? prev + 1 : 0
          );
        }
        break;
      
      case 'ArrowUp':
        event.preventDefault();
        if (isOpen) {
          setFocusedIndex(prev => 
            prev > 0 ? prev - 1 : options.length - 1
          );
        }
        break;
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isOpen && !event.target.closest('.stablecoinpay-unified-dropdown')) {
        setIsOpen(false);
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isOpen]);

  const dropdownStyles = {
    container: {
      position: 'relative',
      width: '100%',
      fontFamily: 'inherit',
    },
    button: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '12px 16px',
      border: '2px solid #d1d5db',
      borderRadius: '12px',
      background: '#ffffff',
      fontSize: '15px',
      fontWeight: '500',
      cursor: 'pointer',
      width: '100%',
      textAlign: 'left',
      minHeight: '52px',
      transition: 'all 0.3s ease',
      outline: 'none',
      // Enhanced Blocksy theme compatibility
      boxSizing: 'border-box',
      appearance: 'none',
      WebkitAppearance: 'none',
      MozAppearance: 'none',
    },
    buttonHover: {
      borderColor: '#2563eb',
      boxShadow: '0 4px 12px rgba(37, 99, 235, 0.15)',
    },
    buttonFocus: {
      borderColor: '#2563eb',
      boxShadow: '0 0 0 4px rgba(37, 99, 235, 0.1), 0 4px 12px rgba(37, 99, 235, 0.15)',
    },
    selectedContent: {
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
      flex: '1',
    },
    selectedIcon: {
      width: '24px',
      height: '24px',
      borderRadius: '50%',
      flexShrink: '0',
    },
    selectedText: {
      display: 'flex',
      flexDirection: 'column',
      gap: '2px',
      flex: '1',
    },
    selectedToken: {
      fontWeight: '600',
      fontSize: '14px',
      color: '#1f2937',
      lineHeight: '1.2',
    },
    selectedNetwork: {
      fontSize: '12px',
      color: '#6b7280',
      fontWeight: '400',
      lineHeight: '1.2',
    },
    arrow: {
      width: '20px',
      height: '20px',
      transition: 'transform 0.3s ease',
      color: '#6b7280',
      transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
    },
    menu: {
      position: 'absolute',
      top: '100%',
      left: '0',
      right: '0',
      background: '#ffffff',
      border: '2px solid #d1d5db',
      borderRadius: '12px',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
      zIndex: '99999',
      maxHeight: '300px',
      overflowY: 'auto',
      marginTop: '4px',
      // Enhanced theme compatibility
      boxSizing: 'border-box',
      minWidth: '100%',
    },
    groupLabel: {
      padding: '8px 16px 4px',
      fontSize: '11px',
      fontWeight: '600',
      color: '#6b7280',
      textTransform: 'uppercase',
      letterSpacing: '0.5px',
      background: '#f9fafb',
      borderBottom: '1px solid #f3f4f6',
    },
    option: {
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
      padding: '12px 16px',
      cursor: 'pointer',
      border: 'none',
      background: 'none',
      width: '100%',
      textAlign: 'left',
      fontFamily: 'inherit',
      fontSize: 'inherit',
      transition: 'background-color 0.2s ease',
      outline: 'none',
    },
    optionHover: {
      background: 'rgba(37, 99, 235, 0.08)',
    },
    optionSelected: {
      background: 'rgba(37, 99, 235, 0.12)',
      color: '#2563eb',
    },
    optionFocused: {
      background: 'rgba(37, 99, 235, 0.08)',
    },
    optionIcon: {
      width: '24px',
      height: '24px',
      borderRadius: '50%',
      flexShrink: '0',
    },
    optionText: {
      display: 'flex',
      flexDirection: 'column',
      gap: '2px',
      flex: '1',
    },
    optionToken: {
      fontWeight: '600',
      fontSize: '14px',
      color: 'inherit',
      lineHeight: '1.2',
    },
    optionNetwork: {
      fontSize: '12px',
      color: '#6b7280',
      fontWeight: '400',
      lineHeight: '1.2',
    },
  };

  return createElement(
    'div',
    {
      className: 'stablecoinpay-unified-dropdown',
      style: dropdownStyles.container,
    },
    [
      // Dropdown Button
      createElement(
        'button',
        {
          key: 'dropdown-button',
          type: 'button',
          onClick: () => {
            setIsOpen(!isOpen);
            if (!isOpen) setFocusedIndex(0);
          },
          onKeyDown: handleKeyDown,
          style: {
            ...dropdownStyles.button,
            ...(isOpen ? dropdownStyles.buttonFocus : {}),
          },
          'aria-haspopup': 'listbox',
          'aria-expanded': isOpen,
          'aria-label': selectedOption 
            ? `${selectedOption.label} on ${selectedOption.blockchain} Network` 
            : placeholder,
        },
        [
          createElement(
            'div',
            {
              key: 'selected-content',
              style: dropdownStyles.selectedContent,
            },
            [
              selectedOption ? createElement(
                'img',
                {
                  key: 'selected-icon',
                  src: getIconUrl(selectedOption.token),
                  alt: selectedOption.token,
                  style: dropdownStyles.selectedIcon,
                }
              ) : null,
              createElement(
                'div',
                {
                  key: 'selected-text',
                  style: dropdownStyles.selectedText,
                },
                [
                  createElement(
                    'span',
                    {
                      key: 'token-label',
                      style: dropdownStyles.selectedToken,
                    },
                    selectedOption ? selectedOption.label : placeholder
                  ),
                  selectedOption ? createElement(
                    'span',
                    {
                      key: 'network-label',
                      style: dropdownStyles.selectedNetwork,
                    },
                    selectedOption.blockchain + ' Network'
                  ) : null,
                ]
              ),
            ]
          ),
          createElement(
            'svg',
            {
              key: 'dropdown-arrow',
              fill: 'none',
              stroke: 'currentColor',
              viewBox: '0 0 24 24',
              style: dropdownStyles.arrow,
              'aria-hidden': 'true',
            },
            createElement('path', {
              strokeLinecap: 'round',
              strokeLinejoin: 'round',
              strokeWidth: '2',
              d: 'M19 9l-7 7-7-7',
            })
          ),
        ]
      ),

      // Dropdown Menu
      isOpen ? createElement(
        'div',
        {
          key: 'dropdown-menu',
          style: dropdownStyles.menu,
          role: 'listbox',
          'aria-label': __('Payment method options', 'stablecoinpay-gateway'),
        },
        [
          // Stablecoins Group
          ...(() => {
            const stablecoins = options.filter(opt => 
              ['USDT', 'USDC', 'DAI', 'PYUSD', 'BUSD'].includes(opt.token)
            );
            
            if (stablecoins.length === 0) return [];
            
            return [
              createElement(
                'div',
                {
                  key: 'stablecoins-label',
                  style: dropdownStyles.groupLabel,
                },
                __('Stablecoins', 'stablecoinpay-gateway')
              ),
              ...stablecoins.map((option, index) => {
                const globalIndex = options.findIndex(opt => opt.value === option.value);
                const isSelected = selectedValue === option.value;
                const isFocused = focusedIndex === globalIndex;
                
                return createElement(
                  'button',
                  {
                    key: option.value,
                    type: 'button',
                    onClick: () => {
                      onSelectionChange(option.value);
                      setIsOpen(false);
                      setFocusedIndex(-1);
                    },
                    onKeyDown: handleKeyDown,
                    style: {
                      ...dropdownStyles.option,
                      ...(isFocused ? dropdownStyles.optionFocused : {}),
                      ...(isSelected ? dropdownStyles.optionSelected : {}),
                    },
                    role: 'option',
                    'aria-selected': isSelected,
                    tabIndex: isFocused ? 0 : -1,
                  },
                  [
                    createElement(
                      'img',
                      {
                        key: 'option-icon',
                        src: getIconUrl(option.token),
                        alt: option.token,
                        style: dropdownStyles.optionIcon,
                      }
                    ),
                    createElement(
                      'div',
                      {
                        key: 'option-text',
                        style: dropdownStyles.optionText,
                      },
                      [
                        createElement(
                          'span',
                          {
                            key: 'option-token',
                            style: dropdownStyles.optionToken,
                          },
                          option.label
                        ),
                        createElement(
                          'span',
                          {
                            key: 'option-network',
                            style: dropdownStyles.optionNetwork,
                          },
                          option.blockchain + ' Network'
                        ),
                      ]
                    ),
                  ]
                );
              }),
            ];
          })(),

          // Native Coins Group  
          ...(() => {
            const nativeCoins = options.filter(opt => 
              !['USDT', 'USDC', 'DAI', 'PYUSD', 'BUSD'].includes(opt.token)
            );
            
            if (nativeCoins.length === 0) return [];
            
            const hasStablecoins = options.some(opt => 
              ['USDT', 'USDC', 'DAI', 'PYUSD', 'BUSD'].includes(opt.token)
            );
            
            return [
              createElement(
                'div',
                {
                  key: 'native-label',
                  style: {
                    ...dropdownStyles.groupLabel,
                    ...(hasStablecoins ? { borderTop: '1px solid #f3f4f6' } : {}),
                  },
                },
                __('Native Coins', 'stablecoinpay-gateway')
              ),
              ...nativeCoins.map((option, index) => {
                const globalIndex = options.findIndex(opt => opt.value === option.value);
                const isSelected = selectedValue === option.value;
                const isFocused = focusedIndex === globalIndex;
                
                return createElement(
                  'button',
                  {
                    key: option.value,
                    type: 'button',
                    onClick: () => {
                      onSelectionChange(option.value);
                      setIsOpen(false);
                      setFocusedIndex(-1);
                    },
                    onKeyDown: handleKeyDown,
                    style: {
                      ...dropdownStyles.option,
                      ...(isFocused ? dropdownStyles.optionFocused : {}),
                      ...(isSelected ? dropdownStyles.optionSelected : {}),
                    },
                    role: 'option',
                    'aria-selected': isSelected,
                    tabIndex: isFocused ? 0 : -1,
                  },
                  [
                    createElement(
                      'img',
                      {
                        key: 'option-icon',
                        src: getIconUrl(option.token),
                        alt: option.token,
                        style: dropdownStyles.optionIcon,
                      }
                    ),
                    createElement(
                      'div',
                      {
                        key: 'option-text',
                        style: dropdownStyles.optionText,
                      },
                      [
                        createElement(
                          'span',
                          {
                            key: 'option-token',
                            style: dropdownStyles.optionToken,
                          },
                          option.label
                        ),
                        createElement(
                          'span',
                          {
                            key: 'option-network',
                            style: dropdownStyles.optionNetwork,
                          },
                          option.blockchain + ' Network'
                        ),
                      ]
                    ),
                  ]
                );
              }),
            ];
          })(),
        ]
      ) : null,
    ]
  );
};

/**
 * Content component
 */
const Content = (props) => {
  const [selectedMethod, setSelectedMethod] = useState("");

  // Get checkout data handling from props
  const { eventRegistration, emitResponse } = props;
  const { onPaymentSetup } = eventRegistration;

  // Register payment setup handler
  useEffect(() => {
    const unsubscribe = onPaymentSetup(async () => {
      // Validate selection
      if (!selectedMethod) {
        return {
          type: emitResponse.responseTypes.ERROR,
          message: __("Please select a payment method.", "stablecoinpay-gateway"),
        };
      }

      // Pass selected payment method to backend
      return {
        type: emitResponse.responseTypes.SUCCESS,
        meta: {
          paymentMethodData: {
            stablecoinpay_payment_method: selectedMethod,
          },
        },
      };
    });
    return unsubscribe;
  }, [onPaymentSetup, selectedMethod, emitResponse]);

  // Get enabled payment methods from settings
  const enabledMethods = settings.enabledMethods || {};
  
  // Convert to options array
  const options = Object.entries(enabledMethods).map(([key, method]) => ({
    value: key,
    label: method.label,
    token: method.token,
    blockchain: method.blockchain,
  }));

  // Set default selection if not set and options available
  useEffect(() => {
    if (!selectedMethod && options.length > 0) {
      // Try to find default from settings
      const defaultToken = settings.defaultToken || 'USDT';
      const defaultBlockchain = settings.defaultBlockchain || 'ETHEREUM';
      
      const defaultOption = options.find(opt => 
        opt.token === defaultToken && opt.blockchain === defaultBlockchain
      );
      
      if (defaultOption) {
        setSelectedMethod(defaultOption.value);
      } else {
        // Fallback to first option
        setSelectedMethod(options[0].value);
      }
    }
  }, [options, selectedMethod, settings]);

  return createElement(
    "div",
    {
      className: "wc-block-components-payment-method-content wc-block-components-payment-method-content--stablecoinpay",
      style: {
        padding: '0',
        margin: '0',
        fontFamily: 'inherit',
      },
    },
    [
      // Description
      createElement(
        "div",
        {
          key: "description",
          className: "stablecoinpay-description",
          style: {
            marginBottom: '16px',
            fontSize: '14px',
            color: '#6b7280',
            lineHeight: '1.5',
          },
        },
        decodeEntities(
          settings.description || __("Pay securely with cryptocurrency", "stablecoinpay-gateway")
        )
      ),

      // Payment method selector (only if multiple options)
      options.length > 1 ? createElement(
        "div",
        {
          key: "method-selector",
          className: "stablecoinpay-method-selector",
          style: { 
            marginBottom: '16px',
          },
        },
        [
          createElement(
            "label",
            {
              key: "label",
              style: {
                display: "block",
                marginBottom: "8px",
                fontWeight: "500",
                fontSize: "14px",
                color: "#374151",
              },
            },
            __("Choose a token/coin:", "stablecoinpay-gateway")
          ),

          createElement(StablecoinPayDropdown, {
            key: "dropdown",
            options: options,
            selectedValue: selectedMethod,
            onSelectionChange: setSelectedMethod,
            placeholder: __("Select payment method...", "stablecoinpay-gateway"),
          }),
        ]
      ) : null,

      // Test mode notice
      settings.testMode ? createElement(
        "div",
        {
          key: "test-mode",
          className: "stablecoinpay-test-mode-notice",
          style: {
            background: "linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)",
            border: "1px solid #f59e0b",
            borderRadius: "8px",
            padding: "12px 16px",
            marginTop: "16px",
            fontSize: "13px",
            color: "#92400e",
            display: "flex",
            alignItems: "center",
            gap: "8px",
          },
        },
        [
          createElement(
            "span",
            {
              key: "test-badge",
              style: {
                background: "#f59e0b",
                color: "#fff",
                padding: "2px 8px",
                borderRadius: "4px",
                fontSize: "11px",
                fontWeight: "700",
                letterSpacing: "0.5px",
              },
            },
            __("TEST MODE", "stablecoinpay-gateway")
          ),
          createElement(
            "span",
            { key: "test-text" },
            __("No real payments will be processed", "stablecoinpay-gateway")
          ),
        ]
      ) : null,
    ]
  );
};

/**
 * Label component
 */
const Label = () => {
  return createElement(
    "span",
    {
      style: {
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        width: "100%",
      },
    },
    [
      createElement(
        "span",
        { key: "label-text" },
        label
      ),
      createElement(
        "img",
        {
          key: "label-icon",
          src: settings.icon || "",
          alt: "StablecoinPay",
          style: {
            width: "300px",
            height: "50px",
            marginLeft: "8px",
            objectFit: "contain",
            borderRadius: "4px",
          },
        }
      ),
    ]
  );
};

/**
 * StablecoinPay payment method config object.
 */
const StablecoinPayPaymentMethod = {
  name: "stablecoinpay",
  label: createElement(Label),
  content: createElement(Content),
  edit: createElement(Content),
  canMakePayment: () => true,
  ariaLabel: label,
  supports: {
    features: settings.supports,
  },
};

registerPaymentMethod(StablecoinPayPaymentMethod);
