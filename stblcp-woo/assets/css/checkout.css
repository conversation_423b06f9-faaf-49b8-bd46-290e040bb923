/* StablecoinPay Unified Checkout Styles - Theme Compatible */

/* CSS Custom Properties for consistent theming */
:root {
  --stablecoinpay-primary: #2563eb;
  --stablecoinpay-primary-hover: #1d4ed8;
  --stablecoinpay-text-primary: #1f2937;
  --stablecoinpay-text-secondary: #6b7280;
  --stablecoinpay-border: #d1d5db;
  --stablecoinpay-border-focus: #2563eb;
  --stablecoinpay-background: #ffffff;
  --stablecoinpay-background-hover: rgba(37, 99, 235, 0.08);
  --stablecoinpay-background-selected: rgba(37, 99, 235, 0.12);
  --stablecoinpay-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
  --stablecoinpay-shadow-focus: 0 0 0 4px rgba(37, 99, 235, 0.1);
  --stablecoinpay-radius: 12px;
  --stablecoinpay-transition: all 0.3s ease;
}

/* Payment Method Container - High Specificity for Theme Compatibility */
.woocommerce .payment_method_stablecoinpay,
.wc-block-checkout .payment_method_stablecoinpay,
body .woocommerce-checkout .payment_method_stablecoinpay,
body.woocommerce-checkout .payment_method_stablecoinpay {
  position: relative !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Payment Method Icon - Blocksy Theme Compatibility */
.woocommerce .payment_method_stablecoinpay label img,
.wc-block-checkout .payment_method_stablecoinpay label img,
body .woocommerce-checkout .payment_method_stablecoinpay label img,
body.woocommerce-checkout .payment_method_stablecoinpay label img,
.wc-block-components-payment-method-label--with-icon img {
  width: 300px !important;
  margin-left: 8px !important;
  vertical-align: middle !important;
  display: inline-block !important;
  flex-shrink: 0 !important;
  border-radius: 4px !important;
  object-fit: contain !important;
  max-width: none !important;
  max-height: none !important;
}

/* Payment Method Label - Blocksy Theme Compatibility */
.wc-block-components-payment-method-label--with-icon,
body .wc-block-components-payment-method-label--with-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  width: 100% !important;
  flex-wrap: nowrap !important;
}

/* StablecoinPay Checkout Container - High Specificity */
.woocommerce .stablecoinpay-checkout-container,
.wc-block-checkout .stablecoinpay-checkout-container,
body .woocommerce-checkout .stablecoinpay-checkout-container,
body.woocommerce-checkout .stablecoinpay-checkout-container,
.wc-block-components-payment-method-content .stablecoinpay-checkout-container {
  margin: 0 !important;
  padding: 16px 24px !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  font-family: inherit !important;
}

/* Description Styling */
.stablecoinpay-description,
.woocommerce .stablecoinpay-description,
.wc-block-checkout .stablecoinpay-description {
  margin: 0 0 16px 0 !important;
  padding: 0 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: var(--stablecoinpay-text-secondary) !important;
  background: transparent !important;
  border: none !important;
}

/* Payment Selector Container */
.stablecoinpay-payment-selector,
.stablecoinpay-method-selector,
.woocommerce .stablecoinpay-payment-selector,
.wc-block-checkout .stablecoinpay-method-selector {
  margin: 0 0 16px 0 !important;
  padding: 0 !important;
  background: transparent !important;
}

/* Label Styling - High Specificity for Blocksy */
.stablecoinpay-payment-selector label,
.stablecoinpay-method-selector label,
.woocommerce .stablecoinpay-payment-selector label,
.wc-block-checkout .stablecoinpay-method-selector label,
body .stablecoinpay-payment-selector label,
body .stablecoinpay-method-selector label {
  display: block !important;
  margin: 0 0 8px 0 !important;
  padding: 0 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  color: var(--stablecoinpay-text-primary) !important;
  background: transparent !important;
  border: none !important;
  cursor: auto !important;
}

/* CRITICAL: Hide Native Select with Maximum Specificity */
.stablecoinpay-select,
select.stablecoinpay-select,
.payment-method-select,
select.payment-method-select,
.woocommerce .stablecoinpay-select,
.woocommerce select.stablecoinpay-select,
.wc-block-checkout .stablecoinpay-select,
.wc-block-checkout select.stablecoinpay-select,
body .woocommerce .stablecoinpay-select,
body .woocommerce select.stablecoinpay-select,
body .wc-block-checkout .stablecoinpay-select,
body .wc-block-checkout select.stablecoinpay-select,
body.woocommerce-checkout .stablecoinpay-select,
body.woocommerce-checkout select.stablecoinpay-select {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  width: 1px !important;
  height: 1px !important;
  overflow: hidden !important;
  clip: rect(1px, 1px, 1px, 1px) !important;
  white-space: nowrap !important;
  z-index: -1 !important;
}

/* Custom Dropdown Wrapper - Blocksy Theme Compatibility */
.custom-select-wrapper,
.stablecoinpay-unified-dropdown,
.stablecoinpay-blocks-dropdown-wrapper,
.woocommerce .custom-select-wrapper,
.wc-block-checkout .stablecoinpay-unified-dropdown,
body .custom-select-wrapper,
body .stablecoinpay-unified-dropdown {
  position: relative !important;
  display: block !important;
  width: 100% !important;
  max-width: 100% !important;
  z-index: 10 !important;
  font-family: inherit !important;
  box-sizing: border-box !important;
}

/* Custom Dropdown Container */
.custom-dropdown,
.woocommerce .custom-dropdown,
.wc-block-checkout .custom-dropdown {
  position: relative !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* Dropdown Button - Maximum Specificity for Blocksy */
.dropdown-button,
.stablecoinpay-blocks-dropdown-button,
button.dropdown-button,
button.stablecoinpay-blocks-dropdown-button,
.woocommerce .dropdown-button,
.wc-block-checkout .stablecoinpay-blocks-dropdown-button,
body .dropdown-button,
body .stablecoinpay-blocks-dropdown-button,
body.woocommerce-checkout .dropdown-button {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 12px 16px !important;
  border: 2px solid var(--stablecoinpay-border) !important;
  border-radius: var(--stablecoinpay-radius) !important;
  background: var(--stablecoinpay-background) !important;
  font-size: 15px !important;
  font-weight: 500 !important;
  font-family: inherit !important;
  cursor: pointer !important;
  transition: var(--stablecoinpay-transition) !important;
  width: 100% !important;
  max-width: 100% !important;
  text-align: left !important;
  min-height: 52px !important;
  box-sizing: border-box !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  outline: none !important;
  color: var(--stablecoinpay-text-primary) !important;
  text-decoration: none !important;
  line-height: 1.4 !important;
  position: relative !important;
  overflow: hidden !important;
}

/* Dropdown Button Hover - Blocksy Override */
.dropdown-button:hover,
.stablecoinpay-blocks-dropdown-button:hover,
button.dropdown-button:hover,
button.stablecoinpay-blocks-dropdown-button:hover,
.woocommerce .dropdown-button:hover,
.wc-block-checkout .stablecoinpay-blocks-dropdown-button:hover,
body .dropdown-button:hover,
body .stablecoinpay-blocks-dropdown-button:hover {
  border-color: var(--stablecoinpay-border-focus) !important;
  box-shadow: var(--stablecoinpay-shadow) !important;
  background: var(--stablecoinpay-background) !important;
  transform: none !important;
}

/* Dropdown Button Focus/Active - Blocksy Override */
.dropdown-button:focus,
.dropdown-button.active,
.stablecoinpay-blocks-dropdown-button:focus,
button.dropdown-button:focus,
button.dropdown-button.active,
.woocommerce .dropdown-button:focus,
.woocommerce .dropdown-button.active,
body .dropdown-button:focus,
body .dropdown-button.active {
  outline: none !important;
  border-color: var(--stablecoinpay-border-focus) !important;
  box-shadow: var(--stablecoinpay-shadow-focus), var(--stablecoinpay-shadow) !important;
  background: var(--stablecoinpay-background) !important;
  z-index: 11 !important;
}

/* Dropdown Selected Content */
.dropdown-selected,
.woocommerce .dropdown-selected,
.wc-block-checkout .dropdown-selected {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  flex: 1 !important;
  min-width: 0 !important;
  overflow: hidden !important;
}

/* Selected Icon */
.dropdown-selected-icon,
.woocommerce .dropdown-selected-icon,
.wc-block-checkout .dropdown-selected-icon {
  width: 24px !important;
  height: 24px !important;
  border-radius: 50% !important;
  flex-shrink: 0 !important;
  object-fit: cover !important;
  border: none !important;
  background: transparent !important;
}

/* Selected Text Container */
.dropdown-selected-text,
.woocommerce .dropdown-selected-text,
.wc-block-checkout .dropdown-selected-text {
  display: flex !important;
  flex-direction: column !important;
  gap: 2px !important;
  flex: 1 !important;
  min-width: 0 !important;
  overflow: hidden !important;
}

/* Selected Token Name */
.dropdown-selected-token,
.woocommerce .dropdown-selected-token,
.wc-block-checkout .dropdown-selected-token {
  font-weight: 600 !important;
  font-size: 14px !important;
  color: var(--stablecoinpay-text-primary) !important;
  line-height: 1.2 !important;
  margin: 0 !important;
  padding: 0 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Selected Network */
.dropdown-selected-network,
.woocommerce .dropdown-selected-network,
.wc-block-checkout .dropdown-selected-network {
  font-size: 12px !important;
  color: var(--stablecoinpay-text-secondary) !important;
  font-weight: 400 !important;
  line-height: 1.2 !important;
  margin: 0 !important;
  padding: 0 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Dropdown Arrow */
.dropdown-arrow,
.woocommerce .dropdown-arrow,
.wc-block-checkout .dropdown-arrow {
  width: 20px !important;
  height: 20px !important;
  transition: transform 0.3s ease !important;
  color: var(--stablecoinpay-text-secondary) !important;
  flex-shrink: 0 !important;
  transform: rotate(0deg) !important;
  margin: 0 !important;
  padding: 0 !important;
}

.dropdown-button.active .dropdown-arrow,
.woocommerce .dropdown-button.active .dropdown-arrow,
.wc-block-checkout .dropdown-button.active .dropdown-arrow {
  transform: rotate(180deg) !important;
}

/* Dropdown Menu - Maximum Specificity for Blocksy */
.dropdown-menu,
.stablecoinpay-blocks-dropdown-menu,
.stablecoinpay-unified-dropdown .dropdown-menu,
.woocommerce .dropdown-menu,
.wc-block-checkout .stablecoinpay-blocks-dropdown-menu,
body .dropdown-menu,
body .stablecoinpay-blocks-dropdown-menu,
body.woocommerce-checkout .dropdown-menu {
  position: absolute !important;
  top: 100% !important;
  left: 0 !important;
  right: 0 !important;
  background: var(--stablecoinpay-background) !important;
  border: 2px solid var(--stablecoinpay-border) !important;
  border-radius: var(--stablecoinpay-radius) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  z-index: 99999 !important;
  max-height: 300px !important;
  overflow-y: auto !important;
  margin-top: 4px !important;
  min-width: 100% !important;
  box-sizing: border-box !important;
  opacity: 1 !important;
  visibility: visible !important;
  transform: translateY(0) !important;
  display: block !important;
  font-family: inherit !important;
}

/* Hidden Menu State */
.dropdown-menu:not(.active),
.woocommerce .dropdown-menu:not(.active),
.wc-block-checkout .dropdown-menu:not(.active) {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transform: translateY(-8px) !important;
}

/* Dropdown Group */
.dropdown-group,
.woocommerce .dropdown-group,
.wc-block-checkout .dropdown-group {
  padding: 8px 0 !important;
  margin: 0 !important;
  border: none !important;
  background: transparent !important;
}

/* Group Label */
.dropdown-group-label,
.woocommerce .dropdown-group-label,
.wc-block-checkout .dropdown-group-label {
  padding: 8px 16px 4px !important;
  font-size: 11px !important;
  font-weight: 600 !important;
  color: var(--stablecoinpay-text-secondary) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  background: #f9fafb !important;
  border-bottom: 1px solid #f3f4f6 !important;
  margin: 0 !important;
  line-height: 1 !important;
}

/* Dropdown Option - Maximum Specificity for Blocksy */
.dropdown-option,
button.dropdown-option,
.stablecoinpay-blocks-dropdown-menu button,
.woocommerce .dropdown-option,
.wc-block-checkout .dropdown-option,
body .dropdown-option,
body.woocommerce-checkout .dropdown-option {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  padding: 12px 16px !important;
  cursor: pointer !important;
  border: none !important;
  background: none !important;
  width: 100% !important;
  max-width: 100% !important;
  text-align: left !important;
  font-family: inherit !important;
  font-size: inherit !important;
  transition: background-color 0.2s ease !important;
  outline: none !important;
  color: var(--stablecoinpay-text-primary) !important;
  margin: 0 !important;
  box-sizing: border-box !important;
  line-height: 1.4 !important;
  text-decoration: none !important;
  position: relative !important;
  overflow: hidden !important;
}

/* Dropdown Option Hover */
.dropdown-option:hover,
button.dropdown-option:hover,
.woocommerce .dropdown-option:hover,
.wc-block-checkout .dropdown-option:hover,
body .dropdown-option:hover {
  background: var(--stablecoinpay-background-hover) !important;
  color: var(--stablecoinpay-text-primary) !important;
}

/* Dropdown Option Selected */
.dropdown-option.selected,
button.dropdown-option.selected,
.woocommerce .dropdown-option.selected,
.wc-block-checkout .dropdown-option.selected,
body .dropdown-option.selected {
  background: var(--stablecoinpay-background-selected) !important;
  color: var(--stablecoinpay-primary) !important;
}

/* Option Icon */
.dropdown-option-icon,
.woocommerce .dropdown-option-icon,
.wc-block-checkout .dropdown-option-icon {
  width: 24px !important;
  height: 24px !important;
  border-radius: 50% !important;
  flex-shrink: 0 !important;
  object-fit: cover !important;
  border: none !important;
  background: transparent !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Option Text Container */
.dropdown-option-text,
.woocommerce .dropdown-option-text,
.wc-block-checkout .dropdown-option-text {
  display: flex !important;
  flex-direction: column !important;
  gap: 2px !important;
  flex: 1 !important;
  min-width: 0 !important;
  overflow: hidden !important;
}

/* Option Token Name */
.dropdown-option-token,
.woocommerce .dropdown-option-token,
.wc-block-checkout .dropdown-option-token {
  font-weight: 600 !important;
  font-size: 14px !important;
  color: inherit !important;
  line-height: 1.2 !important;
  margin: 0 !important;
  padding: 0 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Option Network */
.dropdown-option-network,
.woocommerce .dropdown-option-network,
.wc-block-checkout .dropdown-option-network {
  font-size: 12px !important;
  color: var(--stablecoinpay-text-secondary) !important;
  font-weight: 400 !important;
  line-height: 1.2 !important;
  margin: 0 !important;
  padding: 0 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.dropdown-option.selected .dropdown-option-token,
.woocommerce .dropdown-option.selected .dropdown-option-token,
.wc-block-checkout .dropdown-option.selected .dropdown-option-token {
  color: var(--stablecoinpay-primary) !important;
}

/* Test Mode Notice */
.stablecoinpay-test-notice,
.stablecoinpay-test-mode-notice,
.woocommerce .stablecoinpay-test-notice,
.wc-block-checkout .stablecoinpay-test-mode-notice {
  margin: 16px 0 0 0 !important;
  padding: 12px 16px !important;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%) !important;
  border: 1px solid #f59e0b !important;
  border-radius: 8px !important;
  font-size: 13px !important;
  color: #92400e !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  line-height: 1.4 !important;
}

.test-badge,
.woocommerce .test-badge,
.wc-block-checkout .test-badge {
  background: #f59e0b !important;
  color: #fff !important;
  padding: 2px 8px !important;
  border-radius: 4px !important;
  font-size: 11px !important;
  font-weight: 700 !important;
  letter-spacing: 0.5px !important;
  margin: 0 !important;
  line-height: 1 !important;
}

/* Mobile Responsiveness - Enhanced for All Themes */
@media (max-width: 768px) {
  .dropdown-button,
  .stablecoinpay-blocks-dropdown-button,
  button.dropdown-button,
  .woocommerce .dropdown-button,
  body .dropdown-button {
    padding: 16px !important;
    min-height: 56px !important;
    font-size: 16px !important; /* Prevent zoom on iOS */
  }

  .dropdown-selected-icon,
  .dropdown-option-icon,
  .woocommerce .dropdown-selected-icon,
  .woocommerce .dropdown-option-icon {
    width: 28px !important;
    height: 28px !important;
  }

  .dropdown-menu,
  .stablecoinpay-blocks-dropdown-menu,
  .woocommerce .dropdown-menu,
  body .dropdown-menu {
    max-height: 250px !important;
    margin-top: 8px !important;
  }

  .dropdown-option,
  button.dropdown-option,
  .woocommerce .dropdown-option,
  body .dropdown-option {
    padding: 16px !important;
  }

  .stablecoinpay-test-notice,
  .stablecoinpay-test-mode-notice,
  .woocommerce .stablecoinpay-test-notice {
    padding: 10px 12px !important;
    font-size: 12px !important;
  }
}

/* Desktop Enhancements */
@media (min-width: 769px) {
  .custom-select-wrapper,
  .stablecoinpay-unified-dropdown,
  .woocommerce .custom-select-wrapper {
    max-width: 400px !important;
  }

  .dropdown-menu,
  .stablecoinpay-blocks-dropdown-menu,
  .woocommerce .dropdown-menu {
    margin-top: 4px !important;
    transform: translateY(-8px) !important;
  }

  .dropdown-menu.active,
  .stablecoinpay-blocks-dropdown-menu.active,
  .woocommerce .dropdown-menu.active {
    transform: translateY(0) !important;
  }
}

/* High Contrast and Accessibility Support */
@media (prefers-contrast: high) {
  :root {
    --stablecoinpay-border: #000000;
    --stablecoinpay-text-primary: #000000;
    --stablecoinpay-text-secondary: #333333;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .dropdown-button,
  .dropdown-arrow,
  .dropdown-menu,
  .dropdown-option,
  .woocommerce .dropdown-button,
  .woocommerce .dropdown-arrow,
  .woocommerce .dropdown-menu,
  .woocommerce .dropdown-option {
    transition: none !important;
    animation: none !important;
  }
}

/* Print Styles */
@media print {
  .dropdown-menu,
  .stablecoinpay-blocks-dropdown-menu,
  .dropdown-arrow,
  .woocommerce .dropdown-menu,
  .woocommerce .dropdown-arrow {
    display: none !important;
  }
}

/* Force Override for Popular Theme Conflicts */

/* Blocksy Theme Specific Overrides */
.ct-container .stablecoinpay-unified-dropdown,
.ct-container .dropdown-button,
.ct-container .dropdown-menu {
  font-family: inherit !important;
  box-sizing: border-box !important;
}

/* Astra Theme Overrides */
.ast-container .dropdown-button,
.ast-container .dropdown-menu {
  font-family: inherit !important;
}

/* GeneratePress Theme Overrides */
.gp-container .dropdown-button,
.gp-container .dropdown-menu {
  font-family: inherit !important;
}

/* OceanWP Theme Overrides */
.oceanwp-theme .dropdown-button,
.oceanwp-theme .dropdown-menu {
  font-family: inherit !important;
}

/* Neve Theme Overrides */
.neve-main .dropdown-button,
.neve-main .dropdown-menu {
  font-family: inherit !important;
}

/* Additional Browser-Specific Fixes */

/* Firefox Specific */
@-moz-document url-prefix() {
  .dropdown-button,
  .woocommerce .dropdown-button {
    appearance: none !important;
    -moz-appearance: none !important;
  }
}

/* Safari Specific */
@supports (-webkit-appearance: none) {
  .dropdown-button,
  .woocommerce .dropdown-button {
    appearance: none !important;
    -webkit-appearance: none !important;
  }
}

/* Edge/IE Specific */
@supports (-ms-ime-align: auto) {
  .dropdown-button,
  .woocommerce .dropdown-button {
    appearance: none !important;
    -ms-appearance: none !important;
  }
}

/* Legacy Checkout Specific Fixes */

/* Ensure dropdown menu shows on legacy checkout */
.payment_method_stablecoinpay .dropdown-menu,
.stablecoinpay-checkout-container .dropdown-menu,
body .payment_method_stablecoinpay .dropdown-menu,
body .stablecoinpay-checkout-container .dropdown-menu {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
}

.payment_method_stablecoinpay .dropdown-menu.active,
.stablecoinpay-checkout-container .dropdown-menu.active,
body .payment_method_stablecoinpay .dropdown-menu.active,
body .stablecoinpay-checkout-container .dropdown-menu.active {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: auto !important;
}

/* Force dropdown button to be clickable */
.payment_method_stablecoinpay .dropdown-button,
.stablecoinpay-checkout-container .dropdown-button,
body .payment_method_stablecoinpay .dropdown-button,
body .stablecoinpay-checkout-container .dropdown-button {
  pointer-events: auto !important;
  cursor: pointer !important;
  position: relative !important;
  z-index: 10 !important;
}

/* Ensure dropdown options are clickable */
.payment_method_stablecoinpay .dropdown-option,
.stablecoinpay-checkout-container .dropdown-option,
body .payment_method_stablecoinpay .dropdown-option,
body .stablecoinpay-checkout-container .dropdown-option {
  pointer-events: auto !important;
  cursor: pointer !important;
  position: relative !important;
}

/* Blocksy Theme Specific Overrides for Legacy Checkout */
.ct-container .payment_method_stablecoinpay .dropdown-button,
.ct-container .payment_method_stablecoinpay .dropdown-menu,
.ct-container .stablecoinpay-checkout-container .dropdown-button,
.ct-container .stablecoinpay-checkout-container .dropdown-menu {
  font-family: inherit !important;
  box-sizing: border-box !important;
  pointer-events: auto !important;
}

/* Legacy Checkout - Force Visibility */
.woocommerce-checkout .payment_method_stablecoinpay,
.woocommerce-checkout .stablecoinpay-checkout-container,
body.woocommerce-checkout .payment_method_stablecoinpay,
body.woocommerce-checkout .stablecoinpay-checkout-container {
  visibility: visible !important;
  opacity: 1 !important;
  display: block !important;
}
