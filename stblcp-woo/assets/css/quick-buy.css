/* StablecoinPay Quick Buy Styles */

/* Quick Buy Container */
.stablecoinpay-quick-buy-container {
  margin: 16px 0;
  position: relative;
}

/* Split Button Layout */
.quick-buy-split-button {
  display: flex;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
  transition: box-shadow 0.2s ease;
  max-width: 400px;
}

.quick-buy-split-button:hover {
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.25);
}

/* Primary Button */
.quick-buy-primary {
  flex: 1;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
}

.quick-buy-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-1px);
}

.quick-buy-primary:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.quick-buy-primary.loading {
  position: relative !important;
}

/* High specificity spinner with multiple selectors to override theme styles */
.quick-buy-primary.loading::after,
.stablecoinpay-quick-buy-container .quick-buy-primary.loading::after,
.woocommerce .quick-buy-primary.loading::after,
.woocommerce-cart .quick-buy-primary.loading::after,
.cart_totals .quick-buy-primary.loading::after {
  content: "" !important;
  position: absolute !important;
  right: 16px !important;
  top: 50% !important;
  left: auto !important;
  bottom: auto !important;
  transform: translateY(-50%) !important;
  width: 16px !important;
  height: 16px !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-top: 2px solid white !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
  /* Force proper centering */
  margin: 0 !important;
  padding: 0 !important;
  z-index: 10 !important;
  display: block !important;
  /* Ensure it's not affected by text alignment */
  text-align: left !important;
  vertical-align: top !important;
}

@keyframes spin {
  0% {
    transform: translateY(-50%) rotate(0deg);
  }
  100% {
    transform: translateY(-50%) rotate(360deg);
  }
}

/* Token Icon */
.quick-buy-token-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  flex-shrink: 0;
}

/* Button Text */
.quick-buy-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1.2;
}

.quick-buy-token-name {
  font-size: 13px;
  font-weight: 500;
  opacity: 0.9;
}

/* Dropdown Toggle */
.quick-buy-dropdown-toggle {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  border: none;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  padding: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48px;
}

.quick-buy-dropdown-toggle:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.quick-buy-chevron {
  transition: transform 0.2s ease;
}

.quick-buy-dropdown-toggle[aria-expanded="true"] .quick-buy-chevron {
  transform: rotate(180deg);
}

/* Dropdown Menu */
.quick-buy-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 4px;
  max-height: 300px;
  overflow-y: auto;
  min-width: 280px;
  max-width: 400px;
}

/* Dropdown Groups */
.quick-buy-dropdown-group {
  padding: 8px 0;
}

.quick-buy-dropdown-group:not(:last-child) {
  border-bottom: 1px solid #f3f4f6;
}

.quick-buy-group-label {
  padding: 8px 16px 4px;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Dropdown Options */
.quick-buy-dropdown-option {
  width: 100%;
  background: none;
  border: none;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-align: left;
}

.quick-buy-dropdown-option:hover {
  background-color: #f9fafb;
}

.quick-buy-dropdown-option:focus {
  outline: none;
  background-color: #f3f4f6;
}

.quick-buy-option-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

.quick-buy-option-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.quick-buy-option-token {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.quick-buy-option-network {
  font-size: 12px;
  color: #6b7280;
}

/* Email Modal - Enhanced Centering and Responsiveness */
.stablecoinpay-email-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  /* Ensure modal is always visible */
  visibility: visible;
  opacity: 1;
}

/* BLOCK-BASED CART FIX: Ensure modal works in WooCommerce blocks context */
.wp-block-woocommerce-cart .stablecoinpay-email-modal,
.wc-block-cart .stablecoinpay-email-modal {
  z-index: 999999; /* Very high z-index for block context */
  pointer-events: auto;
}

.wp-block-woocommerce-cart .stablecoinpay-email-modal .email-modal-content,
.wc-block-cart .stablecoinpay-email-modal .email-modal-content {
  pointer-events: auto;
  position: relative;
  z-index: 1;
}

.wp-block-woocommerce-cart .stablecoinpay-email-modal .btn-continue,
.wp-block-woocommerce-cart .stablecoinpay-email-modal .btn-cancel,
.wp-block-woocommerce-cart .stablecoinpay-email-modal .email-modal-close,
.wc-block-cart .stablecoinpay-email-modal .btn-continue,
.wc-block-cart .stablecoinpay-email-modal .btn-cancel,
.wc-block-cart .stablecoinpay-email-modal .email-modal-close {
  pointer-events: auto;
  cursor: pointer;
  position: relative;
  z-index: 2;
}

/* Enhanced Modal Overlay - Fixed positioning for better centering */
.email-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* Support for modern browsers with dynamic viewport height */
  min-height: 100vh;
  min-height: 100dvh;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
  /* Smooth backdrop transition */
  transition: background-color 0.2s ease, backdrop-filter 0.2s ease;
}

/* Enhanced Modal Content - Better responsive sizing */
.email-modal-content {
  background: white;
  border-radius: 16px;
  max-width: 480px;
  width: 100%;
  max-height: 90vh;
  max-height: 90dvh; /* Dynamic viewport height for mobile */
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  /* Smooth content transitions */
  transform: scale(1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  /* Ensure proper box sizing */
  box-sizing: border-box;
  /* Better scrolling on mobile */
  -webkit-overflow-scrolling: touch;
}

.email-modal-header {
  padding: 24px 24px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.email-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
}

.email-modal-close {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.email-modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.email-modal-body {
  padding: 24px;
}

.email-modal-description {
  font-size: 1rem;
  margin: 0 0 24px;
  color: #6b7280;
  line-height: 1.5;
}

/* Form Styles */
.email-collection-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-field label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.required {
  color: #ef4444;
}

.optional {
  font-weight: 400;
  color: #6b7280;
}

/* UX IMPROVEMENT: Email input wrapper for clear button */
.email-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-field input {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
  width: 100%;
}

.email-input-wrapper input {
  padding-right: 44px; /* Make room for clear button */
}

.form-field input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* UX IMPROVEMENT: Clear saved email button */
.clear-saved-email {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.clear-saved-email:hover {
  background: #f3f4f6;
  color: #374151;
}

.clear-saved-email:focus {
  outline: none;
  background: #f3f4f6;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.field-error {
  font-size: 14px;
  color: #ef4444;
  margin-top: 4px;
}

/* Modal Actions */
.email-modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

.btn-cancel {
  padding: 12px 24px;
  background: #f3f4f6;
  color: #374151;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-cancel:hover {
  background: #e5e7eb;
}

.btn-continue {
  padding: 12px 24px;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 140px;
  justify-content: center;
}

.btn-continue:hover:not(:disabled) {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.btn-continue:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.btn-spinner {
  display: flex;
  align-items: center;
  height: 0;
  margin-top: 16px;
}

.spinner {
  animation: spin 1s linear infinite;
}

/* Body scroll lock */
body.stablecoinpay-modal-open {
  overflow: hidden;
}

/* Enhanced Responsive Design - Multiple Breakpoints */

/* Large tablets and small desktops */
@media (max-width: 1024px) {
  .email-modal-content {
    max-width: 440px;
  }

  .email-modal-overlay {
    padding: 24px;
  }
}

/* Tablets */
@media (max-width: 768px) {
  .quick-buy-split-button {
    max-width: none;
  }

  .quick-buy-primary {
    padding: 10px 14px;
    font-size: 14px;
    min-height: 44px;
  }

  .quick-buy-token-icon {
    width: 20px;
    height: 20px;
  }

  .quick-buy-text {
    gap: 2px;
  }

  .quick-buy-token-name {
    font-size: 12px;
  }

  .quick-buy-dropdown-toggle {
    min-width: 44px;
    padding: 10px;
  }

  /* Enhanced modal responsiveness for tablets */
  .email-modal-overlay {
    padding: 20px;
  }

  .email-modal-content {
    border-radius: 12px;
    max-width: 400px;
  }

  .email-modal-header,
  .email-modal-body {
    padding: 20px;
  }

  .email-modal-actions {
    flex-direction: column-reverse;
    gap: 10px;
  }

  .btn-cancel,
  .btn-continue {
    width: 100%;
    justify-content: center;
    min-height: 48px; /* Better touch targets */
  }

  /* UX IMPROVEMENT: Mobile adjustments for email input */
  .email-input-wrapper input {
    padding-right: 40px; /* Slightly smaller on mobile */
    min-height: 48px; /* Better touch target */
  }

  .clear-saved-email {
    right: 6px; /* Closer to edge on mobile */
    padding: 6px; /* Smaller touch target */
  }

  /* Cart page spinner fix for tablets */
  .woocommerce-cart .quick-buy-primary.loading::after,
  .cart_totals .quick-buy-primary.loading::after {
    right: 14px; /* Adjust for smaller padding */
  }
}

/* STANDARDIZED ERROR HANDLING: Professional notification system */
.stablecoinpay-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 999999;
  max-width: 400px;
  min-width: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.3s ease-out;
}

.stablecoinpay-notification-content {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.stablecoinpay-notification-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  font-weight: 500;
}

.stablecoinpay-notification-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.stablecoinpay-notification-error {
  background: #fee;
  border-left: 4px solid #dc3545;
  color: #721c24;
}

.stablecoinpay-notification-error .stablecoinpay-notification-close {
  color: #721c24;
}

.stablecoinpay-notification-error .stablecoinpay-notification-close:hover {
  background: rgba(220, 53, 69, 0.1);
}

.stablecoinpay-notification-success {
  background: #d4edda;
  border-left: 4px solid #28a745;
  color: #155724;
}

.stablecoinpay-notification-success .stablecoinpay-notification-close {
  color: #155724;
}

.stablecoinpay-notification-success .stablecoinpay-notification-close:hover {
  background: rgba(40, 167, 69, 0.1);
}

.stablecoinpay-notification-info {
  background: #d1ecf1;
  border-left: 4px solid #17a2b8;
  color: #0c5460;
}

.stablecoinpay-notification-info .stablecoinpay-notification-close {
  color: #0c5460;
}

.stablecoinpay-notification-info .stablecoinpay-notification-close:hover {
  background: rgba(23, 162, 184, 0.1);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Mobile adjustments for notifications */
@media (max-width: 768px) {
  .stablecoinpay-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
  }

  .stablecoinpay-notification-content {
    padding: 12px 16px;
  }

  .stablecoinpay-notification-message {
    font-size: 13px;
  }
}

/* Mobile devices */
@media (max-width: 480px) {
  .quick-buy-primary {
    flex-direction: row;
    align-items: flex-start;
    gap: 6px;
    padding: 12px;
  }

  .quick-buy-text {
    font-size: 13px;
  }

  .quick-buy-token-name {
    font-size: 11px;
  }

  /* Enhanced mobile modal styling */
  .email-modal-overlay {
    padding: 12px;
    /* Better centering on small screens */
    align-items: flex-start;
    padding-top: 5vh;
  }

  .email-modal-content {
    border-radius: 12px;
    max-width: none;
    width: calc(100% - 24px);
    max-height: 85vh;
    max-height: 85dvh;
    margin: 0 auto;
  }

  .email-modal-header {
    padding: 16px 16px 0;
  }

  .email-modal-header h3 {
    font-size: 18px;
  }

  .email-modal-body {
    padding: 16px;
  }

  .email-modal-actions {
    margin-top: 20px;
    gap: 8px;
  }

  .btn-continue {
    min-height: 50px; /* Larger touch targets on mobile */
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .btn-cancel {
    min-height: 40px;
    font-size: 16px;
  }

  .form-field input {
    font-size: 16px; /* Prevent zoom on iOS */
    min-height: 32px;
    padding: 8px;
  }

  .email-input-wrapper input {
    padding-right: 44px;
  }

  /* Cart page spinner fix for mobile */
  .woocommerce-cart .quick-buy-primary.loading::after,
  .cart_totals .quick-buy-primary.loading::after {
    right: 12px; /* Adjust for mobile padding */
    width: 14px;
    height: 14px;
    border-width: 1.5px;
  }
}

/* Very small mobile devices */
@media (max-width: 375px) {
  .email-modal-overlay {
    padding: 8px;
    padding-top: 3vh;
  }

  .email-modal-content {
    width: calc(100% - 16px);
    border-radius: 8px;
  }

  .email-modal-header,
  .email-modal-body {
    padding: 12px;
  }

  .email-modal-header h3 {
    font-size: 16px;
  }
}

/* Extra small devices */
@media (max-width: 320px) {
  .email-modal-overlay {
    padding: 4px;
    padding-top: 2vh;
  }

  .email-modal-content {
    width: calc(100% - 8px);
  }
}

/* Landscape orientation on mobile devices */
@media (max-height: 500px) and (orientation: landscape) {
  .email-modal-overlay {
    padding: 8px;
    align-items: center; /* Center vertically in landscape */
  }

  .email-modal-content {
    max-height: 80vh;
    max-height: 80dvh;
  }

  .email-modal-header,
  .email-modal-body {
    padding: 12px 16px;
  }

  .email-modal-actions {
    margin-top: 12px;
  }
}

/* Theme Compatibility */
.woocommerce .stablecoinpay-quick-buy-container {
  margin: 16px 0;
  display: block !important;
}

/* Product page - after add to cart button */
.single-product .stablecoinpay-quick-buy-container {
  margin-top: 20px;
  clear: both;
}

/* Cart page - after cart totals */
.woocommerce-cart .stablecoinpay-quick-buy-container {
  margin: 20px 0;
  text-align: center;
  display: block !important;
  clear: both;
}

/* Specific cart totals styling */
.cart_totals .stablecoinpay-quick-buy-container {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

/* Cart page spinner centering fix with maximum specificity */
.woocommerce-cart .stablecoinpay-quick-buy-container .quick-buy-primary,
.cart_totals .stablecoinpay-quick-buy-container .quick-buy-primary,
.woocommerce .cart_totals .quick-buy-primary {
  /* Ensure button maintains proper flexbox alignment despite parent text-align */
  display: flex !important;
  align-items: center !important;
  justify-content: start !important;
  margin: 0 auto !important;
  position: relative !important;
}

/* Maximum specificity for cart page spinner */
.woocommerce-cart
  .stablecoinpay-quick-buy-container
  .quick-buy-primary.loading::after,
.cart_totals
  .stablecoinpay-quick-buy-container
  .quick-buy-primary.loading::after,
.woocommerce .cart_totals .quick-buy-primary.loading::after,
body.woocommerce-cart .quick-buy-primary.loading::after {
  /* Enhanced spinner positioning for cart page with maximum specificity */
  position: absolute !important;
  right: 16px !important;
  top: 50% !important;
  left: auto !important;
  bottom: auto !important;
  transform: translateY(-50%) !important;
  /* Force proper centering */
  margin: 0 !important;
  padding: 0 !important;
  z-index: 15 !important;
  /* Override any inherited positioning */
  float: none !important;
  clear: none !important;
}

/* Ensure split button container is properly centered on cart page */
.woocommerce-cart .quick-buy-split-button,
.cart_totals .quick-buy-split-button {
  margin: 0 auto;
  display: flex;
}

/* WooCommerce Blocks cart page spinner fixes */
.wp-block-woocommerce-cart .quick-buy-primary,
.wc-block-cart .quick-buy-primary {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.wp-block-woocommerce-cart .quick-buy-primary.loading::after,
.wc-block-cart .quick-buy-primary.loading::after {
  position: absolute !important;
  right: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin: 0 !important;
  z-index: 20 !important;
}

/* JavaScript fallback targeting - highest specificity */
.quick-buy-primary[data-loading-context="cart"].loading::after,
button.quick-buy-primary[data-loading-context="cart"].loading::after {
  position: absolute !important;
  right: 16px !important;
  top: 50% !important;
  left: auto !important;
  bottom: auto !important;
  transform: translateY(-50%) !important;
  margin: 0 !important;
  padding: 0 !important;
  z-index: 25 !important;
  display: block !important;
  width: 16px !important;
  height: 16px !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-top: 2px solid white !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
  content: "" !important;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .quick-buy-split-button {
    border: 2px solid #000;
  }

  .quick-buy-dropdown-menu {
    border: 2px solid #000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .quick-buy-primary,
  .quick-buy-dropdown-toggle,
  .quick-buy-chevron,
  .email-modal-close,
  .email-modal-overlay,
  .email-modal-content {
    transition: none;
  }

  .spinner {
    animation: none;
  }
}

/* Enhanced Accessibility and Modern Browser Support */

/* Focus management for better keyboard navigation */
.email-modal-content:focus {
  outline: none;
}

.email-modal-content:focus-visible {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Better support for browsers without dvh support */
@supports not (height: 100dvh) {
  .email-modal-overlay {
    min-height: 100vh;
  }

  .email-modal-content {
    max-height: 90vh;
  }

  @media (max-width: 480px) {
    .email-modal-content {
      max-height: 85vh;
    }
  }

  @media (max-height: 500px) and (orientation: landscape) {
    .email-modal-content {
      max-height: 80vh;
    }
  }
}

/* Better support for browsers without backdrop-filter */
@supports not (backdrop-filter: blur(2px)) {
  .email-modal-overlay {
    background: rgba(0, 0, 0, 0.6);
  }
}

/* Print styles - hide modal when printing */
@media print {
  .stablecoinpay-email-modal {
    display: none !important;
  }
}

/* Dark mode support (if theme supports it) */
@media (prefers-color-scheme: dark) {
  .email-modal-content {
    background: #1f2937;
    color: #f9fafb;
  }

  .email-modal-header h3 {
    color: #f9fafb;
  }

  .email-modal-description {
    color: #d1d5db;
  }

  .form-field label {
    color: #e5e7eb;
  }

  .form-field input {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .form-field input:focus {
    border-color: #3b82f6;
    background: #374151;
  }

  .btn-cancel {
    background: #374151;
    color: #e5e7eb;
  }

  .btn-cancel:hover {
    background: #4b5563;
  }
}
