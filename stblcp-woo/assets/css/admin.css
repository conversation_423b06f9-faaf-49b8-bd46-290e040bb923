/* StablecoinPay Admin Styles */

/* Animations */
@keyframes stablecoinpay-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.stablecoinpay-spin {
  animation: stablecoinpay-spin 1s linear infinite;
}

/* Cloudflare IP Info Styles */
.cloudflare-ip-info .notice {
  margin: 10px 0;
  padding: 10px;
}

.cloudflare-ip-info textarea {
  font-family: 'Courier New', Courier, monospace;
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.cloudflare-ip-info .button.copied {
  background: #28a745;
  border-color: #28a745;
  color: white;
}
#cloudflare-ips-section .stablecoinpay-settings-section {
  padding: 24px;
}

/* Main Layout */
.stablecoinpay-admin-container {
  max-width: 1200px;
  margin: 0 auto;
}
.stablecoinpay-admin-container h1 {
  margin: 24px 0 16px 0;
}

/* Settings Sections */
.stablecoinpay-settings-section {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.stablecoinpay-settings-section:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Collapsible Section Styling */
.stablecoinpay-settings-section.collapsible .stablecoinpay-section-header {
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  position: relative;
}

.stablecoinpay-settings-section.collapsible
  .stablecoinpay-section-header:focus {
  outline: 2px solid #667eea;
  outline-offset: -2px;
  border-radius: 8px;
}

.stablecoinpay-section-header {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 20px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.stablecoinpay-section-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1d2327;
  flex-grow: 1;
}

.stablecoinpay-section-icon {
  width: 20px;
  height: 20px;
  font-size: 20px;
  color: #667eea;
  flex-shrink: 0;
  display: inline-block;
  line-height: 1;
  font-family: dashicons;
  text-decoration: inherit;
  font-weight: normal;
  font-style: normal;
  vertical-align: top;
  text-align: center;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Collapse Indicator */
.collapse-indicator {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #6c757d;
  transition: transform 0.3s ease, color 0.2s ease;
  flex-shrink: 0;
  margin-left: auto;
  display: inline-block;
  line-height: 1;
  font-family: dashicons;
  text-decoration: inherit;
  font-weight: normal;
  font-style: normal;
  vertical-align: top;
  text-align: center;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.stablecoinpay-settings-section.collapsible
  .stablecoinpay-section-header:hover
  .collapse-indicator {
  color: #495057;
}

.stablecoinpay-settings-section.expanded .collapse-indicator {
  transform: rotate(180deg);
}

/* Collapsible Content */
.stablecoinpay-section-content {
  padding: 24px;
}

.stablecoinpay-settings-section.collapsible:not(.expanded)
  .stablecoinpay-section-content {
  display: none;
}

.stablecoinpay-settings-section.expanded .stablecoinpay-section-header {
  border-bottom-color: #d1d5db;
}

.stablecoinpay-section-description {
  margin: 0 0 20px 0;
  color: #646970;
  font-size: 14px;
  line-height: 1.5;
}

/* Enhanced Section States */
.stablecoinpay-settings-section.expanded {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.stablecoinpay-settings-section.expanded .stablecoinpay-section-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.stablecoinpay-settings-section.expanded .stablecoinpay-section-header h2 {
  color: #ffffff;
}

.stablecoinpay-settings-section.expanded .stablecoinpay-section-icon {
  color: #ffffff;
}

.stablecoinpay-settings-section.expanded .collapse-indicator {
  color: rgba(255, 255, 255, 0.8);
}

.stablecoinpay-settings-section.expanded
  .stablecoinpay-section-header:hover
  .collapse-indicator {
  color: #ffffff;
}

/* Footer */
.stablecoinpay-admin-footer {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-top: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stablecoinpay-admin-footer h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1d2327;
}

.stablecoinpay-quick-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin: 0;
  padding: 0;
  list-style: none;
}

.stablecoinpay-quick-links li {
  margin: 0;
}

.stablecoinpay-quick-links a {
  display: block;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  text-decoration: none;
  color: #0073aa;
  font-weight: 500;
  transition: all 0.2s ease;
}

.stablecoinpay-quick-links a:hover {
  background: #e9ecef;
  color: #005177;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* External link styling */
.stablecoinpay-quick-links a[target="_blank"] {
  position: relative;
}

/* Enhanced Status Cards */
.stablecoinpay-status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

/* Single card layout optimization */
.stablecoinpay-status-cards:has(.stablecoinpay-status-card:only-child) {
  max-width: 600px;
}

/* Fallback for browsers that don't support :has() */
.stablecoinpay-status-cards.single-card {
  max-width: 600px;
}

.stablecoinpay-status-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #e9ecef;
}

.stablecoinpay-status-card.success {
  border-left-color: #28a745;
}

.stablecoinpay-status-card.warning {
  border-left-color: #ffc107;
}

.stablecoinpay-status-card.error {
  border-left-color: #dc3545;
}

.stablecoinpay-status-card.info {
  border-left-color: #17a2b8;
}

/* Enhanced Test API Button */
#test-api-connection {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

#test-api-connection:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
  color: #fff;
}

#test-api-connection:disabled,
#test-api-connection.testing {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

#test-api-connection .dashicons {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

#stablecoinpay-connection-status {
  margin-bottom: 20px;
}

.stablecoinpay-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
}

.stablecoinpay-status-indicator.success {
  background: #d1e7dd;
  color: #0f5132;
  border: 1px solid #badbcc;
}

.stablecoinpay-status-indicator.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c2c7;
}

.stablecoinpay-status-indicator.warning {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffecb5;
}

.stablecoinpay-status-indicator .dashicons {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Order Meta Box Styles */
.stablecoinpay-order-meta {
  font-size: 13px;
}

.stablecoinpay-order-meta table {
  width: 100%;
  border-collapse: collapse;
}

.stablecoinpay-order-meta td {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f1;
  vertical-align: top;
}

.stablecoinpay-order-meta td:first-child {
  width: 30%;
  font-weight: 600;
}

.stablecoinpay-order-meta code {
  background: #f0f0f1;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 12px;
  word-break: break-all;
}

.stablecoinpay-payment-link {
  margin-top: 10px;
}

.stablecoinpay-payment-link .button {
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  line-height: 1.4;
}

/* Enhanced Form Styles */
.stablecoinpay-form-table {
  background: transparent;
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
}

.stablecoinpay-form-table th {
  width: 220px;
  padding: 16px 0;
  vertical-align: top;
  font-weight: 600;
  color: #1d2327;
  font-size: 14px;
}

.stablecoinpay-form-table td {
  padding: 16px 0;
  vertical-align: top;
}

.stablecoinpay-form-row {
  border-bottom: 1px solid #f0f0f1;
  margin-bottom: 0;
}

.stablecoinpay-form-row:last-child {
  border-bottom: none;
}

.stablecoinpay-form-table .description {
  font-style: normal;
  color: #646970;
  font-size: 13px;
  line-height: 1.5;
  margin-top: 8px;
  margin-bottom: 0;
}

.stablecoinpay-form-table input[type="text"],
.stablecoinpay-form-table input[type="password"],
.stablecoinpay-form-table input[type="number"],
.stablecoinpay-form-table select,
.stablecoinpay-form-table textarea {
  width: 100%;
  max-width: 400px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.stablecoinpay-form-table input[type="text"]:focus,
.stablecoinpay-form-table input[type="password"]:focus,
.stablecoinpay-form-table input[type="number"]:focus,
.stablecoinpay-form-table select:focus,
.stablecoinpay-form-table textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 1px #667eea;
  outline: none;
}

.stablecoinpay-form-table textarea {
  height: 80px;
  resize: vertical;
  font-family: inherit;
}

.stablecoinpay-form-table input[type="checkbox"] {
  margin-right: 8px;
  transform: scale(1.1);
}

/* Field Groups */
.stablecoinpay-field-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stablecoinpay-field-label {
  font-weight: 600;
  color: #1d2327;
  margin-bottom: 4px;
}

.stablecoinpay-field-help {
  font-size: 12px;
  color: #646970;
  font-style: italic;
}

/* Enhanced Payment Methods */
.payment-method-wrapper {
  display: inline-block;
  width: 32%;
  margin-right: 8px;
  vertical-align: top; /* optional: aligns top if items have different heights */
  box-sizing: border-box;
}

.payment-method-wrapper label {
  height: 40px;
  display: block;
}

/* 📱 One item per row on mobile screens */
@media (max-width: 767px) {
  .payment-method-wrapper {
    width: 100%;
    margin-right: 0;
  }
}

.stablecoinpay-payment-methods {
  max-width: 100%;
}

.payment-methods-controls {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.select-all-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.select-all-controls strong {
  color: #1d2327;
  font-size: 14px;
}

.select-all-controls button {
  padding: 6px 12px;
  font-size: 12px;
  border: 1px solid #ddd;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.select-all-controls button:hover {
  background: #f0f0f0;
  border-color: #999;
}

.payment-method-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #e9ecef;
  margin-bottom: 6px;
  border-radius: 6px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.payment-method-item:hover {
  background: #f8f9fa;
  border-color: #667eea;
}

.payment-method-item input[type="checkbox"] {
  margin-right: 12px;
  transform: scale(1.2);
}

.payment-method-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  flex-shrink: 0;
  border-radius: 50%;
}

.payment-method-label {
  flex: 1;
  font-weight: 500;
  color: #1d2327;
}

.payment-method-network {
  color: #646970;
  font-size: 12px;
  margin-left: 8px;
  background: #f0f0f1;
  padding: 2px 6px;
  border-radius: 3px;
}

#selection-counter {
  margin-left: 12px;
  font-weight: bold;
  color: #667eea;
  font-size: 13px;
}

/* Enhanced Notices */
.notice p {
  color: #000;
}

.stablecoinpay-notice {
  background: #fff;
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #e9ecef;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.stablecoinpay-notice.success {
  border-left-color: #28a745;
  background: #f8fff9;
}

.stablecoinpay-notice.warning {
  border-left-color: #ffc107;
  background: #fffdf5;
}

.stablecoinpay-notice.error {
  border-left-color: #dc3545;
  background: #fff8f8;
}

.stablecoinpay-notice.info {
  border-left-color: #17a2b8;
  background: #f8fcfd;
}

.stablecoinpay-notice p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.stablecoinpay-notice ul {
  margin: 8px 0 0 20px;
}

.stablecoinpay-notice li {
  margin-bottom: 4px;
  font-size: 14px;
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
  .stablecoinpay-admin-container {
    margin: 0 -20px;
    padding: 0 20px;
  }

  .stablecoinpay-admin-header,
  .stablecoinpay-admin-footer,
  .stablecoinpay-settings-section {
    margin-left: -10px;
    margin-right: -10px;
    border-radius: 0;
  }

  .stablecoinpay-form-table th {
    width: 100%;
    display: block;
    padding-bottom: 8px;
  }

  .stablecoinpay-form-table td {
    display: block;
    padding-top: 0;
  }

  .stablecoinpay-form-table input[type="text"],
  .stablecoinpay-form-table input[type="password"],
  .stablecoinpay-form-table input[type="number"],
  .stablecoinpay-form-table select,
  .stablecoinpay-form-table textarea {
    max-width: 100%;
  }

  .stablecoinpay-status-cards {
    grid-template-columns: 1fr;
  }

  .stablecoinpay-quick-links {
    grid-template-columns: 1fr;
  }

  .select-all-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .select-all-controls > div {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
}

/* Submit Button Enhancement */
#submit {
  padding: 4px 16px !important;
}

.stablecoinpay-admin-container .button-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  text-shadow: none !important;
  transition: all 0.2s ease !important;
}

.stablecoinpay-admin-container .button-primary:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3) !important;
}

.stablecoinpay-admin-container .button-primary:active {
  transform: translateY(0) !important;
}

.stablecoinpay-admin-container .button-primary.stablecoinpay-has-changes {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;
  animation: pulse-changes 2s infinite;
}

@keyframes pulse-changes {
  0% {
    box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);
  }
  50% {
    box-shadow: 0 4px 8px rgba(255, 107, 53, 0.5);
  }
  100% {
    box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);
  }
}

/* Status Card Headers */
.stablecoinpay-status-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.stablecoinpay-status-title {
  font-weight: 600;
  font-size: 14px;
  margin: 0;
  color: #1d2327;
}

.stablecoinpay-status-message {
  margin: 0;
  font-size: 13px;
  color: #646970;
  line-height: 1.4;
}

/* Loading states */
.stablecoinpay-loading {
  opacity: 0.6;
  pointer-events: none;
}

.stablecoinpay-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: stablecoinpay-spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes stablecoinpay-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Form Validation States */
.stablecoinpay-form-table input.error,
.stablecoinpay-form-table select.error,
.stablecoinpay-form-table textarea.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 1px #dc3545;
}

.stablecoinpay-form-table input.success,
.stablecoinpay-form-table select.success,
.stablecoinpay-form-table textarea.success {
  border-color: #28a745;
  box-shadow: 0 0 0 1px #28a745;
}

/* Improved Typography */
.stablecoinpay-admin-container h1,
.stablecoinpay-admin-container h2,
.stablecoinpay-admin-container h3 {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

/* Better spacing for form elements */
.stablecoinpay-form-table tr:not(:last-child) {
  border-bottom: 1px solid #f0f0f1;
}

.stablecoinpay-form-table th,
.stablecoinpay-form-table td {
  padding: 20px 0;
}

/* Enhanced focus states */
.stablecoinpay-form-table input:focus,
.stablecoinpay-form-table select:focus,
.stablecoinpay-form-table textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  outline: none;
}

/* Custom Tooltip Styles */
.stablecoinpay-custom-tooltip {
  background: #1d2327;
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  max-width: 250px;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  display: none;
  pointer-events: none;
}

.stablecoinpay-custom-tooltip::before {
  content: "";
  position: absolute;
  top: 50%;
  left: -5px;
  transform: translateY(-50%);
  border: 5px solid transparent;
  border-right-color: #1d2327;
}

/* Notice styles */
.notice.stablecoinpay-notice {
  border-left-color: #0073aa;
}

.notice.stablecoinpay-notice .notice-title {
  font-weight: 600;
  margin-bottom: 5px;
}

/* Dashboard widget styles */
.stablecoinpay-dashboard-widget {
  padding: 12px;
}

.stablecoinpay-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stablecoinpay-stat {
  text-align: center;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 4px;
}

.stablecoinpay-stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #0073aa;
  display: block;
}

.stablecoinpay-stat-label {
  font-size: 12px;
  color: #646970;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stablecoinpay-recent-payments {
  margin-top: 15px;
}

.stablecoinpay-recent-payments h4 {
  margin-bottom: 10px;
  font-size: 14px;
}

.stablecoinpay-payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f1;
  font-size: 13px;
}

.stablecoinpay-payment-item:last-child {
  border-bottom: none;
}

.stablecoinpay-payment-order {
  font-weight: 600;
}

.stablecoinpay-payment-amount {
  color: #0073aa;
}

.stablecoinpay-payment-status {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.stablecoinpay-payment-status.confirmed {
  background: #d1e7dd;
  color: #0f5132;
}

.stablecoinpay-payment-status.waiting {
  background: #fff3cd;
  color: #856404;
}

.stablecoinpay-payment-status.expired {
  background: #f8d7da;
  color: #721c24;
}
