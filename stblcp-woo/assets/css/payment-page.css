/* StablecoinPay Payment Page Styles - Optimized & Responsive */

:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --background: #f8fafc;
  --card-background: #ffffff;
  --border-color: #e5e7eb;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
  --transition: all 0.2s ease;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body.stablecoinpay-payment-page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif;
  background: var(--background);
  color: var(--text-primary);
  line-height: 1.5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-size: 14px;
}

/* Container - Mobile First */
.payment-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .payment-container {
    padding: 20px;
  }
}

/* Header - Responsive & Compact */
.payment-header {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 10px 14px;
  margin-bottom: 16px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  position: relative;
  backdrop-filter: blur(10px);
}

.payment-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--success-color)
  );
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.site-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.site-logo-link {
  display: inline-block;
  text-decoration: none;
  border-radius: 4px;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.site-logo-link::after {
  content: "";
  position: absolute;
  inset: -2px;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(37, 99, 235, 0.1),
    transparent
  );
  border-radius: 6px;
  opacity: 0;
  transition: var(--transition);
}

.site-logo-link:hover::after {
  opacity: 1;
  transform: scale(1.1);
}

.site-logo,
.site-logo-fallback {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  flex-shrink: 0;
  transition: var(--transition);
}

.site-logo {
  object-fit: contain;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.site-logo-link:hover .site-logo {
  transform: scale(1.05);
}

.site-logo-fallback {
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.site-logo-link:hover .site-logo-fallback {
  background: var(--primary-hover);
  transform: scale(1.05);
}

.site-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-summary {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.order-number {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
}

.order-total {
  font-size: 16px;
  font-weight: 700;
  color: var(--text-primary);
}

@media (min-width: 768px) {
  .payment-header {
    padding: 16px 20px;
    margin-bottom: 24px;
  }

  .header-content {
    gap: 16px;
    flex-wrap: nowrap;
  }

  .site-info {
    gap: 10px;
  }

  .site-logo,
  .site-logo-fallback {
    width: 100px;
    height: 24px;
    border-radius: 6px;
  }

  .site-logo-fallback {
    font-size: 14px;
  }

  .site-name {
    font-size: 18px;
  }

  .order-number {
    font-size: 14px;
  }

  .order-total {
    font-size: 18px;
  }
}

/* Main Content */
.payment-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

@media (min-width: 768px) {
  .payment-main {
    gap: 24px;
  }
}

/* Token Selector - Compact */
.token-selector-section {
  margin-bottom: 12px;
}

.token-selector-card {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 12px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.token-selector-card h3 {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.current-payment-info {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: var(--border-radius);
  padding: 6px 10px;
  margin-bottom: 12px;
  font-size: 12px;
}

.current-label {
  color: #0369a1;
  margin-right: 4px;
  font-weight: 500;
}

.current-details {
  color: #0c4a6e;
}

/* External Payment Method Selector - Mobile First */
.payment-method-selector-external {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  transition: var(--transition);
  margin-bottom: 4px;
  position: relative;
  z-index: 10;
  flex-wrap: wrap;
}

.payment-method-selector-external label {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 6px;
}

@media (min-width: 768px) {
  .token-selector-section {
    margin-bottom: 16px;
  }

  .token-selector-card {
    padding: 16px;
  }

  .token-selector-card h3 {
    font-size: 16px;
    margin-bottom: 12px;
  }

  .current-payment-info {
    padding: 8px 12px;
    margin-bottom: 16px;
    font-size: 13px;
  }

  .payment-method-selector-external {
    justify-content: flex-end;
    gap: 16px;
    margin-bottom: 4px;
    flex-wrap: nowrap;
  }

  .payment-method-selector-external label {
    font-size: 14px;
    gap: 8px;
  }
}

/* Payment Card Header */
.payment-card-header {
  margin-bottom: 16px;
}

.payment-card-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

@media (min-width: 768px) {
  .payment-card-header {
    margin-bottom: 24px;
  }

  .payment-card-header h3 {
    font-size: 20px;
  }
}

/* Custom Dropdown - Mobile First */
.custom-select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  flex: 1;
  z-index: 10;
}

.custom-dropdown {
  position: relative;
  width: 100%;
}

.dropdown-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--card-background);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-primary);
  width: 100%;
  box-shadow: var(--shadow);
  min-height: 48px;
}

.dropdown-button:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
}

.dropdown-button:focus,
.dropdown-button.active {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.dropdown-selected {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.dropdown-selected-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  flex-shrink: 0;
}

.dropdown-selected-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1px;
}

.dropdown-selected-token {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.2;
}

.dropdown-selected-network {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 400;
  line-height: 1.2;
}

.dropdown-arrow {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
  color: var(--text-secondary);
  flex-shrink: 0;
}

.dropdown-button.active .dropdown-arrow {
  transform: rotate(180deg);
}

@media (min-width: 768px) {
  .custom-select-wrapper {
    max-width: 320px;
  }

  .dropdown-button {
    padding: 12px 16px;
    font-size: 15px;
    min-height: 52px;
  }

  .dropdown-selected {
    gap: 12px;
  }

  .dropdown-selected-icon {
    width: 24px;
    height: 24px;
  }

  .dropdown-selected-text {
    gap: 2px;
  }

  .dropdown-selected-token {
    font-size: 15px;
  }

  .dropdown-selected-network {
    font-size: 13px;
  }

  .dropdown-arrow {
    width: 20px;
    height: 20px;
  }
}

/* Dropdown Menu - Responsive */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: 99999;
  max-height: 240px;
  overflow-y: auto;
  margin-top: 2px;
  display: none;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-4px);
  transition: var(--transition);
}

.dropdown-menu.active {
  display: block;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-group {
  padding: 4px 0;
}

.dropdown-group:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
}

.dropdown-group-label {
  padding: 6px 12px 2px;
  font-size: 11px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dropdown-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  cursor: pointer;
  transition: var(--transition);
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  font-family: inherit;
  font-size: inherit;
}

.dropdown-option:hover,
.dropdown-option:focus {
  background: rgba(37, 99, 235, 0.08);
  outline: none;
}

.dropdown-option.selected {
  background: rgba(37, 99, 235, 0.12);
  color: var(--primary-color);
}

.dropdown-option-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  flex-shrink: 0;
}

.dropdown-option-text {
  display: flex;
  flex-direction: column;
  gap: 1px;
  flex: 1;
}

.dropdown-option-token {
  font-weight: 600;
  font-size: 13px;
  color: var(--text-primary);
  line-height: 1.2;
}

.dropdown-option-network {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 400;
  line-height: 1.2;
}

.dropdown-option.selected .dropdown-option-token {
  color: var(--primary-color);
}

@media (min-width: 768px) {
  .dropdown-menu {
    max-height: 300px;
    margin-top: 4px;
    transform: translateY(-8px);
  }

  .dropdown-group {
    padding: 8px 0;
  }

  .dropdown-group-label {
    padding: 8px 16px 4px;
    font-size: 12px;
  }

  .dropdown-option {
    gap: 12px;
    padding: 12px 16px;
  }

  .dropdown-option-icon {
    width: 24px;
    height: 24px;
  }

  .dropdown-option-text {
    gap: 2px;
  }

  .dropdown-option-token {
    font-size: 14px;
  }

  .dropdown-option-network {
    font-size: 12px;
  }
}

/* Hidden native select */
.payment-method-select {
  display: none;
}

/* Update Button - Compact */
.update-payment-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  min-width: 120px;
  position: relative;
}

.update-payment-btn:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}

.update-payment-btn:disabled {
  background: #d1d5db;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.update-payment-btn .btn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.update-payment-btn .spinner {
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@media (min-width: 768px) {
  .update-payment-btn {
    padding: 10px 20px;
    font-size: 14px;
    min-width: 140px;
  }

  .update-payment-btn .spinner {
    width: 14px;
    height: 14px;
  }
}

/* Switching Indicator - Compact */
.switching-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: var(--border-radius);
  color: #0369a1;
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 12px;
}

.token-selector-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: var(--text-secondary);
  font-size: 13px;
}

/* Payment Status - Mobile First */
.status-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-radius: var(--border-radius-lg);
  border: 1px solid;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  flex-wrap: wrap;
  gap: 12px;
}

.status-indicator::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  opacity: 0.8;
}

.status-indicator[data-status="WAITING"] {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: var(--warning-color);
  animation: gentle-pulse 3s ease-in-out infinite;
}

.status-indicator[data-status="WAITING"]::before {
  background: var(--warning-color);
}

.status-indicator[data-status="DETECTED"] {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: var(--primary-color);
  animation: gentle-pulse 2s ease-in-out infinite;
}

.status-indicator[data-status="DETECTED"]::before {
  background: var(--primary-color);
}

.status-indicator[data-status="CONFIRMED"] {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: var(--success-color);
}

.status-indicator[data-status="CONFIRMED"]::before {
  background: var(--success-color);
}

.status-indicator[data-status="EXPIRED"],
.status-indicator[data-status="CANCELED"] {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border-color: var(--error-color);
}

.status-indicator[data-status="EXPIRED"]::before,
.status-indicator[data-status="CANCELED"]::before {
  background: var(--error-color);
}

.status-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.status-icon {
  font-size: 28px;
  min-width: 40px;
  text-align: center;
  flex-shrink: 0;
}

.status-text {
  flex: 1;
  min-width: 0;
  text-align: left;
}

.status-title {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 2px;
  line-height: 1.2;
}

.status-description {
  color: var(--text-secondary);
  font-size: 13px;
  font-weight: 400;
  line-height: 1.3;
  margin-block-end: 0 !important;
}
.status-text .status-description {
  margin-block-end: 0 !important;
}

@media (min-width: 768px) {
  .switching-indicator {
    gap: 12px;
    padding: 12px 24px;
    font-size: 14px;
    margin-bottom: 16px;
  }

  .token-selector-loading {
    padding: 20px;
    font-size: 14px;
  }

  .status-indicator {
    padding: 16px 24px;
    flex-wrap: nowrap;
    gap: 20px;
  }

  .status-indicator::before {
    height: 4px;
  }

  .status-content {
    gap: 20px;
  }

  .status-icon {
    font-size: 36px;
    min-width: 56px;
  }

  .status-title {
    font-size: 20px;
    margin-bottom: 6px;
  }

  .status-description {
    font-size: 15px;
  }
}

/* Status Timer & Confirmations - Unified */
.status-timer-right,
.status-confirmations {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
  padding: 6px 10px;
  border-radius: var(--border-radius);
  min-width: 80px;
  flex-shrink: 0;
}

.status-timer-right {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-confirmations {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.timer-label,
.confirmation-label {
  font-size: 10px;
  font-weight: 500;
  line-height: 1;
}

.timer-label {
  color: #856404;
}

.confirmation-label {
  color: #1e40af;
}

.timer-value,
.confirmation-count {
  font-size: 14px;
  font-weight: bold;
  font-family: "Monaco", "Menlo", monospace;
  line-height: 1;
}

.timer-value {
  color: #856404;
}

.confirmation-count {
  color: #1e40af;
}

.timer-value.timer-expired {
  color: #dc2626;
  animation: pulse-red 1s infinite;
}

.timer-value.timer-warning {
  color: #d97706;
}

@media (min-width: 768px) {
  .status-timer-right,
  .status-confirmations {
    gap: 4px;
    padding: 8px 12px;
    min-width: 120px;
  }

  .timer-label,
  .confirmation-label {
    font-size: 12px;
  }

  .timer-value,
  .confirmation-count {
    font-size: 18px;
  }
}

/* Payment Layout - Mobile First */
.payment-layout {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: stretch;
}

@media (min-width: 768px) {
  .payment-layout {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 32px;
    align-items: start;
  }

  .qr-code-section {
    order: 0;
  }

  .payment-info-details {
    order: 0;
  }
}

/* Payment Details */
.payment-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

@media (min-width: 768px) {
  .payment-details {
    gap: 24px;
  }
}

/* Confirmation Card - Compact */
.confirmation-card {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 16px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.confirmation-header h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 6px;
  color: var(--text-primary);
}

.confirmation-header p {
  color: var(--text-secondary);
  margin-bottom: 16px;
  font-size: 13px;
}

.confirmation-progress {
  margin-bottom: 16px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 6px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-hover)
  );
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.progress-count {
  color: var(--text-primary);
  font-weight: 500;
}

.progress-percentage {
  color: var(--text-secondary);
  font-weight: 600;
}

.transaction-info {
  border-top: 1px solid var(--border-color);
  padding-top: 12px;
}

.transaction-info label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 6px;
}

.tx-hash {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: var(--background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.tx-value {
  flex: 1;
  font-family: "Monaco", "Menlo", monospace;
  font-size: 11px;
  color: var(--text-primary);
  word-break: break-all;
}

@media (min-width: 768px) {
  .confirmation-card {
    padding: 24px;
  }

  .confirmation-header h3 {
    font-size: 20px;
    margin-bottom: 8px;
  }

  .confirmation-header p {
    margin-bottom: 20px;
    font-size: 14px;
  }

  .confirmation-progress {
    margin-bottom: 20px;
  }

  .progress-bar {
    height: 12px;
    border-radius: 6px;
    margin-bottom: 8px;
  }

  .progress-fill {
    border-radius: 6px;
  }

  .progress-text {
    font-size: 14px;
  }

  .transaction-info {
    padding-top: 16px;
  }

  .transaction-info label {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .tx-hash {
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
  }

  .tx-value {
    font-size: 13px;
  }
}

/* Payment Details */
.payment-details {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Payment Info Card - Mobile First */
.payment-info-card {
  background: var(--card-background);
  border-radius: var(--border-radius-lg);
  padding: 16px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.payment-info-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--success-color)
  );
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.payment-info-card h3 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  text-align: center;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.info-row {
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row label {
  display: block;
  font-size: 11px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.amount-display,
.address-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background: var(--background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  min-height: 44px;
}

.amount-value,
.address-value {
  flex: 1;
  font-family: "Monaco", "Menlo", monospace;
  font-size: 13px;
  font-weight: 600;
  word-break: break-all;
  line-height: 1.3;
}

.amount-token {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.amount-network {
  color: var(--text-secondary);
  font-size: 8px;
  margin-left: 6px;
  font-style: italic;
  opacity: 0.8;
}

@media (min-width: 768px) {
  .payment-info-card {
    padding: 24px;
  }

  .payment-info-card::before {
    height: 4px;
  }

  .payment-info-card h3 {
    font-size: 18px;
    margin-bottom: 20px;
    gap: 12px;
  }

  .info-row label {
    font-size: 12px;
  }

  .amount-display,
  .address-display {
    gap: 12px;
    padding: 12px;
  }

  .amount-value,
  .address-value {
    font-size: 15px;
  }

  .amount-token {
    font-size: 13px;
  }

  .amount-network {
    font-size: 12px;
    margin-left: 8px;
  }
}

@media (min-width: 1024px) {
  .payment-info-card {
    padding: 32px;
  }

  .payment-info-card h3 {
    font-size: 20px;
    margin-bottom: 28px;
  }

  .amount-value,
  .address-value {
    font-size: 16px;
  }

  .amount-token {
    font-size: 14px;
  }

  .amount-network {
    font-size: 13px;
  }
}

/* Copy Icon - Simplified */
.copy-icon {
  cursor: pointer;
  transition: var(--transition);
  opacity: 0.7;
  position: relative;
  flex-shrink: 0;
  width: 18px;
  height: 18px;
}

.copy-icon:hover {
  opacity: 1;
  transform: scale(1.1);
}

.copy-icon:active {
  transform: scale(0.95);
}

.copy-icon.copied {
  animation: copySuccess 0.4s ease;
}

@media (min-width: 768px) {
  .copy-icon {
    width: 20px;
    height: 20px;
  }
}

/* QR Code Section - Mobile First */
.qr-code-section {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 16px;
}

.qr-code {
  display: inline-block;
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  margin-bottom: 8px;
  padding: 6px;
  border: 2px solid var(--border-color);
  transition: var(--transition);
}

.qr-code:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-lg);
}

.qr-code img {
  max-width: 160px;
  width: 100%;
  height: auto;
  display: block;
  border-radius: var(--border-radius);
}

.qr-code-placeholder {
  padding: 24px;
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius-lg);
  background: var(--background);
  margin-bottom: 16px;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 13px;
}

.qr-description {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 400;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

@media (min-width: 768px) {
  .qr-code-section {
    min-height: 240px;
    padding: 20px;
  }

  .qr-code {
    padding: 8px;
    border: 3px solid var(--border-color);
  }

  .qr-code img {
    max-width: 200px;
  }

  .qr-code-placeholder {
    padding: 32px;
    margin-bottom: 20px;
    font-size: 14px;
  }

  .qr-description {
    font-size: 12px;
    gap: 8px;
  }
}

@media (min-width: 1024px) {
  .qr-code-section {
    min-height: 280px;
  }

  .qr-code img {
    max-width: 220px;
  }
}

/* Warning Box - Compact */
.warning-box {
  font-size: 11px;
  font-weight: 500;
  color: #dc2626;
  margin-bottom: 8px;
  margin-top: -8px;
}

@media (min-width: 768px) {
  .warning-box {
    font-size: 12px;
  }
}

/* Payment Instructions Accordion - Mobile First */
.payment-instructions-accordion {
  margin-top: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.accordion-toggle {
  width: 100%;
  background: #f8fafc;
  border: none;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  text-align: left;
}

.accordion-toggle:hover {
  background: #f1f5f9;
}

.accordion-toggle[aria-expanded="true"] {
  background: #e2e8f0;
}

.accordion-title {
  flex: 1;
}

.accordion-icon {
  font-size: 10px;
  transition: transform 0.2s ease;
  color: var(--text-secondary);
  flex-shrink: 0;
}

.accordion-toggle[aria-expanded="true"] .accordion-icon {
  transform: rotate(180deg);
}

.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.accordion-toggle[aria-expanded="true"] + .accordion-content {
  max-height: 400px;
}

.instructions-content {
  padding: 16px;
  background: var(--card-background);
  font-size: 13px;
}

.instructions-content ol {
  margin-bottom: 12px;
  padding-left: 16px;
}

.instructions-content li {
  margin-bottom: 10px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.instructions-content li strong {
  color: var(--text-primary);
}

.instruction-address {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: var(--border-radius);
  padding: 6px 8px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 11px;
  color: var(--text-primary);
  word-break: break-all;
  display: block;
  margin-top: 4px;
  max-width: 100%;
}

@media (min-width: 768px) {
  .payment-instructions-accordion {
    margin-top: 20px;
  }

  .accordion-toggle {
    padding: 8px 16px;
    font-size: 16px;
  }

  .accordion-icon {
    font-size: 12px;
  }

  .accordion-toggle[aria-expanded="true"] + .accordion-content {
    max-height: 500px;
  }

  .instructions-content {
    padding: 20px;
    font-size: 14px;
  }

  .instructions-content ol {
    margin-bottom: 16px;
    padding-left: 20px;
  }

  .instructions-content li {
    margin-bottom: 12px;
    line-height: 1.5;
  }

  .instruction-address {
    font-size: 12px;
  }
}

/* Buttons - Mobile First */
.action-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16px;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  background: var(--primary-color);
  color: white;
  text-decoration: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  font-size: 14px;
  transition: var(--transition);
  border: none;
  cursor: pointer;
  min-width: 140px;
}

.action-btn:hover {
  background: var(--primary-hover);
  color: white;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}

.action-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.btn-secondary {
  background: var(--card-background);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: 8px 16px;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: var(--transition);
  text-decoration: none;
  display: inline-block;
}

.btn-secondary:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* Footer - Compact */
.payment-footer {
  margin-top: auto;
  padding-top: 16px;
}

.payment-footer.hidden {
  display: none;
}

.footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.cancel-button-container {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.cancel-button-container.hidden {
  display: none;
}

.powered-by {
  font-size: 11px;
  color: var(--text-secondary);
}

@media (min-width: 768px) {
  .action-button-container {
    margin-top: 20px;
  }

  .action-btn {
    padding: 12px 24px;
    font-size: 16px;
    min-width: 160px;
  }

  .btn-secondary {
    padding: 10px 20px;
    font-size: 14px;
  }

  .payment-footer {
    padding-top: 24px;
  }

  .footer-content {
    gap: 16px;
  }

  .cancel-button-container {
    margin-top: 24px;
    padding-top: 20px;
    gap: 16px;
  }

  .powered-by {
    font-size: 12px;
  }
}

/* Loading States - Optimized */
.loading-state {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Unified Spinner */
.spinner {
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-state .spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  margin-bottom: 12px;
}

.switching-indicator .spinner {
  width: 14px;
  height: 14px;
  border: 2px solid #0ea5e9;
  border-top: 2px solid transparent;
}

.token-selector-loading .spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid var(--primary-color);
  margin-right: 8px;
}

/* Messages - Mobile First */
.copy-success,
.error-message {
  position: fixed;
  top: 16px;
  right: 16px;
  left: 16px;
  padding: 10px 16px;
  border-radius: var(--border-radius);
  font-size: 13px;
  font-weight: 500;
  z-index: 1000;
  animation: slideIn 0.3s ease;
  text-align: center;
}

.copy-success {
  background: var(--success-color);
  color: white;
}

.error-message {
  background: var(--error-color);
  color: white;
}

@media (min-width: 768px) {
  .loading-state .spinner {
    width: 40px;
    height: 40px;
    border-width: 4px;
    margin-bottom: 16px;
  }

  .switching-indicator .spinner {
    width: 16px;
    height: 16px;
  }

  .token-selector-loading .spinner {
    width: 20px;
    height: 20px;
    margin-right: 12px;
  }

  .copy-success,
  .error-message {
    top: 20px;
    right: 20px;
    left: auto;
    padding: 12px 20px;
    font-size: 14px;
    text-align: left;
    max-width: 300px;
  }
}

/* Animations - Optimized */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse-red {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes gentle-pulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: var(--shadow);
  }
  50% {
    transform: scale(1.01);
    box-shadow: var(--shadow-lg);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes copySuccess {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    filter: brightness(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Success Animation */
.payment-success .status-indicator[data-status="CONFIRMED"] {
  animation: pulse-scale 2s infinite;
}

@keyframes pulse-scale {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* Mobile-specific adjustments */
@media (max-width: 480px) {
  .header-content {
    flex-direction: row;
    text-align: center;
    gap: 8px;
  }

  .order-summary {
    justify-content: center;
    gap: 8px;
  }

  .status-indicator {
    flex-direction: row;
    gap: 12px;
    text-align: center;
  }

  .status-content {
    justify-content: center;
    flex-direction: row;
    gap: 8px;
  }

  .amount-display,
  .address-display {
    flex-direction: row;
    align-items: stretch;
    gap: 8px;
  }

  .cancel-button-container {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
}

/* Enhanced Logo Styling */
.site-logo-link {
  position: relative;
  overflow: hidden;
}

.site-logo-link::after {
  content: "";
  position: absolute;
  inset: -2px;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(37, 99, 235, 0.1),
    transparent
  );
  border-radius: 6px;
  opacity: 0;
  transition: var(--transition);
}

.site-logo-link:hover::after {
  opacity: 1;
  transform: scale(1.1);
}

/* Mobile-first ultra compact logo */
@media (max-width: 480px) {
  .site-logo,
  .site-logo-fallback {
    width: 100px;
    height: 24px;
    border-radius: 3px;
  }

  .site-logo-fallback {
    font-size: 10px;
  }

  .header-content {
    gap: 6px;
  }

  .site-info {
    gap: 6px;
  }
}

/* Logo size variants */
.site-logo.logo-minimal,
.site-logo-fallback.logo-minimal {
  width: 18px;
  height: 18px;
  border-radius: 3px;
}

.site-logo-fallback.logo-minimal {
  font-size: 9px;
}

/* High-DPI display optimization */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .site-logo {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
