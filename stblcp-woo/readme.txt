=== WooCommerce StablecoinPay Gateway ===
Contributors: stablecoinpay
Tags: woocommerce, cryptocurrency, payment, stablecoin, usdt, usdc, dai
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Accept cryptocurrency payments in your WooCommerce store using StablecoinPay. Support for USDT, USDC, DAI, PYUSD, and BUSD across multiple blockchains.

== Description ==

The WooCommerce StablecoinPay Gateway plugin allows you to accept cryptocurrency payments in your WooCommerce store using the StablecoinPay payment processor. This plugin provides a seamless integration with support for multiple stablecoins across various blockchain networks.

= Supported Cryptocurrencies =

* **USDT (Tether USD)** - Ethereum, BSC, Tron, Solana, TON
* **USDC (USD Coin)** - Ethereum, BSC, Tron, Solana, TON  
* **DAI (Dai Stablecoin)** - Ethereum, BSC
* **PYUSD (PayPal USD)** - Ethereum
* **BUSD (Binance USD)** - BSC

= Key Features =

* **Self-Hosted Payment Page** - Customers are redirected to a dedicated payment page hosted on your domain
* **Real-Time Payment Monitoring** - Automatic detection and confirmation of payments
* **Multiple Blockchain Support** - Accept payments on Ethereum, BSC, Tron, Solana, and TON networks
* **HPOS Compatible** - Full support for WooCommerce High-Performance Order Storage
* **Configurable Settings** - All API endpoints and settings manageable from WordPress admin
* **Mobile Responsive** - Optimized payment experience for all devices
* **QR Code Support** - Easy mobile wallet payments with QR codes
* **Callback Integration** - Automatic order status updates via webhooks
* **Debug Logging** - Comprehensive logging for troubleshooting
* **IP Whitelisting** - Security features for callback URLs

= How It Works =

1. Customer selects cryptocurrency payment at checkout
2. Order is created and customer is redirected to the payment page
3. Customer selects their preferred token and blockchain
4. Payment details (amount, address, QR code) are displayed
5. Customer sends payment from their wallet
6. Payment is automatically detected and confirmed
7. Order status is updated and customer is redirected to success page

= Requirements =

* WordPress 5.0 or higher
* WooCommerce 3.0 or higher
* PHP 7.4 or higher
* SSL certificate (recommended)
* StablecoinPay API account

= Getting Started =

1. Install and activate the plugin
2. Go to WooCommerce > StablecoinPay in your WordPress admin
3. Configure your API credentials and settings
4. Enable the payment method in WooCommerce > Settings > Payments
5. Test with a small transaction

== Installation ==

= Automatic Installation =

1. Log in to your WordPress admin panel
2. Go to Plugins > Add New
3. Search for "WooCommerce StablecoinPay Gateway"
4. Click "Install Now" and then "Activate"

= Manual Installation =

1. Download the plugin zip file
2. Upload it to your `/wp-content/plugins/` directory
3. Extract the files
4. Activate the plugin through the WordPress admin

= Configuration =

1. Go to **WooCommerce > StablecoinPay** in your WordPress admin
2. Enter your StablecoinPay API credentials:
   - API URL (default: http://localhost:3000/api/v1)
   - API Key
   - API Secret
3. Configure payment settings:
   - Default token (USDT, USDC, etc.)
   - Default blockchain
   - Payment expiry time
4. Go to **WooCommerce > Settings > Payments**
5. Enable "StablecoinPay" payment method
6. Configure the payment method title and description

== Frequently Asked Questions ==

= Do I need a StablecoinPay account? =

Yes, you need to sign up for a StablecoinPay API account to use this plugin. Contact StablecoinPay for API credentials.

= Which cryptocurrencies are supported? =

The plugin supports USDT, USDC, DAI, PYUSD, and BUSD across multiple blockchain networks including Ethereum, BSC, Tron, Solana, and TON.

= Is this plugin compatible with HPOS? =

Yes, this plugin is fully compatible with WooCommerce High-Performance Order Storage (HPOS).

= Can I customize the payment page? =

The payment page uses a self-hosted template that can be customized through CSS. The plugin includes responsive design and modern styling out of the box.

= How are payments confirmed? =

Payments are automatically detected and confirmed through blockchain monitoring. The plugin receives real-time updates via callback URLs.

= Is test mode available? =

Yes, the plugin includes a test mode that uses testnet for testing payments without real cryptocurrency transactions.

= What happens if a payment expires? =

If a payment is not completed within the configured time limit (default 30 minutes), the order is automatically cancelled.

= Can I export payment data? =

Yes, the plugin includes functionality to export payment data to CSV format for accounting and reporting purposes.

== Screenshots ==

1. Plugin settings page with API configuration
2. WooCommerce payment method settings
3. Checkout page with StablecoinPay option
4. Self-hosted payment page with QR code
5. Order details with payment information
6. Admin dashboard with payment statistics

== Changelog ==

= 1.0.0 =
* Initial release
* Support for USDT, USDC, DAI, PYUSD, BUSD
* Multiple blockchain support (Ethereum, BSC, Tron, Solana, TON)
* Self-hosted payment page
* Real-time payment monitoring
* HPOS compatibility
* Configurable API settings
* Mobile responsive design
* QR code support
* Callback integration
* Debug logging
* IP whitelisting

== Upgrade Notice ==

= 1.0.0 =
Initial release of the WooCommerce StablecoinPay Gateway plugin.

== Support ==

For support and documentation, please visit:
* Plugin documentation: [Coming Soon]
* StablecoinPay API documentation: [Coming Soon]
* Support forum: [Coming Soon]

== Privacy Policy ==

This plugin connects to the StablecoinPay API to process payments. Please review StablecoinPay's privacy policy for information about how payment data is handled.

The plugin stores the following data locally:
* Order payment information
* Transaction logs
* API configuration settings

No sensitive payment information (private keys, wallet seeds) is stored by this plugin.

== Credits ==

This plugin is developed for integration with the StablecoinPay payment processor. StablecoinPay is a trademark of its respective owners.
