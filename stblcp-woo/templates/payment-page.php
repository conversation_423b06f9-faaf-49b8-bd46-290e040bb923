<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="robots" content="noindex, nofollow">
    <title><?php echo esc_html($page_title); ?> - <?php echo esc_html($site_name); ?></title>
    
    <?php wp_head(); ?>
    
    <style>
        /* Ensure clean styling independent of theme */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body.stablecoinpay-payment-page {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1a202c;
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Accessibility improvements */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus indicators */
        button:focus,
        select:focus,
        a:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            body.stablecoinpay-payment-page {
                background: #ffffff;
                color: #000000;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Keyboard navigation improvements */
        .copy-btn:focus,
        .accordion-toggle:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
        }
    </style>
</head>

<body class="stablecoinpay-payment-page">
    <div class="payment-container">
        <!-- Header -->
        <div class="payment-header">
            <div class="header-content">
                <?php
                // Get site logo
                $custom_logo_id = get_theme_mod('custom_logo');
                $logo_url = $custom_logo_id ? wp_get_attachment_image_url($custom_logo_id, 'full') : '';
                ?>

                <div class="site-info">
                    <?php if ($logo_url): ?>
                        <a href="<?php echo esc_url(home_url('/')); ?>" class="site-logo-link" title="<?php echo esc_attr(sprintf(__('Go to %s homepage', 'stablecoinpay-gateway'), $site_name)); ?>">
                            <img src="<?php echo esc_url($logo_url); ?>" alt="<?php echo esc_attr($site_name); ?>" class="site-logo" loading="lazy">
                        </a>
                    <?php else: ?>
                        <a href="<?php echo esc_url(home_url('/')); ?>" class="site-logo-link" title="<?php echo esc_attr(sprintf(__('Go to %s homepage', 'stablecoinpay-gateway'), $site_name)); ?>">
                            <div class="site-logo-fallback">
                                <?php echo esc_html(substr($site_name, 0, 1)); ?>
                            </div>
                        </a>
                    <?php endif; ?>
                    <!-- <span class="site-name"><?php echo esc_html($site_name); ?></span> -->
                </div>

                <div class="order-summary">
                    <span class="order-number">
                        <?php printf(__('Order #%s', 'stablecoinpay-gateway'), $order->get_order_number()); ?>
                    </span>
                    <span class="order-total">
                        <?php echo wc_price($payment_info['order_total'], array('currency' => $payment_info['order_currency'])); ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="payment-main">
            <!-- Payment Status -->
            <div class="status-indicator" data-status="<?php echo esc_attr($payment_info['status']); ?>" role="status" aria-live="polite" aria-label="<?php _e('Payment status indicator', 'stablecoinpay-gateway'); ?>">
                <div class="status-content">
                    <div class="status-icon" aria-hidden="true">
                        <span class="status-emoji">⏳</span>
                    </div>
                    <div class="status-text">
                        <h3 class="status-title" id="payment-status-title"><?php _e('Waiting for Payment', 'stablecoinpay-gateway'); ?></h3>
                        <p class="status-description" id="payment-status-description"><?php _e('Send the exact amount to the address below', 'stablecoinpay-gateway'); ?></p>
                    </div>
                </div>
                <?php if (!empty($payment_info['expires_at'])): ?>
                <div class="status-timer-right" role="timer" aria-live="polite" aria-label="<?php _e('Payment countdown timer', 'stablecoinpay-gateway'); ?>">
                    <span class="timer-label"><?php _e('Time remaining:', 'stablecoinpay-gateway'); ?></span>
                    <span class="timer-value" id="countdown-timer" aria-label="<?php _e('Time remaining for payment', 'stablecoinpay-gateway'); ?>">--:--</span>
                </div>
                <?php endif; ?>
            </div>

            <!-- Dynamic Action Button (for expired/completed payments) -->
            <div class="payment-action-area" id="payment-action-area" style="display: none;">
                <div class="action-button-container">
                    <a href="#" id="payment-action-btn" class="btn-primary action-btn">
                        <span class="btn-text"><?php _e('Back to Shop', 'stablecoinpay-gateway'); ?></span>
                    </a>
                </div>
            </div>

            <!-- Payment Method Selector (Outside Card) -->
            <div class="payment-method-selector-external" role="region" aria-label="<?php _e('Payment method selection', 'stablecoinpay-gateway'); ?>">
                <label for="payment-method-dropdown" id="payment-method-label"><?php _e('Switch token/coin', 'stablecoinpay-gateway'); ?></label>
                <div class="custom-select-wrapper">
                    <!-- Custom Dropdown -->
                    <?php
                    // Get enabled methods first, before using them
                    $enabled_methods = StablecoinPay_Settings::get_enabled_payment_methods();
                    $current_method_key = '';
                    $stablecoins = array();
                    $native_coins = array();

                    // Helper function to get current token label
                    function get_current_token_label($payment_info, $enabled_methods) {
                        foreach ($enabled_methods as $method_key => $method) {
                            if ($method['token'] === $payment_info['token'] && $method['blockchain'] === $payment_info['blockchain']) {
                                return $method['label'];
                            }
                        }
                        return $payment_info['token']; // fallback
                    }

                    // Helper function to get display string for amount row
                    function get_amount_display_label($payment_info, $enabled_methods) {
                        $stablecoins = array('USDT', 'USDC', 'DAI', 'PYUSD', 'BUSD');
                        $token = $payment_info['token'];
                        $blockchain = $payment_info['blockchain'];
                        $network = $payment_info['network'];
                        $label = get_current_token_label($payment_info, $enabled_methods);
                        $is_stablecoin = in_array($token, $stablecoins);
                        $is_testnet = (stripos($network, 'test') !== false || strtolower($network) !== 'mainnet');
                        if ($is_stablecoin) {
                            $result = $label . ' on ' . $blockchain;
                        } else {
                            $result = $token . ' on ' . $blockchain;
                        }
                        if ($is_testnet) {
                            $result .= ' (Testnet)';
                        }
                        return $result;
                    }

                    $current_token_label = get_current_token_label($payment_info, $enabled_methods);
                    ?>
                    <div class="custom-dropdown" id="payment-method-dropdown" role="combobox" aria-labelledby="payment-method-label" aria-describedby="payment-method-help" aria-expanded="false">
                        <button type="button" class="dropdown-button" aria-haspopup="listbox">
                            <div class="dropdown-selected">
                                <img class="dropdown-selected-icon" src="<?php echo STABLECOINPAY_PLUGIN_URL . 'assets/icons/' . strtolower($payment_info['token']) . '.svg'; ?>" alt="<?php echo esc_attr($payment_info['token']); ?>">
                                <div class="dropdown-selected-text">
                                    <span class="dropdown-selected-token"><?php echo esc_html($current_token_label); ?></span>
                                    <span class="dropdown-selected-network"><?php echo esc_html($payment_info['blockchain']); ?> Network</span>
                                </div>
                            </div>
                            <svg class="dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <div class="dropdown-menu" role="listbox">
                            <?php
                            // Find current method and organize options
                            foreach ($enabled_methods as $method_key => $method) {
                                $icon_url = STABLECOINPAY_PLUGIN_URL . 'assets/icons/' . strtolower($method['token']) . '.svg';
                                $option_data = array(
                                    'value' => $method_key,
                                    'token' => $method['token'],
                                    'blockchain' => $method['blockchain'],
                                    'icon' => $icon_url,
                                    'label' => $method['label'],
                                    'network_label' => $method['network_label']
                                );

                                // Check if this is the current method
                                if ($method['token'] === $payment_info['token'] && $method['blockchain'] === $payment_info['blockchain']) {
                                    $current_method_key = $method_key;
                                }

                                // Organize by type
                                if (in_array($method['token'], array('USDT', 'USDC', 'DAI', 'PYUSD', 'BUSD'))) {
                                    $stablecoins[] = $option_data;
                                } else {
                                    $native_coins[] = $option_data;
                                }
                            }

                            // Output stablecoins group
                            if (!empty($stablecoins)) {
                                echo '<div class="dropdown-group">';
                                echo '<div class="dropdown-group-label">' . esc_html__('Stablecoins', 'stablecoinpay-gateway') . '</div>';
                                foreach ($stablecoins as $option) {
                                    $selected_class = ($option['value'] === $current_method_key) ? ' selected' : '';
                                    echo '<button type="button" class="dropdown-option' . $selected_class . '" data-value="' . esc_attr($option['value']) . '" data-token="' . esc_attr($option['token']) . '" data-blockchain="' . esc_attr($option['blockchain']) . '" data-label="' . esc_attr($option['label']) . '" role="option" tabindex="0">';
                                    echo '<img class="dropdown-option-icon" src="' . esc_url($option['icon']) . '" alt="' . esc_attr($option['token']) . '">';
                                    echo '<div class="dropdown-option-text">';
                                    echo '<span class="dropdown-option-token">' . esc_html($option['label']) . '</span>';
                                    echo '<span class="dropdown-option-network">' . esc_html($option['blockchain']) . ' Network</span>';
                                    echo '</div>';
                                    echo '</button>';
                                }
                                echo '</div>';
                            }

                            // Output native coins group
                            if (!empty($native_coins)) {
                                echo '<div class="dropdown-group">';
                                echo '<div class="dropdown-group-label">' . esc_html__('Native Coins', 'stablecoinpay-gateway') . '</div>';
                                foreach ($native_coins as $option) {
                                    $selected_class = ($option['value'] === $current_method_key) ? ' selected' : '';
                                    echo '<button type="button" class="dropdown-option' . $selected_class . '" data-value="' . esc_attr($option['value']) . '" data-token="' . esc_attr($option['token']) . '" data-blockchain="' . esc_attr($option['blockchain']) . '" data-label="' . esc_attr($option['label']) . '" role="option" tabindex="0">';
                                    echo '<img class="dropdown-option-icon" src="' . esc_url($option['icon']) . '" alt="' . esc_attr($option['token']) . '">';
                                    echo '<div class="dropdown-option-text">';
                                    echo '<span class="dropdown-option-token">' . esc_html($option['label']) . '</span>';
                                    echo '<span class="dropdown-option-network">' . esc_html($option['blockchain']) . ' Network</span>';
                                    echo '</div>';
                                    echo '</button>';
                                }
                                echo '</div>';
                            }
                            ?>
                        </div>
                    </div>

                    <!-- Hidden native select for form submission -->
                    <select id="payment-method-select" class="payment-method-select" aria-hidden="true" tabindex="-1">
                        <?php
                        // Output all options for the hidden select
                        foreach (array_merge($stablecoins, $native_coins) as $option) {
                            $selected = ($option['value'] === $current_method_key) ? 'selected' : '';
                            echo '<option value="' . esc_attr($option['value']) . '" data-token="' . esc_attr($option['token']) . '" data-blockchain="' . esc_attr($option['blockchain']) . '" data-label="' . esc_attr($option['label']) . '" ' . $selected . '>';
                            echo esc_html($option['label'] . ' - ' . $option['network_label']);
                            echo '</option>';
                        }
                        ?>
                    </select>
                </div>
                <div id="payment-method-help" class="sr-only"><?php _e('Select a cryptocurrency and blockchain network for your payment', 'stablecoinpay-gateway'); ?></div>
            </div>

            <!-- Payment Details -->
            <div class="payment-details">
                <div class="payment-info-card">
                    <div class="payment-card-header">
                        <h3><?php _e('Payment Information', 'stablecoinpay-gateway'); ?></h3>
                    </div>

                    <!-- Switching Indicator -->
                    <div class="selector-actions" id="update-actions" style="display: none;">
                        <div class="switching-indicator">
                            <span class="spinner"></span>
                            <span class="switching-text"><?php _e('Switching payment method...', 'stablecoinpay-gateway'); ?></span>
                        </div>
                    </div>

                    <div class="payment-layout">
                        <!-- QR Code Section (Left Side) -->
                        <div class="qr-code-section">
                            <?php if (!empty($payment_info['qr_code'])): ?>
                            <div class="qr-code">
                                <img src="<?php echo esc_attr($payment_info['qr_code']); ?>" alt="<?php _e('Payment QR Code', 'stablecoinpay-gateway'); ?>">
                            </div>
                            <p class="qr-description"><?php _e('Scan with your wallet app', 'stablecoinpay-gateway'); ?></p>
                            <?php endif; ?>
                        </div>

                        <!-- Payment Info Section (Right Side) -->
                        <div class="payment-info-details">
                            <!-- Amount with Network -->
                            <div class="info-row">
                                <label><?php _e('Amount to Send', 'stablecoinpay-gateway'); ?></label>
                                <div class="amount-display">
                                    <span class="amount-value"><?php echo esc_html($payment_info['amount']); ?></span>
                                    <span class="amount-label">
                                        <?php echo esc_html(get_amount_display_label($payment_info, $enabled_methods)); ?>
                                    </span>
                                    <svg class="copy-icon" data-copy="<?php echo esc_attr($payment_info['amount']); ?>" aria-label="<?php printf(__('Copy amount %s to clipboard', 'stablecoinpay-gateway'), esc_attr($payment_info['amount'])); ?>" title="<?php _e('Copy amount', 'stablecoinpay-gateway'); ?>" width="20" height="20" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <g transform="translate(-240.000000, 0.000000)" fill-rule="nonzero">
                                                <g transform="translate(240.000000, 0.000000)">
                                                    <path d="M24,0 L24,24 L0,24 L0,0 L24,0 Z M12.5934901,23.257841 L12.5819402,23.2595131 L12.5108777,23.2950439 L12.4918791,23.2987469 L12.4918791,23.2987469 L12.4767152,23.2950439 L12.4056548,23.2595131 C12.3958229,23.2563662 12.3870493,23.2590235 12.3821421,23.2649074 L12.3780323,23.275831 L12.360941,23.7031097 L12.3658947,23.7234994 L12.3769048,23.7357139 L12.4804777,23.8096931 L12.4953491,23.8136134 L12.4953491,23.8136134 L12.5071152,23.8096931 L12.6106902,23.7357139 L12.6232938,23.7196733 L12.6232938,23.7196733 L12.6266527,23.7031097 L12.609561,23.275831 C12.6075724,23.2657013 12.6010112,23.2592993 12.5934901,23.257841 L12.5934901,23.257841 Z M12.8583906,23.1452862 L12.8445485,23.1473072 L12.6598443,23.2396597 L12.6498822,23.2499052 L12.6498822,23.2499052 L12.6471943,23.2611114 L12.6650943,23.6906389 L12.6699349,23.7034178 L12.6699349,23.7034178 L12.678386,23.7104931 L12.8793402,23.8032389 C12.8914285,23.8068999 12.9022333,23.8029875 12.9078286,23.7952264 L12.9118235,23.7811639 L12.8776777,23.1665331 C12.8752882,23.1545897 12.8674102,23.1470016 12.8583906,23.1452862 L12.8583906,23.1452862 Z M12.1430473,23.1473072 C12.1332178,23.1423925 12.1221763,23.1452606 12.1156365,23.1525954 L12.1099173,23.1665331 L12.0757714,23.7811639 C12.0751323,23.7926639 12.0828099,23.8018602 12.0926481,23.8045676 L12.108256,23.8032389 L12.3092106,23.7104931 L12.3186497,23.7024347 L12.3186497,23.7024347 L12.3225043,23.6906389 L12.340401,23.2611114 L12.337245,23.2485176 L12.337245,23.2485176 L12.3277531,23.2396597 L12.1430473,23.1473072 Z" fill-rule="nonzero" fill="transparent"></path>
                                                    <path d="M19,2 C20.1046,2 21,2.89543 21,4 L21,16 C21,17.1046 20.1046,18 19,18 L17,18 L17,20 C17,21.1046 16.1046,22 15,22 L5,22 C3.89543,22 3,21.1046 3,20 L3,8 C3,6.89543 3.89543,6 5,6 L7,6 L7,4 C7,2.89543 7.89543,2 9,2 L19,2 Z M15,8 L5,8 L5,20 L15,20 L15,8 Z M10,15 C10.5523,15 11,15.4477 11,16 C11,16.5523 10.5523,17 10,17 L8,17 C7.44772,17 7,16.5523 7,16 C7,15.4477 7.44772,15 8,15 L10,15 Z M19,4 L9,4 L9,6 L15,6 C16.1046,6 17,6.89543 17,8 L17,16 L19,16 L19,4 Z M12,11 C12.5523,11 13,11.4477 13,12 C13,12.51285 12.613973,12.9355092 12.1166239,12.9932725 L12,13 L8,13 C7.44772,13 7,12.5523 7,12 C7,11.48715 7.38604429,11.0644908 7.88337975,11.0067275 L8,11 L12,11 Z" fill="#2563eb"></path>
                                                </g>
                                            </g>
                                        </g>
                                    </svg>
                                </div>
                            </div>

                            <!-- Important Notice -->
                            <div class="warning-box">
                                <strong><?php _e('Important:', 'stablecoinpay-gateway'); ?></strong>
                                <?php _e('Send the exact amount, otherwise payment may fail.', 'stablecoinpay-gateway'); ?>
                            </div>

                            <!-- Wallet Address -->
                            <div class="info-row">
                                <label><?php _e('Send to Address', 'stablecoinpay-gateway'); ?></label>
                                <div class="address-display">
                                    <span class="address-value"><?php echo esc_html($payment_info['walletAddress']); ?></span>
                                    <svg class="copy-icon" data-copy="<?php echo esc_attr($payment_info['walletAddress']); ?>" aria-label="<?php _e('Copy wallet address to clipboard', 'stablecoinpay-gateway'); ?>" title="<?php _e('Copy address', 'stablecoinpay-gateway'); ?>" width="20" height="20" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <g transform="translate(-240.000000, 0.000000)" fill-rule="nonzero">
                                                <g transform="translate(240.000000, 0.000000)">
                                                    <path d="M24,0 L24,24 L0,24 L0,0 L24,0 Z M12.5934901,23.257841 L12.5819402,23.2595131 L12.5108777,23.2950439 L12.4918791,23.2987469 L12.4918791,23.2987469 L12.4767152,23.2950439 L12.4056548,23.2595131 C12.3958229,23.2563662 12.3870493,23.2590235 12.3821421,23.2649074 L12.3780323,23.275831 L12.360941,23.7031097 L12.3658947,23.7234994 L12.3769048,23.7357139 L12.4804777,23.8096931 L12.4953491,23.8136134 L12.4953491,23.8136134 L12.5071152,23.8096931 L12.6106902,23.7357139 L12.6232938,23.7196733 L12.6232938,23.7196733 L12.6266527,23.7031097 L12.609561,23.275831 C12.6075724,23.2657013 12.6010112,23.2592993 12.5934901,23.257841 L12.5934901,23.257841 Z M12.8583906,23.1452862 L12.8445485,23.1473072 L12.6598443,23.2396597 L12.6498822,23.2499052 L12.6498822,23.2499052 L12.6471943,23.2611114 L12.6650943,23.6906389 L12.6699349,23.7034178 L12.6699349,23.7034178 L12.678386,23.7104931 L12.8793402,23.8032389 C12.8914285,23.8068999 12.9022333,23.8029875 12.9078286,23.7952264 L12.9118235,23.7811639 L12.8776777,23.1665331 C12.8752882,23.1545897 12.8674102,23.1470016 12.8583906,23.1452862 L12.8583906,23.1452862 Z M12.1430473,23.1473072 C12.1332178,23.1423925 12.1221763,23.1452606 12.1156365,23.1525954 L12.1099173,23.1665331 L12.0757714,23.7811639 C12.0751323,23.7926639 12.0828099,23.8018602 12.0926481,23.8045676 L12.108256,23.8032389 L12.3092106,23.7104931 L12.3186497,23.7024347 L12.3186497,23.7024347 L12.3225043,23.6906389 L12.340401,23.2611114 L12.337245,23.2485176 L12.337245,23.2485176 L12.3277531,23.2396597 L12.1430473,23.1473072 Z" fill-rule="nonzero" fill="transparent"></path>
                                                    <path d="M19,2 C20.1046,2 21,2.89543 21,4 L21,16 C21,17.1046 20.1046,18 19,18 L17,18 L17,20 C17,21.1046 16.1046,22 15,22 L5,22 C3.89543,22 3,21.1046 3,20 L3,8 C3,6.89543 3.89543,6 5,6 L7,6 L7,4 C7,2.89543 7.89543,2 9,2 L19,2 Z M15,8 L5,8 L5,20 L15,20 L15,8 Z M10,15 C10.5523,15 11,15.4477 11,16 C11,16.5523 10.5523,17 10,17 L8,17 C7.44772,17 7,16.5523 7,16 C7,15.4477 7.44772,15 8,15 L10,15 Z M19,4 L9,4 L9,6 L15,6 C16.1046,6 17,6.89543 17,8 L17,16 L19,16 L19,4 Z M12,11 C12.5523,11 13,11.4477 13,12 C13,12.51285 12.613973,12.9355092 12.1166239,12.9932725 L12,13 L8,13 C7.44772,13 7,12.5523 7,12 C7,11.48715 7.38604429,11.0644908 7.88337975,11.0067275 L8,11 L12,11 Z" fill="#2563eb"></path>
                                                </g>
                                            </g>
                                        </g>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Instructions Accordion -->
                    <div class="payment-instructions-accordion">
                        <button class="accordion-toggle" type="button" aria-expanded="false">
                            <span class="accordion-title"><?php _e('How to Pay', 'stablecoinpay-gateway'); ?></span>
                            <span class="accordion-icon">▼</span>
                        </button>
                        <div class="accordion-content">
                            <div class="instructions-content">
                                <ol>
                                    <li><?php _e('Open your crypto wallet and select', 'stablecoinpay-gateway'); ?> <strong><?php echo esc_html($current_token_label); ?></strong> <?php _e('on', 'stablecoinpay-gateway'); ?> <strong><?php echo esc_html($payment_info['blockchain']); ?></strong> <?php _e('network', 'stablecoinpay-gateway'); ?></li>
                                    <li><?php printf(__('Send exactly <strong>%s %s</strong> to address:', 'stablecoinpay-gateway'), esc_html($payment_info['amount']), esc_html($current_token_label)); ?><br><code class="instruction-address"><?php echo esc_html($payment_info['walletAddress']); ?></code></li>
                                    <li><?php _e('Verify amount, address and network before submitting', 'stablecoinpay-gateway'); ?></li>
                                    <li><?php _e('Wait for confirmation (usually 1-5 minutes)', 'stablecoinpay-gateway'); ?></li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cancel Button Container - positioned relative to payment content -->
                <div class="cancel-button-container" id="cancel-button-container">
                    <a href="<?php echo esc_url($order->get_cancel_order_url()); ?>" class="btn-secondary">
                        <?php _e('Cancel Order', 'stablecoinpay-gateway'); ?>
                    </a>
                    <div class="powered-by">
                        <?php _e('Powered by Crypto', 'stablecoinpay-gateway'); ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->

    <!-- Loading State (hidden by default) -->
    <div class="loading-state" style="display: none;">
        <div class="spinner"></div>
        <p><?php _e('Processing payment...', 'stablecoinpay-gateway'); ?></p>
    </div>

    <!-- Success Messages -->
    <div id="copy-success" class="copy-success" style="display: none;">
        <?php _e('Copied to clipboard!', 'stablecoinpay-gateway'); ?>
    </div>

    <div id="error-message" class="error-message" style="display: none;">
        <span class="error-text"></span>
    </div>



    <script>
        // Pass payment data to JavaScript with server-side timer
        window.stablecoinpayPayment = {
            id: <?php echo wp_json_encode($payment_info['id']); ?>,
            status: <?php echo wp_json_encode($payment_info['status']); ?>,
            expiresAt: <?php echo wp_json_encode($payment_info['expires_at']); ?>,
            timeRemainingSeconds: <?php echo wp_json_encode($payment_info['time_remaining_seconds']); ?>,
            isExpired: <?php echo wp_json_encode($payment_info['is_expired']); ?>,
            serverTime: <?php echo wp_json_encode($payment_info['server_time']); ?>,
            amount: <?php echo wp_json_encode($payment_info['amount']); ?>,
            token: <?php echo wp_json_encode($payment_info['token']); ?>,
            walletAddress: <?php echo wp_json_encode($payment_info['walletAddress']); ?>,
            blockchain: <?php echo wp_json_encode($payment_info['blockchain']); ?>,
            network: <?php echo wp_json_encode($payment_info['network'] ?? 'MAINNET'); ?>,
            qrCode: <?php echo wp_json_encode($payment_info['qr_code'] ?? ''); ?>,
            orderId: <?php echo wp_json_encode($order->get_id()); ?>,
            orderTotal: <?php echo wp_json_encode($payment_info['order_total']); ?>,
            orderCurrency: <?php echo wp_json_encode($payment_info['order_currency']); ?>,
            shopUrl: <?php echo wp_json_encode(wc_get_page_permalink('shop')); ?>
        };

        // Supported tokens and blockchains
        window.stablecoinpaySupportedTokens = {
            // Stablecoins
            'USDT': {
                'name': 'Tether USD',
                'symbol': 'USDT',
                'blockchains': ['ETHEREUM', 'BSC', 'TRON', 'SOLANA', 'TON']
            },
            'USDC': {
                'name': 'USD Coin',
                'symbol': 'USDC',
                'blockchains': ['ETHEREUM', 'BSC', 'TRON', 'SOLANA', 'TON']
            },
            'DAI': {
                'name': 'Dai Stablecoin',
                'symbol': 'DAI',
                'blockchains': ['ETHEREUM', 'BSC']
            },
            'PYUSD': {
                'name': 'PayPal USD',
                'symbol': 'PYUSD',
                'blockchains': ['ETHEREUM']
            },
            'BUSD': {
                'name': 'Binance USD',
                'symbol': 'BUSD',
                'blockchains': ['BSC']
            },
            // Native Coins
            'ETH': {
                'name': 'Ethereum',
                'symbol': 'ETH',
                'blockchains': ['ETHEREUM']
            },
            'BNB': {
                'name': 'Binance Coin',
                'symbol': 'BNB',
                'blockchains': ['BSC']
            },
            'TRX': {
                'name': 'Tron',
                'symbol': 'TRX',
                'blockchains': ['TRON']
            },
            'SOL': {
                'name': 'Solana',
                'symbol': 'SOL',
                'blockchains': ['SOLANA']
            },
            'TON': {
                'name': 'Toncoin',
                'symbol': 'TON',
                'blockchains': ['TON']
            }
        };

        window.stablecoinpaySupportedBlockchains = {
            'ETHEREUM': 'Ethereum',
            'BSC': 'Binance Smart Chain',
            'TRON': 'Tron',
            'SOLANA': 'Solana',
            'TON': 'TON'
        };
    </script>

    <?php wp_footer(); ?>
</body>
</html>
