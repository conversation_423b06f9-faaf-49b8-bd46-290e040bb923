<?php
/**
 * Quick Buy Email Collection Modal Template
 * 
 * Template for email collection modal for guest users
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="stablecoinpay-email-modal" id="stablecoinpay-email-modal" style="display: none;" role="dialog" aria-labelledby="email-modal-title" aria-hidden="true">
    <div class="email-modal-overlay">
        <div class="email-modal-content">
            <div class="email-modal-header">
                <h3 id="email-modal-title"><?php _e('Information', 'stablecoinpay-gateway'); ?></h3>
                <button type="button" class="email-modal-close" aria-label="<?php _e('Close modal', 'stablecoinpay-gateway'); ?>">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            
            <div class="email-modal-body">
                <p class="email-modal-description">
                    <?php _e('Please provide your email, its required for the order', 'stablecoinpay-gateway'); ?>
                </p>
                
                <form class="email-collection-form" id="email-collection-form" novalidate>
                    <div class="form-field">
                        <label for="customer_email"><?php _e('Email Address', 'stablecoinpay-gateway'); ?> <span class="required">*</span></label>
                        <div class="email-input-wrapper">
                            <input type="email"
                                   id="customer_email"
                                   name="customer_email"
                                   placeholder="<?php _e('Enter your email address', 'stablecoinpay-gateway'); ?>"
                                   autocomplete="email"
                                   data-stablecoinpay-modal-input="true">
                            <button type="button"
                                    class="clear-saved-email"
                                    title="<?php _e('Clear saved email', 'stablecoinpay-gateway'); ?>"
                                    style="display: none;">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                            </button>
                        </div>
                        <div class="field-error" id="email-error" style="display: none;"></div>
                    </div>
                    
                    <?php if (StablecoinPay_Settings::is_quick_buy_name_required()): ?>
                    <div class="form-field">
                        <label for="customer_name"><?php _e('Full Name', 'stablecoinpay-gateway'); ?> <span class="optional"><?php _e('(optional)', 'stablecoinpay-gateway'); ?></span></label>
                        <input type="text"
                               id="customer_name"
                               name="customer_name"
                               placeholder="<?php _e('Enter your full name', 'stablecoinpay-gateway'); ?>"
                               autocomplete="name"
                               data-stablecoinpay-modal-input="true">
                    </div>
                    <?php endif; ?>
                    
                    <div class="email-modal-actions">
                        <button type="button" class="btn-cancel"><?php _e('Cancel', 'stablecoinpay-gateway'); ?></button>
                        <button type="button" class="btn-continue">
                            <span class="btn-text"><?php _e('Continue to Payment', 'stablecoinpay-gateway'); ?></span>
                            <span class="btn-spinner" style="display: none;">
                                <svg class="spinner" width="16" height="16" viewBox="0 0 24 24">
                                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="32" stroke-dashoffset="32">
                                        <animate attributeName="stroke-dashoffset" values="32;0" dur="1s" repeatCount="indefinite"/>
                                    </circle>
                                </svg>
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
