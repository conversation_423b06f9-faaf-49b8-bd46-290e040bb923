<?php
/**
 * Quick Buy Button Template
 * 
 * Template for rendering Quick Buy buttons on product and cart pages
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$button_text = StablecoinPay_Settings::get_setting('quick_buy_button_text', 'Quick Pay with');
$show_token_name = StablecoinPay_Settings::get_setting('quick_buy_show_token', 1);
?>

<div class="stablecoinpay-quick-buy-container" data-context="<?php echo esc_attr($context); ?>" <?php if (isset($data['product_id'])): ?>data-product-id="<?php echo esc_attr($data['product_id']); ?>"<?php endif; ?>>
    
    <div class="quick-buy-split-button">
        
        <button type="button" class="quick-buy-primary" data-action="quick-buy">
            <img class="quick-buy-token-icon" 
                 src="<?php echo STABLECOINPAY_PLUGIN_URL . 'assets/icons/' . strtolower($default_method['token']) . '.svg'; ?>" 
                 alt="<?php echo esc_attr($default_method['token']); ?>">
            
            <span class="quick-buy-text">
                <?php echo esc_html($button_text); ?>
                <?php if ($show_token_name): ?>
                    <span class="quick-buy-token-name"><?php echo esc_html($default_method['label']); ?></span>
                <?php endif; ?>
            </span>
        </button>
        
        <button type="button" class="quick-buy-dropdown-toggle" data-action="toggle-dropdown" aria-label="<?php _e('Select payment method', 'stablecoinpay-gateway'); ?>">
            <svg class="quick-buy-chevron" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M6 9l6 6 6-6"/>
            </svg>
        </button>
        

        <div class="quick-buy-dropdown-menu" style="display: none;">
            <?php
            // Organize methods by type
            $stablecoins = array();
            $native_coins = array();
            
            foreach ($enabled_methods as $method_key => $method) {
                if (in_array($method['token'], array('USDT', 'USDC', 'DAI', 'PYUSD', 'BUSD'))) {
                    $stablecoins[] = array_merge($method, array('key' => $method_key));
                } else {
                    $native_coins[] = array_merge($method, array('key' => $method_key));
                }
            }
            ?>
            
            <?php if (!empty($stablecoins)): ?>
                <div class="quick-buy-dropdown-group">
                    <div class="quick-buy-group-label"><?php _e('Stablecoins', 'stablecoinpay-gateway'); ?></div>
                    <?php foreach ($stablecoins as $method): ?>
                        <button type="button" 
                                class="quick-buy-dropdown-option" 
                                data-method-key="<?php echo esc_attr($method['key']); ?>"
                                data-token="<?php echo esc_attr($method['token']); ?>"
                                data-blockchain="<?php echo esc_attr($method['blockchain']); ?>"
                                data-label="<?php echo esc_attr($method['label']); ?>">
                            <img class="quick-buy-option-icon" 
                                 src="<?php echo STABLECOINPAY_PLUGIN_URL . 'assets/icons/' . strtolower($method['token']) . '.svg'; ?>" 
                                 alt="<?php echo esc_attr($method['token']); ?>">
                            <div class="quick-buy-option-text">
                                <span class="quick-buy-option-token"><?php echo esc_html($method['label']); ?></span>
                                <span class="quick-buy-option-network"><?php echo esc_html($method['network_label']); ?> Network</span>
                            </div>
                        </button>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($native_coins)): ?>
                <div class="quick-buy-dropdown-group">
                    <div class="quick-buy-group-label"><?php _e('Native Coins', 'stablecoinpay-gateway'); ?></div>
                    <?php foreach ($native_coins as $method): ?>
                        <button type="button" 
                                class="quick-buy-dropdown-option" 
                                data-method-key="<?php echo esc_attr($method['key']); ?>"
                                data-token="<?php echo esc_attr($method['token']); ?>"
                                data-blockchain="<?php echo esc_attr($method['blockchain']); ?>"
                                data-label="<?php echo esc_attr($method['label']); ?>">
                            <img class="quick-buy-option-icon" 
                                 src="<?php echo STABLECOINPAY_PLUGIN_URL . 'assets/icons/' . strtolower($method['token']) . '.svg'; ?>" 
                                 alt="<?php echo esc_attr($method['token']); ?>">
                            <div class="quick-buy-option-text">
                                <span class="quick-buy-option-token"><?php echo esc_html($method['label']); ?></span>
                                <span class="quick-buy-option-network"><?php echo esc_html($method['network_label']); ?> Network</span>
                            </div>
                        </button>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <input type="hidden" class="quick-buy-selected-method" value="<?php echo esc_attr(isset($default_method['key']) ? $default_method['key'] : ''); ?>">
    <input type="hidden" class="quick-buy-selected-token" value="<?php echo esc_attr($default_method['token']); ?>">
    <input type="hidden" class="quick-buy-selected-blockchain" value="<?php echo esc_attr($default_method['blockchain']); ?>">
</div>

<?php
if (!is_user_logged_in()):
    // BLOCK-BASED CART FIX: Use different static variable for block contexts
    $modal_key = 'modal_included';
    if (wp_doing_ajax() || (function_exists('has_block') && has_block('woocommerce/cart'))) {
        $modal_key = 'modal_included_blocks';
    }

    static $modal_states = array();
    if (!isset($modal_states[$modal_key]) || !$modal_states[$modal_key]) {
        include STABLECOINPAY_PLUGIN_PATH . 'templates/quick-buy-email-modal.php';
        $modal_states[$modal_key] = true;

        // BLOCK-BASED CART FIX: Add JavaScript to ensure modal works in block context
        if ($modal_key === 'modal_included_blocks') {
            echo '<script>
                // Ensure modal events are bound in block context
                jQuery(document).ready(function($) {
                    if (window.StablecoinPayQuickBuy) {
                        // Re-initialize modal events for blocks
                        setTimeout(function() {
                            const modal = $("#stablecoinpay-email-modal");
                            if (modal.length && !modal.data("block-events-bound")) {
                                modal.data("block-events-bound", true);
                            }
                        }, 500);
                    }
                });
            </script>';
        }
    }
endif;
?>
