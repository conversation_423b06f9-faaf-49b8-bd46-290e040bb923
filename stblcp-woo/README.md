# WooCommerce StablecoinPay Gateway

A professional WooCommerce payment gateway plugin for accepting cryptocurrency payments via StablecoinPay. Support for USDT, USDC, DAI, PYUSD, and BUSD across multiple blockchain networks.

## Features

### 🚀 **Core Features**
- **Self-Hosted Payment Page** - Dedicated payment page hosted on your domain
- **Real-Time Payment Monitoring** - Automatic payment detection and confirmation
- **Multiple Cryptocurrencies** - Support for 5 major stablecoins
- **Multi-Blockchain Support** - Ethereum, BSC, Tron, Solana, TON networks
- **HPOS Compatible** - Full support for WooCommerce High-Performance Order Storage

### 💰 **Supported Cryptocurrencies**
- **USDT (Tether USD)** - Ethereum, BSC, Tron, Solana, TON
- **USDC (USD Coin)** - Ethereum, BSC, Tron, Solana, TON
- **DAI (Dai Stablecoin)** - Ethereum, BSC
- **PYUSD (PayPal USD)** - Ethereum
- **BUSD (Binance USD)** - BSC

### 🔧 **Technical Features**
- **Configurable API Settings** - All endpoints and settings manageable from admin
- **Comprehensive Admin Interface** - Dedicated settings page with validation
- **Mobile Responsive Design** - Optimized for all devices
- **QR Code Support** - Easy mobile wallet payments
- **Callback Integration** - Automatic order status updates
- **Debug Logging** - Comprehensive logging for troubleshooting
- **Security Features** - IP whitelisting, input validation, rate limiting

## Requirements

- WordPress 5.0+
- WooCommerce 3.0+
- PHP 7.4+
- MySQL 5.6+
- SSL Certificate (recommended)
- StablecoinPay API Account

## Installation

### Quick Install
1. Download the plugin
2. Upload to `/wp-content/plugins/stablecoinpay-gateway/`
3. Activate through WordPress admin
4. Configure API settings at **WooCommerce > StablecoinPay**
5. Enable payment method at **WooCommerce > Settings > Payments**

For detailed installation instructions, see [INSTALLATION.md](INSTALLATION.md).

## Configuration

### 1. API Settings
```
WooCommerce > StablecoinPay
├── API URL: http://localhost:3000/api/v1
├── API Key: [Your API Key]
└── API Secret: [Your API Secret]
```

### 2. Payment Settings
```
├── Default Token: USDT
├── Default Blockchain: Ethereum
├── Payment Expiry: 30 minutes
└── Test Mode: Enabled (for testing)
```

### 3. WooCommerce Integration
```
WooCommerce > Settings > Payments > StablecoinPay
├── Enable: Yes
├── Title: "Cryptocurrency Payment"
└── Description: "Pay with USDT, USDC, DAI, or other stablecoins"
```

## File Structure

```
stablecoinpay-gateway/
├── stablecoinpay-gateway.php          # Main plugin file
├── readme.txt                         # WordPress plugin readme
├── INSTALLATION.md                    # Installation guide
├── includes/                          # PHP classes
│   ├── class-stablecoinpay-hpos.php          # HPOS compatibility
│   ├── class-stablecoinpay-settings.php      # Settings management
│   ├── class-stablecoinpay-admin.php         # Admin interface
│   ├── class-stablecoinpay-api.php           # API client
│   ├── class-stablecoinpay-callback.php      # Callback handler
│   ├── class-stablecoinpay-payment-page.php  # Payment page
│   ├── class-stablecoinpay-payment-manager.php # Payment data
│   ├── class-stablecoinpay-security.php      # Security utilities
│   ├── class-stablecoinpay-database.php      # Database operations
│   └── class-wc-stablecoinpay-gateway.php    # WooCommerce gateway
├── templates/
│   └── payment-page.php               # Payment page template
├── assets/
│   ├── css/
│   │   ├── payment-page.css           # Payment page styles
│   │   ├── admin.css                  # Admin styles
│   │   └── checkout.css               # Checkout styles
│   ├── js/
│   │   ├── payment-page.js            # Payment page functionality
│   │   └── admin.js                   # Admin functionality
│   └── images/
│       └── stablecoinpay-icon.svg     # Plugin icon
└── languages/                         # Translation files
```

## Usage

### Customer Payment Flow
1. Customer adds products to cart
2. Proceeds to checkout
3. Selects "Cryptocurrency Payment"
4. Completes order and gets redirected to payment page
5. Selects preferred token and blockchain
6. Sends payment from their wallet
7. Payment is automatically detected and confirmed
8. Order status updated and customer redirected to success page

### Admin Management
- **View Payments**: Monitor all cryptocurrency payments
- **Check Status**: Manually check payment status
- **Export Data**: Export payment data to CSV
- **Debug Logs**: View detailed transaction logs
- **Test Connection**: Verify API connectivity

## API Integration

The plugin integrates with the StablecoinPay API:

### Endpoints Used
- `POST /payments` - Create new payment
- `GET /payments/{id}` - Get payment details
- `GET /health` - Test API connection
- `GET /tokens` - Get supported tokens
- `GET /rates/{currency}` - Get exchange rates

### Callback URLs
- `POST /wc-api/stablecoinpay/callback/{order_id}` - Payment status updates

## Security

### Built-in Security Features
- **Input Validation** - All user inputs sanitized and validated
- **CSRF Protection** - Nonce verification for all forms
- **IP Whitelisting** - Restrict callback access by IP address
- **Rate Limiting** - Prevent abuse of API endpoints
- **Secure Storage** - Encrypted storage of sensitive data

### Best Practices
- Use HTTPS in production
- Keep API credentials secure
- Regular plugin updates
- Monitor transaction logs
- Configure IP whitelisting

## Development

### Local Development Setup
1. Clone the repository
2. Set up WordPress with WooCommerce
3. Configure StablecoinPay API (test mode)
4. Enable debug logging
5. Test payment flows

### Customization
- **Payment Page**: Modify `templates/payment-page.php`
- **Styling**: Edit CSS files in `assets/css/`
- **Functionality**: Extend classes in `includes/`

### Hooks and Filters
```php
// Payment confirmed hook
do_action('stablecoinpay_payment_confirmed', $order, $payment_data);

// Modify supported tokens
apply_filters('stablecoinpay_supported_tokens', $tokens);

// Customize payment page data
apply_filters('stablecoinpay_payment_page_data', $data, $order);
```

## Troubleshooting

### Common Issues
- **404 on payment page**: Flush permalinks
- **API connection failed**: Check credentials and URL
- **Callback not working**: Verify URL accessibility
- **Database errors**: Check table creation

### Debug Mode
Enable debug logging:
1. Go to **WooCommerce > StablecoinPay**
2. Enable **Debug Mode**
3. View logs at **WooCommerce > Status > Logs**

## Support

- **Documentation**: See [INSTALLATION.md](INSTALLATION.md)
- **Issues**: Report bugs via GitHub issues
- **API Support**: Contact StablecoinPay support

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- Support for 5 major stablecoins
- Multi-blockchain support
- Self-hosted payment page
- HPOS compatibility
- Comprehensive admin interface
- Real-time payment monitoring
- Security features and validation

---

**Made with ❤️ for the WordPress and WooCommerce community**
