<?php
/**
 * Plugin Name: WooCommerce StablecoinPay Gateway
 * Plugin URI: https://stablecoinpay.com
 * Description: Accept cryptocurrency payments via StablecoinPay - supports USDT, USDC, DAI, PYUSD, BUSD across multiple blockchains (Ethereum, BSC, Tron, Solana, TON)
 * Version: 1.0.0
 * Author: StablecoinPay
 * Author URI: https://stablecoinpay.com
 * Requires at least: 6.0
 * Tested up to: 6.6
 * Requires PHP: 8.0
 * WC requires at least: 7.0
 * WC tested up to: 9.0
 * Text Domain: stablecoinpay-gateway
 * Domain Path: /languages
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Plugin constants
define('STABLECOINPAY_PLUGIN_URL', plugin_dir_url(__FILE__));
define('STABLECOINPAY_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('STABLECOINPAY_VERSION', '1.0.0');
define('STABLECOINPAY_PLUGIN_FILE', __FILE__);

// Declare HPOS compatibility
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility(
            'custom_order_tables', 
            __FILE__, 
            true
        );
    }
});

// Initialize plugin
add_action('plugins_loaded', 'stablecoinpay_init');

function stablecoinpay_init() {
    // Check PHP version requirement
    if (version_compare(PHP_VERSION, '8.0', '<')) {
        add_action('admin_notices', function() {
            // Only show on admin pages, not during activation
            if (is_admin() && !defined('STABLECOINPAY_ACTIVATING') && !wp_doing_ajax()) {
                echo '<div class="notice notice-error"><p>';
                echo sprintf(
                    __('StablecoinPay Gateway requires PHP 8.0 or higher. You are running PHP %s. Please upgrade your PHP version.', 'stablecoinpay-gateway'),
                    PHP_VERSION
                );
                echo '</p></div>';
            }
        });
        return;
    }

    // Check WordPress version requirement
    global $wp_version;
    if (version_compare($wp_version, '6.0', '<')) {
        add_action('admin_notices', function() {
            global $wp_version;
            // Only show on admin pages, not during activation
            if (is_admin() && !defined('STABLECOINPAY_ACTIVATING') && !wp_doing_ajax()) {
                echo '<div class="notice notice-error"><p>';
                echo sprintf(
                    __('StablecoinPay Gateway requires WordPress 6.0 or higher. You are running WordPress %s. Please upgrade your WordPress installation.', 'stablecoinpay-gateway'),
                    $wp_version
                );
                echo '</p></div>';
            }
        });
        return;
    }

    // Check if WooCommerce is active
    if (!class_exists('WC_Payment_Gateway')) {
        add_action('admin_notices', 'stablecoinpay_woocommerce_missing_notice');
        return;
    }

    // Check WooCommerce version requirement
    if (defined('WC_VERSION') && version_compare(WC_VERSION, '7.0', '<')) {
        add_action('admin_notices', function() {
            // Only show on admin pages, not during activation
            if (is_admin() && !defined('STABLECOINPAY_ACTIVATING') && !wp_doing_ajax()) {
                echo '<div class="notice notice-error"><p>';
                echo sprintf(
                    __('StablecoinPay Gateway requires WooCommerce 7.0 or higher. You are running WooCommerce %s. Please upgrade WooCommerce.', 'stablecoinpay-gateway'),
                    WC_VERSION
                );
                echo '</p></div>';
            }
        });
        return;
    }

    // Check required PHP extensions
    $required_extensions = ['curl', 'json', 'openssl', 'mbstring'];
    $missing_extensions = [];

    foreach ($required_extensions as $extension) {
        if (!extension_loaded($extension)) {
            $missing_extensions[] = $extension;
        }
    }

    if (!empty($missing_extensions)) {
        add_action('admin_notices', function() use ($missing_extensions) {
            // Only show on admin pages, not during activation
            if (is_admin() && !defined('STABLECOINPAY_ACTIVATING') && !wp_doing_ajax()) {
                echo '<div class="notice notice-error"><p>';
                echo sprintf(
                    __('StablecoinPay Gateway requires the following PHP extensions: %s. Please contact your hosting provider to install these extensions.', 'stablecoinpay-gateway'),
                    implode(', ', $missing_extensions)
                );
                echo '</p></div>';
            }
        });
        return;
    }

    // Load text domain
    load_plugin_textdomain('stablecoinpay-gateway', false, dirname(plugin_basename(__FILE__)) . '/languages');

    // Include required files
    $required_files = array(
        'includes/class-stablecoinpay-error-handler.php',
        'includes/class-stablecoinpay-database.php',
        'includes/class-stablecoinpay-hpos.php',
        'includes/class-stablecoinpay-settings.php',
        'includes/class-stablecoinpay-security.php',
        'includes/class-stablecoinpay-api.php',
        'includes/class-stablecoinpay-payment-manager.php',
        'includes/class-stablecoinpay-callback.php',
        'includes/class-stablecoinpay-payment-page.php',
        'includes/class-stablecoinpay-blockchain-explorer.php',
        'includes/class-stablecoinpay-admin.php',
        'includes/class-wc-stablecoinpay-gateway.php',
        'includes/class-stablecoinpay-quick-buy.php'
    );

    foreach ($required_files as $file) {
        $file_path = STABLECOINPAY_PLUGIN_PATH . $file;
        if (file_exists($file_path)) {
            require_once $file_path;
        } else {
            add_action('admin_notices', function() use ($file) {
                echo '<div class="error"><p><strong>StablecoinPay Gateway:</strong> Missing file: ' . esc_html($file) . '</p></div>';
            });
            return;
        }
    }

    // Initialize components (only if not during activation)
    if (!defined('STABLECOINPAY_ACTIVATING')) {
        if (class_exists('StablecoinPay_Settings')) {
            new StablecoinPay_Settings();
        }

        if (class_exists('StablecoinPay_Admin')) {
            new StablecoinPay_Admin();
        }

        if (class_exists('StablecoinPay_Callback')) {
            new StablecoinPay_Callback();
        }

        if (class_exists('StablecoinPay_Payment_Page')) {
            new StablecoinPay_Payment_Page();
        }
    }

    add_filter('woocommerce_payment_gateways', 'add_stablecoinpay_gateway');

    // Add blocks support
    add_action('woocommerce_blocks_loaded', 'stablecoinpay_woocommerce_block_support');
}

function add_stablecoinpay_gateway($gateways) {
    $gateways[] = 'WC_StablecoinPay_Gateway';
    return $gateways;
}

function stablecoinpay_woocommerce_block_support() {
    if (class_exists('Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType')) {
        require_once STABLECOINPAY_PLUGIN_PATH . 'includes/class-stablecoinpay-checkout-block.php';
        add_action(
            'woocommerce_blocks_payment_method_type_registration',
            function(Automattic\WooCommerce\Blocks\Payments\PaymentMethodRegistry $payment_method_registry) {
                $payment_method_registry->register(new StablecoinPay_Checkout_Block());
            }
        );
    }
}

function stablecoinpay_woocommerce_missing_notice() {
    // Only show on admin pages, not during activation
    if (is_admin() && !defined('STABLECOINPAY_ACTIVATING') && !wp_doing_ajax()) {
        echo '<div class="error"><p><strong>' .
             esc_html__('StablecoinPay Gateway requires WooCommerce to be installed and active.', 'stablecoinpay-gateway') .
             '</strong></p></div>';
    }
}

// Plugin activation hook
register_activation_hook(__FILE__, 'stablecoinpay_activate');
function stablecoinpay_activate() {
    // Define activation constant to prevent class instantiation
    define('STABLECOINPAY_ACTIVATING', true);

    // Prevent any output during activation
    ob_start();

    try {
        // Include database class if not already loaded
        if (!class_exists('StablecoinPay_Database')) {
            require_once STABLECOINPAY_PLUGIN_PATH . 'includes/class-stablecoinpay-database.php';
        }

        // Create database tables
        StablecoinPay_Database::create_tables();

        // SECURITY FIX: Migrate existing credentials to encrypted format
        if (!class_exists('StablecoinPay_Settings')) {
            require_once STABLECOINPAY_PLUGIN_PATH . 'includes/class-stablecoinpay-settings.php';
        }
        StablecoinPay_Settings::migrate_credentials_to_encrypted();

        // Initialize payment page rewrite rules
        if (!class_exists('StablecoinPay_Payment_Page')) {
            require_once STABLECOINPAY_PLUGIN_PATH . 'includes/class-stablecoinpay-payment-page.php';
        }

        // Add rewrite rules
        add_rewrite_rule(
            '^stablecoinpay-payment/?$',
            'index.php?stablecoinpay_payment_page=1',
            'top'
        );
        add_rewrite_tag('%stablecoinpay_payment_page%', '([^&]+)');

        // Flush rewrite rules to make them active
        flush_rewrite_rules();

    } catch (Exception $e) {
        // Log any errors but don't output them
        error_log('StablecoinPay activation error: ' . $e->getMessage());
    }

    // Clean any output that might have been generated
    $output = ob_get_clean();

    // Log any unexpected output for debugging
    if (!empty($output) && WP_DEBUG) {
        error_log('StablecoinPay activation output: ' . $output);
    }
}

// Plugin deactivation hook
register_deactivation_hook(__FILE__, 'stablecoinpay_deactivate');
function stablecoinpay_deactivate() {
    // Clean up rewrite rules
    flush_rewrite_rules();
}

// Add settings link on plugins page
add_filter('plugin_action_links_' . plugin_basename(__FILE__), 'stablecoinpay_plugin_action_links');
function stablecoinpay_plugin_action_links($links) {
    $settings_link = '<a href="' . admin_url('admin.php?page=stablecoinpay-settings') . '">' . 
                     esc_html__('Settings', 'stablecoinpay-gateway') . '</a>';
    array_unshift($links, $settings_link);
    return $links;
}
