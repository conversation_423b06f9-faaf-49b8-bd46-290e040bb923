# WooCommerce StablecoinPay Plugin Implementation Plan

## Table of Contents

1. [Project Overview](#project-overview)
2. [Architecture Overview](#architecture-overview)
3. [Phase 1: Plugin Foundation](#phase-1-plugin-foundation)
4. [Phase 2: StablecoinPay API Integration](#phase-2-stablecoinpay-api-integration)
5. [Phase 3: Self-Hosted Payment Page](#phase-3-self-hosted-payment-page)
6. [Phase 4: Real-Time Status Updates](#phase-4-real-time-status-updates)
7. [Phase 5: UI/UX Optimization](#phase-5-uiux-optimization)
8. [Phase 6: Security & Error Handling](#phase-6-security--error-handling)
9. [Database Schema](#database-schema)
10. [File Structure](#file-structure)
11. [Configuration Guidelines](#configuration-guidelines)
12. [Testing Procedures](#testing-procedures)
13. [Deployment Guide](#deployment-guide)
14. [Troubleshooting](#troubleshooting)

---

## Project Overview

### Plugin Name
**WooCommerce StablecoinPay Gateway**

### Core Features
- Self-hosted payment page with modern UI
- Support for 5 blockchains (Ethereum, BSC, Tron, Solana, TON)
- Support for 5 stablecoins (USDT, USDC, DAI, PYUSD, BUSD)
- Real-time payment status updates
- Callback URL integration for order-specific notifications
- Mobile-responsive design
- QR code generation for easy mobile payments

### Technical Stack
- **Backend**: PHP 7.4+ (WooCommerce compatibility)
- **Frontend**: Vanilla JavaScript (ES6+) with modern CSS
- **Database**: WordPress/WooCommerce existing tables + custom tables
- **API Integration**: StablecoinPay REST API (configurable endpoint)
- **Real-time Updates**: Server-Sent Events (SSE) or WebSocket fallback
- **HPOS Compatibility**: Full support for WooCommerce High-Performance Order Storage
- **Settings Management**: Dedicated admin settings page with validation

---

## Architecture Overview

### Component Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    WooCommerce Store                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Product Page  │  │  Shopping Cart  │  │   Checkout   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────┬───────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────┐
│              StablecoinPay Gateway Plugin                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Gateway Class  │  │  API Client     │  │  Callback    │ │
│  │  (WC_Payment_   │  │  (StablecoinPay │  │  Handler     │ │
│  │   Gateway)      │  │   API)          │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────┬───────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────┐
│                Self-Hosted Payment Page                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Token Selector │  │  Payment Info   │  │  Status      │ │
│  │  (Blockchain/   │  │  (Address, QR,  │  │  Monitor     │ │
│  │   Token)        │  │   Amount)       │  │  (Real-time) │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────┬───────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────┐
│                  StablecoinPay API                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Payment        │  │  Transaction    │  │  Callback    │ │
│  │  Creation       │  │  Monitoring     │  │  Delivery    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### User Flow Diagram

```
Customer Journey:
Cart → Checkout → Payment Method → Place Order → Payment Page → Payment → Confirmation

Technical Flow:
1. WC Gateway processes order
2. Creates StablecoinPay payment via API
3. Redirects to self-hosted payment page
4. Customer selects token/blockchain
5. Payment page displays payment details
6. Real-time monitoring begins
7. Customer sends payment
8. StablecoinPay detects transaction
9. Callback updates WooCommerce order
10. Payment page shows success
11. Redirect to order confirmation
```

---

## Phase 1: Plugin Foundation

### 1.1 Plugin Structure Setup

**Duration**: 2-3 days

**Objectives**:
- Create WordPress plugin foundation with HPOS compatibility
- Implement WooCommerce payment gateway class
- Set up comprehensive admin settings page
- Ensure configurable API endpoints and settings

**Tasks**:

#### 1.1.1 Create Plugin Files
```php
// File: stablecoinpay-gateway.php
<?php
/**
 * Plugin Name: WooCommerce StablecoinPay Gateway
 * Description: Accept cryptocurrency payments via StablecoinPay
 * Version: 1.0.0
 * Author: Your Name
 * WC requires at least: 3.0
 * WC tested up to: 8.0
 * Requires PHP: 7.4
 * Text Domain: stablecoinpay-gateway
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Plugin constants
define('STABLECOINPAY_PLUGIN_URL', plugin_dir_url(__FILE__));
define('STABLECOINPAY_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('STABLECOINPAY_VERSION', '1.0.0');
define('STABLECOINPAY_PLUGIN_FILE', __FILE__);

// Declare HPOS compatibility
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
    }
});

// Initialize plugin
add_action('plugins_loaded', 'stablecoinpay_init');

function stablecoinpay_init() {
    // Check if WooCommerce is active
    if (!class_exists('WC_Payment_Gateway')) {
        add_action('admin_notices', 'stablecoinpay_woocommerce_missing_notice');
        return;
    }

    // Load text domain
    load_plugin_textdomain('stablecoinpay-gateway', false, dirname(plugin_basename(__FILE__)) . '/languages');

    // Include required files
    require_once STABLECOINPAY_PLUGIN_PATH . 'includes/class-stablecoinpay-settings.php';
    require_once STABLECOINPAY_PLUGIN_PATH . 'includes/class-wc-stablecoinpay-gateway.php';
    require_once STABLECOINPAY_PLUGIN_PATH . 'includes/class-stablecoinpay-api.php';
    require_once STABLECOINPAY_PLUGIN_PATH . 'includes/class-stablecoinpay-callback.php';
    require_once STABLECOINPAY_PLUGIN_PATH . 'includes/class-stablecoinpay-admin.php';

    // Initialize components
    new StablecoinPay_Settings();
    new StablecoinPay_Admin();

    add_filter('woocommerce_payment_gateways', 'add_stablecoinpay_gateway');
}

function add_stablecoinpay_gateway($gateways) {
    $gateways[] = 'WC_StablecoinPay_Gateway';
    return $gateways;
}

function stablecoinpay_woocommerce_missing_notice() {
    echo '<div class="error"><p><strong>' .
         esc_html__('StablecoinPay Gateway requires WooCommerce to be installed and active.', 'stablecoinpay-gateway') .
         '</strong></p></div>';
}

// Plugin activation hook
register_activation_hook(__FILE__, 'stablecoinpay_activate');
function stablecoinpay_activate() {
    // Create database tables
    require_once STABLECOINPAY_PLUGIN_PATH . 'includes/class-stablecoinpay-database.php';
    StablecoinPay_Database::create_tables();

    // Flush rewrite rules
    flush_rewrite_rules();
}

// Plugin deactivation hook
register_deactivation_hook(__FILE__, 'stablecoinpay_deactivate');
function stablecoinpay_deactivate() {
    // Flush rewrite rules
    flush_rewrite_rules();
}
```

#### 1.1.2 Gateway Class Implementation
```php
// File: includes/class-wc-stablecoinpay-gateway.php
<?php
class WC_StablecoinPay_Gateway extends WC_Payment_Gateway {

    public function __construct() {
        $this->id = 'stablecoinpay';
        $this->icon = STABLECOINPAY_PLUGIN_URL . 'assets/images/stablecoinpay-icon.png';
        $this->has_fields = false;
        $this->method_title = __('StablecoinPay', 'stablecoinpay-gateway');
        $this->method_description = __('Accept cryptocurrency payments via StablecoinPay', 'stablecoinpay-gateway');

        $this->supports = array(
            'products',
            'refunds'
        );

        $this->init_form_fields();
        $this->init_settings();

        $this->title = $this->get_option('title');
        $this->description = $this->get_option('description');
        $this->enabled = $this->get_option('enabled');
        $this->testmode = 'yes' === $this->get_option('testmode');
        $this->api_url = $this->get_option('api_url', 'http://localhost:3000/api/v1');
        $this->api_key = $this->get_option('api_key');
        $this->api_secret = $this->get_option('api_secret');

        add_action('woocommerce_update_options_payment_gateways_' . $this->id, array($this, 'process_admin_options'));
        add_action('wp_enqueue_scripts', array($this, 'payment_scripts'));
    }
    
    public function init_form_fields() {
        $this->form_fields = array(
            'enabled' => array(
                'title' => __('Enable/Disable', 'stablecoinpay-gateway'),
                'type' => 'checkbox',
                'label' => __('Enable StablecoinPay Gateway', 'stablecoinpay-gateway'),
                'default' => 'no'
            ),
            'title' => array(
                'title' => __('Title', 'stablecoinpay-gateway'),
                'type' => 'text',
                'description' => __('Payment method title that customers see', 'stablecoinpay-gateway'),
                'default' => __('Cryptocurrency Payment', 'stablecoinpay-gateway'),
                'desc_tip' => true,
            ),
            'description' => array(
                'title' => __('Description', 'stablecoinpay-gateway'),
                'type' => 'textarea',
                'description' => __('Payment method description', 'stablecoinpay-gateway'),
                'default' => __('Pay with USDT, USDC, DAI, or other stablecoins', 'stablecoinpay-gateway'),
            ),
            'api_settings_title' => array(
                'title' => __('API Configuration', 'stablecoinpay-gateway'),
                'type' => 'title',
                'description' => __('Configure your StablecoinPay API settings', 'stablecoinpay-gateway'),
            ),
            'api_url' => array(
                'title' => __('API URL', 'stablecoinpay-gateway'),
                'type' => 'text',
                'description' => __('StablecoinPay API base URL', 'stablecoinpay-gateway'),
                'default' => 'http://localhost:3000/api/v1',
                'desc_tip' => true,
                'placeholder' => 'https://api.stablecoinpay.com/v1'
            ),
            'api_key' => array(
                'title' => __('API Key', 'stablecoinpay-gateway'),
                'type' => 'text',
                'description' => __('Your StablecoinPay API Key', 'stablecoinpay-gateway'),
                'desc_tip' => true,
            ),
            'api_secret' => array(
                'title' => __('API Secret', 'stablecoinpay-gateway'),
                'type' => 'password',
                'description' => __('Your StablecoinPay API Secret', 'stablecoinpay-gateway'),
                'desc_tip' => true,
            ),
            'payment_settings_title' => array(
                'title' => __('Payment Settings', 'stablecoinpay-gateway'),
                'type' => 'title',
                'description' => __('Configure default payment options', 'stablecoinpay-gateway'),
            ),
            'testmode' => array(
                'title' => __('Test Mode', 'stablecoinpay-gateway'),
                'type' => 'checkbox',
                'label' => __('Enable Test Mode', 'stablecoinpay-gateway'),
                'default' => 'yes',
                'description' => __('Use testnet for testing payments', 'stablecoinpay-gateway'),
            ),
            'default_token' => array(
                'title' => __('Default Token', 'stablecoinpay-gateway'),
                'type' => 'select',
                'description' => __('Default cryptocurrency token', 'stablecoinpay-gateway'),
                'default' => 'USDT',
                'options' => array(
                    'USDT' => 'USDT',
                    'USDC' => 'USDC',
                    'DAI' => 'DAI',
                    'PYUSD' => 'PYUSD',
                    'BUSD' => 'BUSD'
                ),
                'desc_tip' => true,
            ),
            'default_blockchain' => array(
                'title' => __('Default Blockchain', 'stablecoinpay-gateway'),
                'type' => 'select',
                'description' => __('Default blockchain network', 'stablecoinpay-gateway'),
                'default' => 'ETHEREUM',
                'options' => array(
                    'ETHEREUM' => 'Ethereum',
                    'BSC' => 'Binance Smart Chain',
                    'TRON' => 'Tron',
                    'SOLANA' => 'Solana',
                    'TON' => 'TON'
                ),
                'desc_tip' => true,
            ),
            'payment_expiry' => array(
                'title' => __('Payment Expiry (minutes)', 'stablecoinpay-gateway'),
                'type' => 'number',
                'description' => __('How long payments remain valid (1-1440 minutes)', 'stablecoinpay-gateway'),
                'default' => '30',
                'custom_attributes' => array(
                    'min' => '1',
                    'max' => '1440'
                ),
                'desc_tip' => true,
            ),
            'advanced_settings_title' => array(
                'title' => __('Advanced Settings', 'stablecoinpay-gateway'),
                'type' => 'title',
                'description' => __('Advanced configuration options', 'stablecoinpay-gateway'),
            ),
            'debug_mode' => array(
                'title' => __('Debug Mode', 'stablecoinpay-gateway'),
                'type' => 'checkbox',
                'label' => __('Enable Debug Logging', 'stablecoinpay-gateway'),
                'default' => 'no',
                'description' => __('Log API requests and responses for debugging', 'stablecoinpay-gateway'),
            ),
            'callback_ip_whitelist' => array(
                'title' => __('Callback IP Whitelist', 'stablecoinpay-gateway'),
                'type' => 'textarea',
                'description' => __('Comma-separated list of IP addresses allowed to send callbacks (leave empty to allow all)', 'stablecoinpay-gateway'),
                'placeholder' => '*************, 10.0.0.0/8',
                'desc_tip' => true,
            )
        );
    }
    
    public function process_payment($order_id) {
        $order = wc_get_order($order_id);

        if (!$order) {
            wc_add_notice(__('Order not found.', 'stablecoinpay-gateway'), 'error');
            return array('result' => 'failure');
        }

        try {
            // Create StablecoinPay payment with configurable settings
            $api = new StablecoinPay_API(
                $this->api_key,
                $this->api_secret,
                $this->api_url,
                $this->testmode
            );

            $payment_data = array(
                'amount' => $order->get_total(),
                'currency' => $order->get_currency(),
                'token' => $this->get_option('default_token', 'USDT'),
                'blockchain' => $this->get_option('default_blockchain', 'ETHEREUM'),
                'network' => $this->testmode ? 'TESTNET' : 'MAINNET',
                'expiryMinutes' => intval($this->get_option('payment_expiry', 30)),
                'callbackUrl' => home_url('/wc-api/stablecoinpay/callback/' . $order_id),
                'metadata' => array(
                    'order_id' => $order_id,
                    'order_key' => $order->get_order_key(),
                    'customer_id' => $order->get_customer_id(),
                    'customer_email' => $order->get_billing_email(),
                    'site_url' => home_url(),
                    'plugin_version' => STABLECOINPAY_VERSION
                )
            );

            $payment = $api->create_payment($payment_data);

            if ($payment) {
                // Store payment data using HPOS-compatible methods
                $order->update_meta_data('_stablecoinpay_payment_id', $payment['id']);
                $order->update_meta_data('_stablecoinpay_payment_data', wp_json_encode($payment));
                $order->update_meta_data('_stablecoinpay_status', $payment['status']);
                $order->update_meta_data('_stablecoinpay_created_at', current_time('mysql'));
                $order->save();

                // Store in custom table for better performance
                StablecoinPay_Payment_Manager::store_payment($order_id, $payment);

                // Mark as pending
                $order->update_status('pending', __('Awaiting cryptocurrency payment', 'stablecoinpay-gateway'));

                // Add order note
                $order->add_order_note(sprintf(
                    __('StablecoinPay payment created. Payment ID: %s, Token: %s, Network: %s', 'stablecoinpay-gateway'),
                    $payment['id'],
                    $payment['token'],
                    $payment['network']
                ));

                // Redirect to payment page with order key for security
                return array(
                    'result' => 'success',
                    'redirect' => add_query_arg(array(
                        'order_id' => $order_id,
                        'payment_id' => $payment['id'],
                        'key' => $order->get_order_key()
                    ), home_url('/stablecoinpay-payment/'))
                );
            }

        } catch (Exception $e) {
            // Log error if debug mode is enabled
            if ('yes' === $this->get_option('debug_mode')) {
                $this->log('Payment creation failed: ' . $e->getMessage());
            }

            wc_add_notice(__('Payment error: ', 'stablecoinpay-gateway') . $e->getMessage(), 'error');
            return array('result' => 'failure');
        }

        return array('result' => 'failure');
    }

    /**
     * Log messages if debug mode is enabled
     */
    private function log($message) {
        if ('yes' === $this->get_option('debug_mode')) {
            if (empty($this->logger)) {
                $this->logger = wc_get_logger();
            }
            $this->logger->info($message, array('source' => 'stablecoinpay'));
        }
    }

    /**
     * Validate admin options
     */
    public function validate_api_url_field($key, $value) {
        if (empty($value)) {
            return 'http://localhost:3000/api/v1';
        }

        $value = esc_url_raw($value);

        if (!filter_var($value, FILTER_VALIDATE_URL)) {
            WC_Admin_Settings::add_error(__('API URL must be a valid URL.', 'stablecoinpay-gateway'));
            return $this->get_option($key);
        }

        return rtrim($value, '/');
    }

    /**
     * Validate payment expiry field
     */
    public function validate_payment_expiry_field($key, $value) {
        $value = intval($value);

        if ($value < 1 || $value > 1440) {
            WC_Admin_Settings::add_error(__('Payment expiry must be between 1 and 1440 minutes.', 'stablecoinpay-gateway'));
            return 30;
        }

        return $value;
    }
}
```

#### 1.1.3 Dedicated Settings Page
```php
// File: includes/class-stablecoinpay-settings.php
<?php
class StablecoinPay_Settings {

    private $settings_key = 'stablecoinpay_settings';

    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }

    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('StablecoinPay Settings', 'stablecoinpay-gateway'),
            __('StablecoinPay', 'stablecoinpay-gateway'),
            'manage_woocommerce',
            'stablecoinpay-settings',
            array($this, 'settings_page')
        );
    }

    public function register_settings() {
        register_setting($this->settings_key, $this->settings_key, array($this, 'validate_settings'));

        // General Settings Section
        add_settings_section(
            'stablecoinpay_general',
            __('General Settings', 'stablecoinpay-gateway'),
            array($this, 'general_section_callback'),
            $this->settings_key
        );

        // API Settings Section
        add_settings_section(
            'stablecoinpay_api',
            __('API Configuration', 'stablecoinpay-gateway'),
            array($this, 'api_section_callback'),
            $this->settings_key
        );

        // Payment Settings Section
        add_settings_section(
            'stablecoinpay_payment',
            __('Payment Configuration', 'stablecoinpay-gateway'),
            array($this, 'payment_section_callback'),
            $this->settings_key
        );

        // Add settings fields
        $this->add_settings_fields();
    }

    private function add_settings_fields() {
        // General Settings
        add_settings_field(
            'enabled',
            __('Enable Gateway', 'stablecoinpay-gateway'),
            array($this, 'checkbox_field'),
            $this->settings_key,
            'stablecoinpay_general',
            array('field' => 'enabled', 'description' => __('Enable StablecoinPay payment gateway', 'stablecoinpay-gateway'))
        );

        add_settings_field(
            'test_mode',
            __('Test Mode', 'stablecoinpay-gateway'),
            array($this, 'checkbox_field'),
            $this->settings_key,
            'stablecoinpay_general',
            array('field' => 'test_mode', 'description' => __('Use testnet for payments', 'stablecoinpay-gateway'))
        );

        add_settings_field(
            'debug_mode',
            __('Debug Mode', 'stablecoinpay-gateway'),
            array($this, 'checkbox_field'),
            $this->settings_key,
            'stablecoinpay_general',
            array('field' => 'debug_mode', 'description' => __('Enable debug logging', 'stablecoinpay-gateway'))
        );

        // API Settings
        add_settings_field(
            'api_url',
            __('API URL', 'stablecoinpay-gateway'),
            array($this, 'text_field'),
            $this->settings_key,
            'stablecoinpay_api',
            array(
                'field' => 'api_url',
                'description' => __('StablecoinPay API base URL', 'stablecoinpay-gateway'),
                'placeholder' => 'https://api.stablecoinpay.com/v1'
            )
        );

        add_settings_field(
            'api_key',
            __('API Key', 'stablecoinpay-gateway'),
            array($this, 'text_field'),
            $this->settings_key,
            'stablecoinpay_api',
            array('field' => 'api_key', 'description' => __('Your StablecoinPay API key', 'stablecoinpay-gateway'))
        );

        add_settings_field(
            'api_secret',
            __('API Secret', 'stablecoinpay-gateway'),
            array($this, 'password_field'),
            $this->settings_key,
            'stablecoinpay_api',
            array('field' => 'api_secret', 'description' => __('Your StablecoinPay API secret', 'stablecoinpay-gateway'))
        );

        // Payment Settings
        add_settings_field(
            'default_token',
            __('Default Token', 'stablecoinpay-gateway'),
            array($this, 'select_field'),
            $this->settings_key,
            'stablecoinpay_payment',
            array(
                'field' => 'default_token',
                'options' => array(
                    'USDT' => 'USDT',
                    'USDC' => 'USDC',
                    'DAI' => 'DAI',
                    'PYUSD' => 'PYUSD',
                    'BUSD' => 'BUSD'
                ),
                'description' => __('Default cryptocurrency token', 'stablecoinpay-gateway')
            )
        );

        add_settings_field(
            'default_blockchain',
            __('Default Blockchain', 'stablecoinpay-gateway'),
            array($this, 'select_field'),
            $this->settings_key,
            'stablecoinpay_payment',
            array(
                'field' => 'default_blockchain',
                'options' => array(
                    'ETHEREUM' => 'Ethereum',
                    'BSC' => 'Binance Smart Chain',
                    'TRON' => 'Tron',
                    'SOLANA' => 'Solana',
                    'TON' => 'TON'
                ),
                'description' => __('Default blockchain network', 'stablecoinpay-gateway')
            )
        );

        add_settings_field(
            'payment_expiry',
            __('Payment Expiry (minutes)', 'stablecoinpay-gateway'),
            array($this, 'number_field'),
            $this->settings_key,
            'stablecoinpay_payment',
            array(
                'field' => 'payment_expiry',
                'min' => 1,
                'max' => 1440,
                'description' => __('How long payments remain valid (1-1440 minutes)', 'stablecoinpay-gateway')
            )
        );
    }

    public function settings_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <div class="stablecoinpay-admin-header">
                <div class="stablecoinpay-admin-logo">
                    <img src="<?php echo STABLECOINPAY_PLUGIN_URL; ?>assets/images/stablecoinpay-logo.png" alt="StablecoinPay" style="max-height: 40px;">
                </div>
                <div class="stablecoinpay-admin-info">
                    <p><?php _e('Configure your StablecoinPay payment gateway settings below.', 'stablecoinpay-gateway'); ?></p>
                </div>
            </div>

            <?php $this->display_connection_status(); ?>

            <form method="post" action="options.php">
                <?php
                settings_fields($this->settings_key);
                do_settings_sections($this->settings_key);
                submit_button();
                ?>
            </form>

            <div class="stablecoinpay-admin-footer">
                <h3><?php _e('Quick Links', 'stablecoinpay-gateway'); ?></h3>
                <ul>
                    <li><a href="<?php echo admin_url('admin.php?page=wc-settings&tab=checkout&section=stablecoinpay'); ?>"><?php _e('Payment Gateway Settings', 'stablecoinpay-gateway'); ?></a></li>
                    <li><a href="<?php echo admin_url('admin.php?page=wc-status&tab=logs'); ?>"><?php _e('View Logs', 'stablecoinpay-gateway'); ?></a></li>
                    <li><a href="#" id="test-api-connection"><?php _e('Test API Connection', 'stablecoinpay-gateway'); ?></a></li>
                </ul>
            </div>
        </div>
        <?php
    }

    private function display_connection_status() {
        $settings = get_option($this->settings_key, array());

        if (!empty($settings['api_key']) && !empty($settings['api_secret']) && !empty($settings['api_url'])) {
            echo '<div id="stablecoinpay-connection-status" class="notice notice-info"><p>' .
                 __('API credentials configured. Click "Test API Connection" to verify.', 'stablecoinpay-gateway') .
                 '</p></div>';
        } else {
            echo '<div class="notice notice-warning"><p>' .
                 __('Please configure your API credentials below.', 'stablecoinpay-gateway') .
                 '</p></div>';
        }
    }

    // Field rendering methods
    public function checkbox_field($args) {
        $settings = get_option($this->settings_key, array());
        $value = isset($settings[$args['field']]) ? $settings[$args['field']] : '';

        echo '<input type="checkbox" id="' . esc_attr($args['field']) . '" name="' . $this->settings_key . '[' . esc_attr($args['field']) . ']" value="1" ' . checked(1, $value, false) . ' />';
        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }
    }

    public function text_field($args) {
        $settings = get_option($this->settings_key, array());
        $value = isset($settings[$args['field']]) ? $settings[$args['field']] : '';
        $placeholder = isset($args['placeholder']) ? $args['placeholder'] : '';

        echo '<input type="text" id="' . esc_attr($args['field']) . '" name="' . $this->settings_key . '[' . esc_attr($args['field']) . ']" value="' . esc_attr($value) . '" placeholder="' . esc_attr($placeholder) . '" class="regular-text" />';
        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }
    }

    public function password_field($args) {
        $settings = get_option($this->settings_key, array());
        $value = isset($settings[$args['field']]) ? $settings[$args['field']] : '';

        echo '<input type="password" id="' . esc_attr($args['field']) . '" name="' . $this->settings_key . '[' . esc_attr($args['field']) . ']" value="' . esc_attr($value) . '" class="regular-text" />';
        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }
    }

    public function select_field($args) {
        $settings = get_option($this->settings_key, array());
        $value = isset($settings[$args['field']]) ? $settings[$args['field']] : '';

        echo '<select id="' . esc_attr($args['field']) . '" name="' . $this->settings_key . '[' . esc_attr($args['field']) . ']">';
        foreach ($args['options'] as $option_value => $option_label) {
            echo '<option value="' . esc_attr($option_value) . '" ' . selected($value, $option_value, false) . '>' . esc_html($option_label) . '</option>';
        }
        echo '</select>';
        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }
    }

    public function number_field($args) {
        $settings = get_option($this->settings_key, array());
        $value = isset($settings[$args['field']]) ? $settings[$args['field']] : '';
        $min = isset($args['min']) ? $args['min'] : '';
        $max = isset($args['max']) ? $args['max'] : '';

        echo '<input type="number" id="' . esc_attr($args['field']) . '" name="' . $this->settings_key . '[' . esc_attr($args['field']) . ']" value="' . esc_attr($value) . '" min="' . esc_attr($min) . '" max="' . esc_attr($max) . '" class="small-text" />';
        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }
    }

    // Section callbacks
    public function general_section_callback() {
        echo '<p>' . __('General plugin settings and operational modes.', 'stablecoinpay-gateway') . '</p>';
    }

    public function api_section_callback() {
        echo '<p>' . __('Configure your StablecoinPay API connection settings.', 'stablecoinpay-gateway') . '</p>';
    }

    public function payment_section_callback() {
        echo '<p>' . __('Default payment options and behavior settings.', 'stablecoinpay-gateway') . '</p>';
    }

    public function validate_settings($input) {
        $validated = array();

        // Validate API URL
        if (!empty($input['api_url'])) {
            $api_url = esc_url_raw($input['api_url']);
            if (filter_var($api_url, FILTER_VALIDATE_URL)) {
                $validated['api_url'] = rtrim($api_url, '/');
            } else {
                add_settings_error($this->settings_key, 'api_url', __('Invalid API URL format.', 'stablecoinpay-gateway'));
            }
        }

        // Validate other fields
        $validated['enabled'] = !empty($input['enabled']) ? 1 : 0;
        $validated['test_mode'] = !empty($input['test_mode']) ? 1 : 0;
        $validated['debug_mode'] = !empty($input['debug_mode']) ? 1 : 0;
        $validated['api_key'] = sanitize_text_field($input['api_key'] ?? '');
        $validated['api_secret'] = sanitize_text_field($input['api_secret'] ?? '');
        $validated['default_token'] = sanitize_text_field($input['default_token'] ?? 'USDT');
        $validated['default_blockchain'] = sanitize_text_field($input['default_blockchain'] ?? 'ETHEREUM');

        // Validate payment expiry
        $payment_expiry = intval($input['payment_expiry'] ?? 30);
        if ($payment_expiry < 1 || $payment_expiry > 1440) {
            add_settings_error($this->settings_key, 'payment_expiry', __('Payment expiry must be between 1 and 1440 minutes.', 'stablecoinpay-gateway'));
            $validated['payment_expiry'] = 30;
        } else {
            $validated['payment_expiry'] = $payment_expiry;
        }

        return $validated;
    }

    public function enqueue_admin_scripts($hook) {
        if ('woocommerce_page_stablecoinpay-settings' !== $hook) {
            return;
        }

        wp_enqueue_script(
            'stablecoinpay-admin',
            STABLECOINPAY_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            STABLECOINPAY_VERSION,
            true
        );

        wp_localize_script('stablecoinpay-admin', 'stablecoinpay_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('stablecoinpay_admin'),
            'strings' => array(
                'testing_connection' => __('Testing connection...', 'stablecoinpay-gateway'),
                'connection_success' => __('Connection successful!', 'stablecoinpay-gateway'),
                'connection_failed' => __('Connection failed. Please check your settings.', 'stablecoinpay-gateway')
            )
        ));

        wp_enqueue_style(
            'stablecoinpay-admin',
            STABLECOINPAY_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            STABLECOINPAY_VERSION
        );
    }
}
```

### 1.2 Database Schema Setup

**Tasks**:

#### 1.2.1 Create Custom Tables
```php
// File: includes/class-stablecoinpay-database.php
<?php
class StablecoinPay_Database {
    
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Payments table
        $table_name = $wpdb->prefix . 'stablecoinpay_payments';
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            order_id bigint(20) NOT NULL,
            payment_id varchar(255) NOT NULL,
            status varchar(50) NOT NULL DEFAULT 'pending',
            token varchar(10) NOT NULL,
            blockchain varchar(20) NOT NULL,
            network varchar(20) NOT NULL,
            target_amount decimal(20,8) NOT NULL,
            request_amount decimal(20,8) NOT NULL,
            fiat_amount decimal(10,2) NOT NULL,
            fiat_currency varchar(3) NOT NULL,
            wallet_address varchar(255) NOT NULL,
            tx_hash varchar(255) DEFAULT NULL,
            callback_url text NOT NULL,
            metadata text DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY payment_id (payment_id),
            KEY order_id (order_id),
            KEY status (status)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Transaction logs table
        $table_name = $wpdb->prefix . 'stablecoinpay_transaction_logs';
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            payment_id varchar(255) NOT NULL,
            event_type varchar(50) NOT NULL,
            event_data text DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY payment_id (payment_id),
            KEY event_type (event_type)
        ) $charset_collate;";
        
        dbDelta($sql);
    }
}

// Hook for plugin activation
register_activation_hook(__FILE__, array('StablecoinPay_Database', 'create_tables'));
```

#### 1.1.4 HPOS Compatibility Implementation

**High-Performance Order Storage (HPOS) Compatibility**:

```php
// File: includes/class-stablecoinpay-hpos.php
<?php
class StablecoinPay_HPOS {

    /**
     * Check if HPOS is enabled
     */
    public static function is_hpos_enabled() {
        return class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') &&
               \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled();
    }

    /**
     * Get order using HPOS-compatible method
     */
    public static function get_order($order_id) {
        if (self::is_hpos_enabled()) {
            return wc_get_order($order_id); // HPOS compatible
        }
        return wc_get_order($order_id); // Legacy compatible
    }

    /**
     * Update order meta using HPOS-compatible method
     */
    public static function update_order_meta($order_id, $meta_key, $meta_value) {
        $order = self::get_order($order_id);
        if ($order) {
            $order->update_meta_data($meta_key, $meta_value);
            $order->save();
            return true;
        }
        return false;
    }

    /**
     * Get order meta using HPOS-compatible method
     */
    public static function get_order_meta($order_id, $meta_key, $single = true) {
        $order = self::get_order($order_id);
        if ($order) {
            return $order->get_meta($meta_key, $single);
        }
        return '';
    }

    /**
     * Search orders using HPOS-compatible method
     */
    public static function search_orders($args) {
        if (self::is_hpos_enabled()) {
            // Use HPOS query methods
            return wc_get_orders($args);
        } else {
            // Use legacy WP_Query methods
            return wc_get_orders($args);
        }
    }
}
```

**HPOS Integration in Gateway Class**:

```php
// Update process_payment method to use HPOS methods
public function process_payment($order_id) {
    // Use HPOS-compatible order retrieval
    $order = StablecoinPay_HPOS::get_order($order_id);

    if (!$order) {
        wc_add_notice(__('Order not found.', 'stablecoinpay-gateway'), 'error');
        return array('result' => 'failure');
    }

    // ... rest of payment processing

    // Use HPOS-compatible meta data storage
    StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_payment_id', $payment['id']);
    StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_status', $payment['status']);

    return array(
        'result' => 'success',
        'redirect' => $redirect_url
    );
}
```

**HPOS Compatibility Declaration**:

```php
// In main plugin file
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility(
            'custom_order_tables',
            __FILE__,
            true
        );
    }
});
```

**Deliverables**:
- ✅ HPOS-compatible plugin structure with feature declaration
- ✅ WooCommerce gateway integration with HPOS support
- ✅ Comprehensive admin settings page with validation
- ✅ Configurable API URL and all settings manageable from admin
- ✅ Database schema with HPOS compatibility
- ✅ Plugin activation/deactivation hooks
- ✅ Translation-ready with text domain
- ✅ Debug logging and error handling

---

## Phase 2: StablecoinPay API Integration

### 2.1 API Client Implementation

**Duration**: 3-4 days

**Objectives**:
- Implement StablecoinPay API client
- Handle authentication and error responses
- Create payment management functions

**Tasks**:

#### 2.1.1 API Client Class
```php
// File: includes/class-stablecoinpay-api.php
<?php
class StablecoinPay_API {

    private $api_key;
    private $api_secret;
    private $base_url;
    private $testmode;
    private $logger;

    public function __construct($api_key, $api_secret, $base_url = null, $testmode = false) {
        $this->api_key = $api_key;
        $this->api_secret = $api_secret;
        $this->testmode = $testmode;

        // Use configurable API URL or fallback to default
        $this->base_url = $base_url ?: 'http://localhost:3000/api/v1';
        $this->base_url = rtrim($this->base_url, '/');

        // Initialize logger for debug mode
        $this->logger = wc_get_logger();
    }

    public function create_payment($payment_data) {
        $endpoint = '/payments';

        // Set default values
        $defaults = array(
            'token' => 'USDT',
            'blockchain' => 'ETHEREUM',
            'network' => $this->testmode ? 'TESTNET' : 'MAINNET',
            'expiryMinutes' => 30
        );

        $payment_data = array_merge($defaults, $payment_data);

        $response = $this->make_request('POST', $endpoint, $payment_data);

        if ($response && $response['success']) {
            return $response['data'];
        }

        throw new Exception($response['error']['message'] ?? 'Payment creation failed');
    }

    public function get_payment($payment_id) {
        $endpoint = '/payments/' . $payment_id;
        $response = $this->make_request('GET', $endpoint);

        if ($response && $response['success']) {
            return $response['data'];
        }

        return null;
    }

    public function check_payment($payment_id) {
        $endpoint = '/payments/' . $payment_id . '/check';
        $response = $this->make_request('POST', $endpoint);

        return $response && $response['success'];
    }

    private function make_request($method, $endpoint, $data = null) {
        $url = $this->base_url . $endpoint;

        $headers = array(
            'X-API-Key' => $this->api_key,
            'X-API-Secret' => $this->api_secret,
            'Content-Type' => 'application/json'
        );

        $args = array(
            'method' => $method,
            'headers' => $headers,
            'timeout' => 30
        );

        if ($data && in_array($method, array('POST', 'PUT', 'PATCH'))) {
            $args['body'] = json_encode($data);
        }

        $response = wp_remote_request($url, $args);

        if (is_wp_error($response)) {
            throw new Exception('API request failed: ' . $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);
        $decoded = json_decode($body, true);

        $status_code = wp_remote_retrieve_response_code($response);

        if ($status_code >= 400) {
            $error_message = $decoded['error']['message'] ?? 'API request failed';
            throw new Exception($error_message);
        }

        return $decoded;
    }

    public function get_supported_tokens() {
        return array(
            'ETHEREUM' => array('USDT', 'USDC', 'DAI', 'PYUSD'),
            'BSC' => array('USDT', 'USDC', 'DAI', 'BUSD'),
            'TRON' => array('USDT', 'USDC'),
            'SOLANA' => array('USDT', 'USDC'),
            'TON' => array('USDT', 'USDC')
        );
    }
}
```

#### 2.1.2 Callback Handler Implementation
```php
// File: includes/class-stablecoinpay-callback.php
<?php
class StablecoinPay_Callback {

    public function __construct() {
        add_action('woocommerce_api_stablecoinpay_callback', array($this, 'handle_callback'));
        add_action('init', array($this, 'add_rewrite_rules'));
        add_action('template_redirect', array($this, 'handle_callback_route'));
    }

    public function add_rewrite_rules() {
        add_rewrite_rule(
            '^wc-api/stablecoinpay/callback/([0-9]+)/?$',
            'index.php?stablecoinpay_callback=1&order_id=$matches[1]',
            'top'
        );

        add_rewrite_tag('%stablecoinpay_callback%', '([^&]+)');
        add_rewrite_tag('%order_id%', '([0-9]+)');
    }

    public function handle_callback_route() {
        if (get_query_var('stablecoinpay_callback')) {
            $order_id = get_query_var('order_id');
            $this->process_callback($order_id);
            exit;
        }
    }

    public function process_callback($order_id) {
        // Verify request method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            exit('Method not allowed');
        }

        // Get callback payload
        $payload = json_decode(file_get_contents('php://input'), true);

        if (!$payload) {
            http_response_code(400);
            exit('Invalid payload');
        }

        // Verify signature (if implemented)
        $signature = $_SERVER['HTTP_X_SIGNATURE'] ?? '';
        if (!$this->verify_signature($payload, $signature)) {
            http_response_code(401);
            exit('Invalid signature');
        }

        // Get order
        $order = wc_get_order($order_id);
        if (!$order) {
            http_response_code(404);
            exit('Order not found');
        }

        // Verify payment ID matches
        $stored_payment_id = $order->get_meta('_stablecoinpay_payment_id');
        if ($stored_payment_id !== $payload['id']) {
            http_response_code(400);
            exit('Payment ID mismatch');
        }

        // Process status update
        $this->update_order_status($order, $payload);

        // Log the callback
        $this->log_callback($order_id, $payload);

        http_response_code(200);
        exit('OK');
    }

    private function update_order_status($order, $payload) {
        $status = $payload['status'];
        $payment_id = $payload['id'];

        switch ($status) {
            case 'DETECTED':
                $order->update_status('on-hold', 'Cryptocurrency payment detected, awaiting confirmation');
                $order->add_order_note('Payment detected on blockchain. Transaction: ' . ($payload['transaction']['txHash'] ?? 'N/A'));
                break;

            case 'CONFIRMED':
                $order->payment_complete($payment_id);
                $order->add_order_note('Cryptocurrency payment confirmed. Transaction: ' . ($payload['transaction']['txHash'] ?? 'N/A'));
                break;

            case 'EXPIRED':
                $order->update_status('cancelled', 'Cryptocurrency payment expired');
                break;

            case 'CANCELED':
                $order->update_status('cancelled', 'Cryptocurrency payment cancelled');
                break;
        }

        // Update payment metadata
        $order->update_meta_data('_stablecoinpay_status', $status);
        $order->update_meta_data('_stablecoinpay_last_update', current_time('mysql'));

        if (isset($payload['transaction']['txHash'])) {
            $order->update_meta_data('_stablecoinpay_tx_hash', $payload['transaction']['txHash']);
        }

        $order->save();
    }

    private function verify_signature($payload, $signature) {
        // Implement signature verification if needed
        // For now, return true (implement based on StablecoinPay documentation)
        return true;
    }

    private function log_callback($order_id, $payload) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'stablecoinpay_transaction_logs';

        $wpdb->insert(
            $table_name,
            array(
                'payment_id' => $payload['id'],
                'event_type' => 'callback_received',
                'event_data' => json_encode($payload)
            ),
            array('%s', '%s', '%s')
        );
    }
}

// Initialize callback handler
new StablecoinPay_Callback();
```

### 2.2 Payment Data Management

**Tasks**:

#### 2.2.1 Payment Storage and Retrieval
```php
// File: includes/class-stablecoinpay-payment-manager.php
<?php
class StablecoinPay_Payment_Manager {

    public static function store_payment($order_id, $payment_data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'stablecoinpay_payments';

        $data = array(
            'order_id' => $order_id,
            'payment_id' => $payment_data['id'],
            'status' => $payment_data['status'],
            'token' => $payment_data['token'],
            'blockchain' => $payment_data['blockchain'],
            'network' => $payment_data['network'],
            'target_amount' => $payment_data['targetAmount'],
            'request_amount' => $payment_data['requestAmount'],
            'fiat_amount' => $payment_data['fiatAmount'],
            'fiat_currency' => $payment_data['fiatCurrency'],
            'wallet_address' => $payment_data['address'],
            'callback_url' => $payment_data['callbackUrl'],
            'metadata' => json_encode($payment_data['metadata'] ?? array())
        );

        $result = $wpdb->insert($table_name, $data);

        if ($result === false) {
            throw new Exception('Failed to store payment data');
        }

        return $wpdb->insert_id;
    }

    public static function get_payment_by_order($order_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'stablecoinpay_payments';

        $payment = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE order_id = %d ORDER BY created_at DESC LIMIT 1", $order_id),
            ARRAY_A
        );

        if ($payment && $payment['metadata']) {
            $payment['metadata'] = json_decode($payment['metadata'], true);
        }

        return $payment;
    }

    public static function update_payment_status($payment_id, $status, $tx_hash = null) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'stablecoinpay_payments';

        $data = array(
            'status' => $status,
            'updated_at' => current_time('mysql')
        );

        if ($tx_hash) {
            $data['tx_hash'] = $tx_hash;
        }

        $result = $wpdb->update(
            $table_name,
            $data,
            array('payment_id' => $payment_id),
            array('%s', '%s'),
            array('%s')
        );

        return $result !== false;
    }
}
```

**Deliverables**:
- ✅ StablecoinPay API client implementation
- ✅ Callback URL handler with order-specific routing
- ✅ Payment data storage and management
- ✅ Error handling and logging
- ✅ WooCommerce order status integration

---

## Phase 3: Self-Hosted Payment Page

### 3.1 Payment Page Template System

**Duration**: 4-5 days

**Objectives**:
- Create standalone payment page independent of WordPress themes
- Implement responsive design with modern UI
- Add token/blockchain selection interface
- Display payment information with QR codes

**Tasks**:

#### 3.1.1 Payment Page Handler
```php
// File: includes/class-stablecoinpay-payment-page.php
<?php
class StablecoinPay_Payment_Page {

    public function __construct() {
        add_action('init', array($this, 'add_rewrite_rules'));
        add_action('template_redirect', array($this, 'handle_payment_page'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_payment_scripts'));
    }

    public function add_rewrite_rules() {
        add_rewrite_rule(
            '^stablecoinpay-payment/?$',
            'index.php?stablecoinpay_payment=1',
            'top'
        );

        add_rewrite_tag('%stablecoinpay_payment%', '([^&]+)');
    }

    public function handle_payment_page() {
        if (get_query_var('stablecoinpay_payment')) {
            $this->display_payment_page();
            exit;
        }
    }

    private function display_payment_page() {
        // Get parameters
        $order_id = intval($_GET['order_id'] ?? 0);
        $payment_id = sanitize_text_field($_GET['payment_id'] ?? '');

        if (!$order_id || !$payment_id) {
            wp_die('Invalid payment parameters');
        }

        // Verify order exists and belongs to current user or session
        $order = wc_get_order($order_id);
        if (!$order) {
            wp_die('Order not found');
        }

        // Get payment data
        $payment_data = StablecoinPay_Payment_Manager::get_payment_by_order($order_id);
        if (!$payment_data || $payment_data['payment_id'] !== $payment_id) {
            wp_die('Payment not found');
        }

        // Load payment page template
        $this->load_payment_template($order, $payment_data);
    }

    private function load_payment_template($order, $payment_data) {
        // Disable WordPress theme
        define('DONOTCACHEPAGE', true);

        // Get API instance
        $gateway = new WC_StablecoinPay_Gateway();
        $api = new StablecoinPay_API(
            $gateway->get_option('api_key'),
            $gateway->get_option('api_secret'),
            $gateway->get_option('testmode') === 'yes'
        );

        // Get fresh payment data from API
        try {
            $fresh_payment_data = $api->get_payment($payment_data['payment_id']);
        } catch (Exception $e) {
            $fresh_payment_data = null;
        }

        // Prepare template data
        $template_data = array(
            'order' => $order,
            'payment' => $fresh_payment_data ?: $payment_data,
            'supported_tokens' => $api->get_supported_tokens(),
            'plugin_url' => STABLECOINPAY_PLUGIN_URL,
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('stablecoinpay_payment_' . $order->get_id())
        );

        // Load template
        include STABLECOINPAY_PLUGIN_PATH . 'templates/payment-page.php';
    }

    public function enqueue_payment_scripts() {
        if (get_query_var('stablecoinpay_payment')) {
            wp_enqueue_style(
                'stablecoinpay-payment',
                STABLECOINPAY_PLUGIN_URL . 'assets/css/payment-page.css',
                array(),
                STABLECOINPAY_VERSION
            );

            wp_enqueue_script(
                'stablecoinpay-payment',
                STABLECOINPAY_PLUGIN_URL . 'assets/js/payment-page.js',
                array(),
                STABLECOINPAY_VERSION,
                true
            );

            wp_localize_script('stablecoinpay-payment', 'stablecoinpay_vars', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('stablecoinpay_ajax'),
                'order_id' => intval($_GET['order_id'] ?? 0),
                'payment_id' => sanitize_text_field($_GET['payment_id'] ?? ''),
                'check_interval' => 10000 // 10 seconds
            ));
        }
    }
}

new StablecoinPay_Payment_Page();
```

#### 3.1.2 Payment Page Template
```php
// File: templates/payment-page.php
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Your Payment - <?php echo esc_html(get_bloginfo('name')); ?></title>
    <link rel="stylesheet" href="<?php echo $template_data['plugin_url']; ?>assets/css/payment-page.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="stablecoinpay-payment-page">
    <div class="payment-container">
        <!-- Header -->
        <header class="payment-header">
            <div class="header-content">
                <h1 class="site-title"><?php echo esc_html(get_bloginfo('name')); ?></h1>
                <div class="order-info">
                    <span class="order-number">Order #<?php echo $template_data['order']->get_order_number(); ?></span>
                    <span class="order-total"><?php echo $template_data['order']->get_formatted_order_total(); ?></span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="payment-main">
            <!-- Payment Status -->
            <div class="payment-status" id="payment-status">
                <div class="status-indicator waiting" id="status-indicator">
                    <div class="status-icon">⏳</div>
                    <div class="status-text">
                        <h3 id="status-title">Waiting for Payment</h3>
                        <p id="status-description">Please send the exact amount to the address below</p>
                    </div>
                </div>
            </div>

            <!-- Token Selection -->
            <div class="token-selection" id="token-selection">
                <h3>Select Payment Method</h3>
                <div class="token-grid">
                    <?php foreach ($template_data['supported_tokens'] as $blockchain => $tokens): ?>
                        <?php foreach ($tokens as $token): ?>
                            <button class="token-option"
                                    data-blockchain="<?php echo esc_attr($blockchain); ?>"
                                    data-token="<?php echo esc_attr($token); ?>">
                                <div class="token-icon">
                                    <img src="<?php echo $template_data['plugin_url']; ?>assets/images/tokens/<?php echo strtolower($token); ?>.png"
                                         alt="<?php echo esc_attr($token); ?>"
                                         onerror="this.style.display='none'">
                                    <span class="token-fallback"><?php echo esc_html($token); ?></span>
                                </div>
                                <div class="token-info">
                                    <span class="token-name"><?php echo esc_html($token); ?></span>
                                    <span class="token-network"><?php echo esc_html($blockchain); ?></span>
                                </div>
                            </button>
                        <?php endforeach; ?>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Payment Details -->
            <div class="payment-details" id="payment-details" style="display: none;">
                <div class="payment-info-card">
                    <h3>Payment Information</h3>

                    <!-- Amount -->
                    <div class="info-row">
                        <label>Amount to Send</label>
                        <div class="amount-display">
                            <span class="amount-value" id="target-amount"><?php echo esc_html($template_data['payment']['targetAmount'] ?? '0'); ?></span>
                            <span class="amount-token" id="selected-token"><?php echo esc_html($template_data['payment']['token'] ?? ''); ?></span>
                            <button class="copy-btn" onclick="copyToClipboard('target-amount')">📋</button>
                        </div>
                    </div>

                    <!-- Address -->
                    <div class="info-row">
                        <label>Send to Address</label>
                        <div class="address-display">
                            <span class="address-value" id="wallet-address"><?php echo esc_html($template_data['payment']['address'] ?? ''); ?></span>
                            <button class="copy-btn" onclick="copyToClipboard('wallet-address')">📋</button>
                        </div>
                    </div>

                    <!-- Network -->
                    <div class="info-row">
                        <label>Network</label>
                        <span class="network-value" id="selected-network"><?php echo esc_html($template_data['payment']['network'] ?? ''); ?></span>
                    </div>

                    <!-- QR Code -->
                    <div class="qr-code-section">
                        <div class="qr-code" id="qr-code">
                            <?php if (!empty($template_data['payment']['qrCode'])): ?>
                                <img src="<?php echo esc_attr($template_data['payment']['qrCode']); ?>" alt="Payment QR Code">
                            <?php endif; ?>
                        </div>
                        <p class="qr-description">Scan with your wallet app</p>
                    </div>

                    <!-- Timer -->
                    <div class="payment-timer">
                        <span class="timer-label">Time remaining:</span>
                        <span class="timer-value" id="countdown-timer">30:00</span>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="payment-instructions">
                    <h4>Payment Instructions</h4>
                    <ol>
                        <li>Copy the exact amount and wallet address above</li>
                        <li>Open your cryptocurrency wallet</li>
                        <li>Send the <strong>exact amount</strong> to the provided address</li>
                        <li>Wait for confirmation (this page will update automatically)</li>
                    </ol>

                    <div class="warning-box">
                        <strong>⚠️ Important:</strong> You must send the exact amount shown above.
                        Sending a different amount may result in payment failure.
                    </div>
                </div>
            </div>

            <!-- Loading State -->
            <div class="loading-state" id="loading-state" style="display: none;">
                <div class="spinner"></div>
                <p>Updating payment information...</p>
            </div>
        </main>

        <!-- Footer -->
        <footer class="payment-footer">
            <div class="footer-content">
                <button class="btn-secondary" onclick="window.history.back()">← Back to Store</button>
                <div class="powered-by">
                    Powered by <strong>StablecoinPay</strong>
                </div>
            </div>
        </footer>
    </div>

    <!-- Hidden Data -->
    <script>
        window.stablecoinpayData = <?php echo json_encode($template_data); ?>;
    </script>

    <script src="<?php echo $template_data['plugin_url']; ?>assets/js/payment-page.js"></script>
</body>
</html>
```

**Deliverables**:
- ✅ Standalone payment page template
- ✅ Token/blockchain selection interface
- ✅ Responsive design with modern UI
- ✅ QR code display and copy functionality
- ✅ Payment timer and instructions

---

## Phase 4: Real-Time Status Updates

### 4.1 JavaScript Payment Monitoring

**Duration**: 3-4 days

**Objectives**:
- Implement real-time payment status updates
- Add automatic payment detection
- Create smooth UI transitions
- Handle payment completion flow

**Tasks**:

#### 4.1.1 Payment Page JavaScript
```javascript
// File: assets/js/payment-page.js
class StablecoinPayPaymentPage {
    constructor() {
        this.orderId = stablecoinpay_vars.order_id;
        this.paymentId = stablecoinpay_vars.payment_id;
        this.checkInterval = stablecoinpay_vars.check_interval;
        this.intervalId = null;
        this.countdownInterval = null;
        this.expiryTime = null;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeTokenSelection();
        this.startPaymentMonitoring();
        this.initializeCountdown();
    }

    setupEventListeners() {
        // Token selection
        document.querySelectorAll('.token-option').forEach(button => {
            button.addEventListener('click', (e) => {
                this.selectToken(e.currentTarget);
            });
        });

        // Copy buttons
        window.copyToClipboard = (elementId) => {
            const element = document.getElementById(elementId);
            if (element) {
                navigator.clipboard.writeText(element.textContent).then(() => {
                    this.showCopySuccess();
                });
            }
        };

        // Page visibility change
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.checkPaymentStatus();
            }
        });
    }

    async selectToken(button) {
        const blockchain = button.dataset.blockchain;
        const token = button.dataset.token;

        // Update UI
        document.querySelectorAll('.token-option').forEach(btn => {
            btn.classList.remove('selected');
        });
        button.classList.add('selected');

        // Show loading
        this.showLoading();

        try {
            // Update payment with selected token
            const response = await this.updatePaymentToken(blockchain, token);

            if (response.success) {
                this.updatePaymentDetails(response.data);
                this.showPaymentDetails();
            } else {
                this.showError('Failed to update payment method');
            }
        } catch (error) {
            this.showError('Network error occurred');
        } finally {
            this.hideLoading();
        }
    }

    async updatePaymentToken(blockchain, token) {
        const formData = new FormData();
        formData.append('action', 'stablecoinpay_update_token');
        formData.append('nonce', stablecoinpay_vars.nonce);
        formData.append('order_id', this.orderId);
        formData.append('payment_id', this.paymentId);
        formData.append('blockchain', blockchain);
        formData.append('token', token);

        const response = await fetch(stablecoinpay_vars.ajax_url, {
            method: 'POST',
            body: formData
        });

        return await response.json();
    }

    updatePaymentDetails(paymentData) {
        // Update amount
        document.getElementById('target-amount').textContent = paymentData.targetAmount;
        document.getElementById('selected-token').textContent = paymentData.token;

        // Update address
        document.getElementById('wallet-address').textContent = paymentData.address;

        // Update network
        document.getElementById('selected-network').textContent = paymentData.network;

        // Update QR code
        const qrCode = document.getElementById('qr-code');
        if (paymentData.qrCode) {
            qrCode.innerHTML = `<img src="${paymentData.qrCode}" alt="Payment QR Code">`;
        }

        // Update expiry time
        this.expiryTime = new Date(paymentData.expiryTime);
        this.updateCountdown();
    }

    showPaymentDetails() {
        document.getElementById('token-selection').style.display = 'none';
        document.getElementById('payment-details').style.display = 'block';
    }

    showLoading() {
        document.getElementById('loading-state').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loading-state').style.display = 'none';
    }

    startPaymentMonitoring() {
        this.checkPaymentStatus();
        this.intervalId = setInterval(() => {
            this.checkPaymentStatus();
        }, this.checkInterval);
    }

    async checkPaymentStatus() {
        try {
            const formData = new FormData();
            formData.append('action', 'stablecoinpay_check_status');
            formData.append('nonce', stablecoinpay_vars.nonce);
            formData.append('order_id', this.orderId);
            formData.append('payment_id', this.paymentId);

            const response = await fetch(stablecoinpay_vars.ajax_url, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.updatePaymentStatus(result.data);
            }
        } catch (error) {
            console.error('Status check failed:', error);
        }
    }

    updatePaymentStatus(statusData) {
        const status = statusData.status;
        const statusIndicator = document.getElementById('status-indicator');
        const statusTitle = document.getElementById('status-title');
        const statusDescription = document.getElementById('status-description');

        // Remove all status classes
        statusIndicator.className = 'status-indicator';

        switch (status) {
            case 'WAITING':
                statusIndicator.classList.add('waiting');
                statusIndicator.querySelector('.status-icon').textContent = '⏳';
                statusTitle.textContent = 'Waiting for Payment';
                statusDescription.textContent = 'Please send the exact amount to the address below';
                break;

            case 'DETECTED':
                statusIndicator.classList.add('detected');
                statusIndicator.querySelector('.status-icon').textContent = '🔍';
                statusTitle.textContent = 'Payment Detected';
                statusDescription.textContent = 'Transaction found, waiting for confirmation...';
                break;

            case 'CONFIRMED':
                statusIndicator.classList.add('confirmed');
                statusIndicator.querySelector('.status-icon').textContent = '✅';
                statusTitle.textContent = 'Payment Confirmed';
                statusDescription.textContent = 'Your payment has been successfully processed!';
                this.handlePaymentSuccess(statusData);
                break;

            case 'EXPIRED':
                statusIndicator.classList.add('expired');
                statusIndicator.querySelector('.status-icon').textContent = '⏰';
                statusTitle.textContent = 'Payment Expired';
                statusDescription.textContent = 'This payment has expired. Please create a new order.';
                this.stopMonitoring();
                break;

            case 'CANCELED':
                statusIndicator.classList.add('canceled');
                statusIndicator.querySelector('.status-icon').textContent = '❌';
                statusTitle.textContent = 'Payment Canceled';
                statusDescription.textContent = 'This payment has been canceled.';
                this.stopMonitoring();
                break;
        }
    }

    handlePaymentSuccess(statusData) {
        this.stopMonitoring();

        // Show success animation
        this.showSuccessAnimation();

        // Redirect after delay
        setTimeout(() => {
            window.location.href = statusData.redirect_url || '/checkout/order-received/';
        }, 3000);
    }

    showSuccessAnimation() {
        // Add success animation class
        document.body.classList.add('payment-success');

        // Create confetti effect (optional)
        this.createConfetti();
    }

    createConfetti() {
        // Simple confetti animation
        for (let i = 0; i < 50; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';
            confetti.style.left = Math.random() * 100 + '%';
            confetti.style.animationDelay = Math.random() * 3 + 's';
            confetti.style.backgroundColor = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][Math.floor(Math.random() * 5)];
            document.body.appendChild(confetti);

            setTimeout(() => confetti.remove(), 3000);
        }
    }

    initializeCountdown() {
        if (window.stablecoinpayData.payment.expiryTime) {
            this.expiryTime = new Date(window.stablecoinpayData.payment.expiryTime);
            this.updateCountdown();
            this.countdownInterval = setInterval(() => {
                this.updateCountdown();
            }, 1000);
        }
    }

    updateCountdown() {
        if (!this.expiryTime) return;

        const now = new Date();
        const timeLeft = this.expiryTime - now;

        if (timeLeft <= 0) {
            document.getElementById('countdown-timer').textContent = '00:00';
            this.stopMonitoring();
            return;
        }

        const minutes = Math.floor(timeLeft / 60000);
        const seconds = Math.floor((timeLeft % 60000) / 1000);

        document.getElementById('countdown-timer').textContent =
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    stopMonitoring() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }

        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    }

    showCopySuccess() {
        // Show temporary success message
        const message = document.createElement('div');
        message.className = 'copy-success';
        message.textContent = 'Copied to clipboard!';
        document.body.appendChild(message);

        setTimeout(() => message.remove(), 2000);
    }

    showError(message) {
        // Show error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);

        setTimeout(() => errorDiv.remove(), 5000);
    }

    initializeTokenSelection() {
        // If payment already has token selected, show payment details
        if (window.stablecoinpayData.payment.token && window.stablecoinpayData.payment.address) {
            this.showPaymentDetails();

            // Select the current token
            const currentToken = window.stablecoinpayData.payment.token;
            const currentBlockchain = window.stablecoinpayData.payment.blockchain;

            document.querySelectorAll('.token-option').forEach(button => {
                if (button.dataset.token === currentToken && button.dataset.blockchain === currentBlockchain) {
                    button.classList.add('selected');
                }
            });
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new StablecoinPayPaymentPage();
});
```

**Deliverables**:
- ✅ Real-time payment status monitoring
- ✅ Automatic payment detection and updates
- ✅ Smooth UI transitions and animations
- ✅ Payment completion flow with redirect
- ✅ Countdown timer and expiry handling

---

## Phase 5: UI/UX Optimization

### 5.1 CSS Styling and Responsive Design

**Duration**: 2-3 days

**Objectives**:
- Create modern, professional UI design
- Ensure mobile responsiveness
- Add smooth animations and transitions
- Optimize for accessibility

**Tasks**:

#### 5.1.1 Payment Page CSS
```css
/* File: assets/css/payment-page.css */
:root {
    --primary-color: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --background: #f9fafb;
    --card-background: #ffffff;
    --border-color: #e5e7eb;
    --border-radius: 12px;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.stablecoinpay-payment-page {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--background);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.payment-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Header */
.payment-header {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.site-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.order-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.order-number {
    font-size: 14px;
    color: var(--text-secondary);
}

.order-total {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

/* Main Content */
.payment-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* Payment Status */
.payment-status {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.status-indicator.waiting {
    background: #fef3c7;
    border: 2px solid #f59e0b;
}

.status-indicator.detected {
    background: #dbeafe;
    border: 2px solid #3b82f6;
}

.status-indicator.confirmed {
    background: #d1fae5;
    border: 2px solid #10b981;
}

.status-indicator.expired,
.status-indicator.canceled {
    background: #fee2e2;
    border: 2px solid #ef4444;
}

.status-icon {
    font-size: 32px;
    min-width: 48px;
    text-align: center;
}

.status-text h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
}

.status-text p {
    color: var(--text-secondary);
    font-size: 14px;
}

/* Token Selection */
.token-selection {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow);
}

.token-selection h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
}

.token-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.token-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: var(--card-background);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.token-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.token-option.selected {
    border-color: var(--primary-color);
    background: #eff6ff;
}

.token-icon {
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.token-icon img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.token-fallback {
    font-size: 12px;
    font-weight: 600;
    color: var(--primary-color);
}

.token-info {
    flex: 1;
}

.token-name {
    display: block;
    font-weight: 600;
    font-size: 16px;
}

.token-network {
    display: block;
    font-size: 12px;
    color: var(--text-secondary);
    text-transform: uppercase;
}

/* Payment Details */
.payment-details {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.payment-info-card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow);
}

.payment-info-card h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
}

.info-row {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 20px;
}

.info-row label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
}

.amount-display,
.address-display {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.amount-value,
.address-value {
    flex: 1;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 16px;
    font-weight: 600;
    word-break: break-all;
}

.amount-token {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.copy-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s ease;
}

.copy-btn:hover {
    background: #1d4ed8;
}

.network-value {
    font-weight: 600;
    color: var(--primary-color);
    text-transform: uppercase;
}

/* QR Code */
.qr-code-section {
    text-align: center;
    margin: 24px 0;
}

.qr-code {
    display: inline-block;
    padding: 16px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 12px;
}

.qr-code img {
    max-width: 200px;
    height: auto;
}

.qr-description {
    font-size: 14px;
    color: var(--text-secondary);
}

/* Timer */
.payment-timer {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 16px;
    background: #fef3c7;
    border-radius: 8px;
    margin-top: 20px;
}

.timer-label {
    font-size: 14px;
    color: var(--text-secondary);
}

.timer-value {
    font-size: 18px;
    font-weight: 600;
    font-family: 'Monaco', 'Menlo', monospace;
    color: var(--warning-color);
}

/* Instructions */
.payment-instructions {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow);
}

.payment-instructions h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
}

.payment-instructions ol {
    margin-bottom: 20px;
    padding-left: 20px;
}

.payment-instructions li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.warning-box {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 16px;
    font-size: 14px;
}

/* Loading State */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 40px;
    background: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Footer */
.payment-footer {
    margin-top: auto;
    padding-top: 24px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.btn-secondary {
    background: var(--card-background);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-secondary:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.powered-by {
    font-size: 12px;
    color: var(--text-secondary);
}

/* Success Animation */
.payment-success .status-indicator.confirmed {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Confetti */
.confetti {
    position: fixed;
    width: 10px;
    height: 10px;
    background: #ff6b6b;
    animation: confetti-fall 3s linear forwards;
    z-index: 1000;
}

@keyframes confetti-fall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

/* Copy Success Message */
.copy-success {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--success-color);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Error Message */
.error-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--error-color);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .payment-container {
        padding: 16px;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .order-info {
        align-items: center;
    }

    .token-grid {
        grid-template-columns: 1fr;
    }

    .amount-display,
    .address-display {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .qr-code img {
        max-width: 150px;
    }
}

@media (max-width: 480px) {
    .payment-container {
        padding: 12px;
    }

    .payment-info-card,
    .payment-instructions {
        padding: 16px;
    }

    .site-title {
        font-size: 20px;
    }

    .order-total {
        font-size: 18px;
    }
}
```

**Deliverables**:
- ✅ Modern, professional UI design
- ✅ Mobile-responsive layout
- ✅ Smooth animations and transitions
- ✅ Accessibility optimizations
- ✅ Success animations and feedback

---

## Phase 6: Security & Error Handling

### 6.1 Security Implementation

**Duration**: 2-3 days

**Objectives**:
- Implement comprehensive security measures
- Add input validation and sanitization
- Secure API communications
- Handle edge cases and errors gracefully

**Tasks**:

#### 6.1.1 Security Enhancements
```php
// File: includes/class-stablecoinpay-security.php
<?php
class StablecoinPay_Security {

    public static function validate_payment_access($order_id, $payment_id) {
        // Verify order exists
        $order = wc_get_order($order_id);
        if (!$order) {
            return false;
        }

        // Check if user has access to this order
        $current_user_id = get_current_user_id();
        $order_user_id = $order->get_user_id();

        // Allow access if:
        // 1. User owns the order
        // 2. User is admin
        // 3. Order key matches (for guest orders)
        if ($current_user_id && $current_user_id === $order_user_id) {
            return true;
        }

        if (current_user_can('manage_woocommerce')) {
            return true;
        }

        // Check order key for guest orders
        $order_key = sanitize_text_field($_GET['key'] ?? '');
        if ($order_key && $order->get_order_key() === $order_key) {
            return true;
        }

        return false;
    }

    public static function sanitize_payment_data($data) {
        $sanitized = array();

        $sanitized['amount'] = floatval($data['amount'] ?? 0);
        $sanitized['currency'] = sanitize_text_field($data['currency'] ?? 'USD');
        $sanitized['token'] = sanitize_text_field($data['token'] ?? 'USDT');
        $sanitized['blockchain'] = sanitize_text_field($data['blockchain'] ?? 'ETHEREUM');
        $sanitized['network'] = sanitize_text_field($data['network'] ?? 'MAINNET');

        // Validate amount
        if ($sanitized['amount'] < 0.01) {
            throw new Exception('Amount must be at least 0.01');
        }

        // Validate currency
        $allowed_currencies = array('USD', 'EUR', 'GBP', 'INR', 'CNY', 'RUB');
        if (!in_array($sanitized['currency'], $allowed_currencies)) {
            throw new Exception('Invalid currency');
        }

        // Validate token and blockchain combination
        $supported_tokens = array(
            'ETHEREUM' => array('USDT', 'USDC', 'DAI', 'PYUSD'),
            'BSC' => array('USDT', 'USDC', 'DAI', 'BUSD'),
            'TRON' => array('USDT', 'USDC'),
            'SOLANA' => array('USDT', 'USDC'),
            'TON' => array('USDT', 'USDC')
        );

        if (!isset($supported_tokens[$sanitized['blockchain']]) ||
            !in_array($sanitized['token'], $supported_tokens[$sanitized['blockchain']])) {
            throw new Exception('Invalid token/blockchain combination');
        }

        return $sanitized;
    }

    public static function verify_nonce($action, $nonce) {
        return wp_verify_nonce($nonce, $action);
    }

    public static function rate_limit_check($action, $identifier, $limit = 10, $window = 3600) {
        $transient_key = "stablecoinpay_rate_limit_{$action}_{$identifier}";
        $attempts = get_transient($transient_key) ?: 0;

        if ($attempts >= $limit) {
            return false;
        }

        set_transient($transient_key, $attempts + 1, $window);
        return true;
    }
}
```

---

## File Structure

```
stablecoinpay-gateway/
├── stablecoinpay-gateway.php              # Main plugin file (HPOS compatible)
├── readme.txt                             # WordPress plugin readme
├── LICENSE                                # Plugin license
├── includes/                              # PHP classes
│   ├── class-wc-stablecoinpay-gateway.php # WooCommerce gateway (HPOS compatible)
│   ├── class-stablecoinpay-settings.php   # Dedicated settings page
│   ├── class-stablecoinpay-admin.php      # Admin interface and AJAX handlers
│   ├── class-stablecoinpay-api.php        # API client (configurable URL)
│   ├── class-stablecoinpay-callback.php   # Callback handler
│   ├── class-stablecoinpay-payment-page.php # Payment page handler
│   ├── class-stablecoinpay-payment-manager.php # Payment data management (HPOS compatible)
│   ├── class-stablecoinpay-database.php   # Database operations
│   ├── class-stablecoinpay-security.php   # Security utilities
│   └── class-stablecoinpay-ajax.php       # AJAX handlers
├── templates/                             # Template files
│   └── payment-page.php                   # Payment page template
├── assets/                                # Frontend assets
│   ├── css/
│   │   └── payment-page.css               # Payment page styles
│   ├── js/
│   │   └── payment-page.js                # Payment page JavaScript
│   └── images/
│       ├── tokens/                        # Token icons
│       │   ├── usdt.png
│       │   ├── usdc.png
│       │   ├── dai.png
│       │   ├── pyusd.png
│       │   └── busd.png
│       └── stablecoinpay-icon.png         # Plugin icon
├── languages/                             # Translation files
│   └── stablecoinpay-gateway.pot          # Translation template
└── tests/                                 # Test files
    ├── unit/
    ├── integration/
    └── e2e/
```

---

## Database Schema

### Custom Tables

#### stablecoinpay_payments
```sql
CREATE TABLE wp_stablecoinpay_payments (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    order_id bigint(20) NOT NULL,
    payment_id varchar(255) NOT NULL,
    status varchar(50) NOT NULL DEFAULT 'pending',
    token varchar(10) NOT NULL,
    blockchain varchar(20) NOT NULL,
    network varchar(20) NOT NULL,
    target_amount decimal(20,8) NOT NULL,
    request_amount decimal(20,8) NOT NULL,
    fiat_amount decimal(10,2) NOT NULL,
    fiat_currency varchar(3) NOT NULL,
    wallet_address varchar(255) NOT NULL,
    tx_hash varchar(255) DEFAULT NULL,
    callback_url text NOT NULL,
    metadata text DEFAULT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY payment_id (payment_id),
    KEY order_id (order_id),
    KEY status (status)
);
```

#### stablecoinpay_transaction_logs
```sql
CREATE TABLE wp_stablecoinpay_transaction_logs (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    payment_id varchar(255) NOT NULL,
    event_type varchar(50) NOT NULL,
    event_data text DEFAULT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY payment_id (payment_id),
    KEY event_type (event_type)
);
```

### WooCommerce Order Meta Fields

- `_stablecoinpay_payment_id`: StablecoinPay payment ID
- `_stablecoinpay_status`: Current payment status
- `_stablecoinpay_tx_hash`: Transaction hash
- `_stablecoinpay_last_update`: Last status update timestamp
- `_stablecoinpay_payment_data`: Full payment data (JSON)

---

## Configuration Guidelines

### Plugin Settings

1. **Basic Configuration**
   - Enable/Disable gateway
   - Payment method title and description
   - Test mode toggle

2. **API Configuration**
   - StablecoinPay API key
   - StablecoinPay API secret
   - API base URL (for custom installations)

3. **Payment Settings**
   - Default token (USDT, USDC, etc.)
   - Default blockchain (Ethereum, BSC, etc.)
   - Default network (Mainnet/Testnet)
   - Payment expiry time (minutes)

4. **Advanced Settings**
   - Callback URL customization
   - IP whitelist for callbacks
   - Debug logging enable/disable
   - Custom CSS for payment page

### Environment Variables

```php
// wp-config.php additions
define('STABLECOINPAY_API_KEY', 'your_api_key_here');
define('STABLECOINPAY_API_SECRET', 'your_api_secret_here');
define('STABLECOINPAY_DEBUG', true);
define('STABLECOINPAY_TEST_MODE', true);
```

---

## Testing Procedures

### 1. Unit Testing

**Test Coverage Areas**:
- API client functionality
- Payment data validation
- Security functions
- Database operations

**Example Test**:
```php
// tests/unit/test-api-client.php
class Test_StablecoinPay_API extends WP_UnitTestCase {

    public function test_create_payment_success() {
        $api = new StablecoinPay_API('test_key', 'test_secret', true);

        $payment_data = array(
            'amount' => 10.00,
            'currency' => 'USD',
            'token' => 'USDT',
            'blockchain' => 'ETHEREUM',
            'network' => 'TESTNET'
        );

        // Mock API response
        $this->mock_api_response(200, array(
            'success' => true,
            'data' => array(
                'id' => 123,
                'status' => 'WAITING',
                'targetAmount' => '10.0237'
            )
        ));

        $result = $api->create_payment($payment_data);

        $this->assertEquals(123, $result['id']);
        $this->assertEquals('WAITING', $result['status']);
    }
}
```

### 2. Integration Testing

**Test Scenarios**:
- Complete payment flow from checkout to confirmation
- Callback URL handling and order status updates
- Payment page functionality and UI interactions
- Error handling and edge cases

### 3. End-to-End Testing

**Test Cases**:
1. **Successful Payment Flow**
   - Customer completes checkout
   - Selects cryptocurrency payment
   - Chooses token/blockchain
   - Sends payment
   - Receives confirmation

2. **Payment Expiry**
   - Payment expires before completion
   - Order status updated correctly
   - Customer redirected appropriately

3. **Network Errors**
   - API unavailable scenarios
   - Callback delivery failures
   - Recovery mechanisms

### 4. Security Testing

**Security Checks**:
- SQL injection prevention
- XSS protection
- CSRF token validation
- Access control verification
- Rate limiting effectiveness

---

## Deployment Guide

### 1. Pre-Deployment Checklist

**Requirements Verification**:
- ✅ WordPress 5.0+
- ✅ WooCommerce 3.0+
- ✅ PHP 7.4+
- ✅ MySQL 5.6+
- ✅ SSL certificate installed
- ✅ StablecoinPay API credentials

**Plugin Preparation**:
- ✅ All files uploaded to server
- ✅ Database tables created
- ✅ Rewrite rules flushed
- ✅ Assets compiled and minified
- ✅ Translation files generated

### 2. Installation Steps

#### Step 1: Upload Plugin Files
```bash
# Via WordPress admin
1. Go to Plugins > Add New
2. Click "Upload Plugin"
3. Select stablecoinpay-gateway.zip
4. Click "Install Now"
5. Activate the plugin

# Via FTP/SFTP
1. Upload stablecoinpay-gateway/ to /wp-content/plugins/
2. Go to Plugins in WordPress admin
3. Activate "WooCommerce StablecoinPay Gateway"
```

#### Step 2: Configure Plugin Settings
```php
// Navigate to: WooCommerce > Settings > Payments > StablecoinPay

1. Enable the payment method
2. Set title: "Cryptocurrency Payment"
3. Set description: "Pay with USDT, USDC, DAI, or other stablecoins"
4. Configure API credentials:
   - API Key: [Your StablecoinPay API Key]
   - API Secret: [Your StablecoinPay API Secret]
5. Set test mode (for initial testing)
6. Save changes
```

#### Step 3: Test Configuration
```bash
# Test payment creation
1. Create a test order
2. Select StablecoinPay as payment method
3. Verify redirect to payment page
4. Check payment details display correctly
5. Test callback URL functionality
```

### 3. Production Deployment

#### Environment Configuration
```php
// wp-config.php - Production settings
define('STABLECOINPAY_API_KEY', 'prod_api_key_here');
define('STABLECOINPAY_API_SECRET', 'prod_api_secret_here');
define('STABLECOINPAY_DEBUG', false);
define('STABLECOINPAY_TEST_MODE', false);

// Security headers
define('FORCE_SSL_ADMIN', true);
define('DISALLOW_FILE_EDIT', true);
```

#### Server Configuration
```nginx
# Nginx configuration for payment page
location /stablecoinpay-payment/ {
    try_files $uri $uri/ /index.php?$args;
}

# Callback URL protection
location /wc-api/stablecoinpay/callback/ {
    # Rate limiting
    limit_req zone=api burst=10 nodelay;

    # IP whitelisting (optional)
    # allow ***********/24;
    # deny all;

    try_files $uri $uri/ /index.php?$args;
}
```

#### Performance Optimization
```php
// Caching configuration
1. Exclude payment pages from caching:
   - /stablecoinpay-payment/*
   - /wc-api/stablecoinpay/*

2. Enable object caching for payment data
3. Optimize database queries with proper indexing
4. Minify CSS/JS assets
5. Enable GZIP compression
```

### 4. Monitoring and Maintenance

#### Log Monitoring
```php
// Enable debug logging
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);

// Monitor log files:
// - /wp-content/debug.log
// - /wp-content/uploads/wc-logs/
```

#### Health Checks
```bash
# Daily checks
1. Verify API connectivity
2. Check callback URL accessibility
3. Monitor payment success rates
4. Review error logs
5. Test payment page functionality
```

#### Database Maintenance
```sql
-- Weekly cleanup of old logs
DELETE FROM wp_stablecoinpay_transaction_logs
WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Monthly payment data archival
-- Archive completed payments older than 90 days
```

---

## Troubleshooting

### Common Issues

#### 1. Payment Page Not Loading

**Symptoms**: 404 error when accessing payment page

**Solutions**:
```php
// Flush rewrite rules
1. Go to Settings > Permalinks
2. Click "Save Changes" (no changes needed)
3. Or programmatically:
flush_rewrite_rules();
```

#### 2. Callback URL Not Working

**Symptoms**: Payments not updating order status

**Debugging Steps**:
```bash
# Check callback URL accessibility
curl -X POST https://yoursite.com/wc-api/stablecoinpay/callback/123 \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# Check server logs for errors
tail -f /var/log/nginx/error.log
tail -f /wp-content/debug.log
```

#### 3. API Connection Issues

**Symptoms**: "Payment creation failed" errors

**Solutions**:
```php
// Test API connectivity
$api = new StablecoinPay_API($api_key, $api_secret, true);
try {
    $test_payment = $api->create_payment(array(
        'amount' => 1.00,
        'currency' => 'USD',
        'token' => 'USDT',
        'blockchain' => 'ETHEREUM',
        'network' => 'TESTNET'
    ));
    echo "API connection successful";
} catch (Exception $e) {
    echo "API error: " . $e->getMessage();
}
```

#### 4. JavaScript Errors

**Symptoms**: Payment page not updating status

**Debugging**:
```javascript
// Check browser console for errors
// Verify AJAX endpoints are accessible
// Test with browser developer tools
```

#### 5. Database Issues

**Symptoms**: Payment data not saving

**Solutions**:
```sql
-- Check table existence
SHOW TABLES LIKE 'wp_stablecoinpay_%';

-- Verify table structure
DESCRIBE wp_stablecoinpay_payments;

-- Check for data
SELECT * FROM wp_stablecoinpay_payments ORDER BY created_at DESC LIMIT 5;
```

### Error Codes Reference

| Error Code | Description | Solution |
|------------|-------------|----------|
| `PAYMENT_001` | Invalid API credentials | Check API key and secret |
| `PAYMENT_002` | Payment creation failed | Verify API connectivity |
| `PAYMENT_003` | Invalid payment parameters | Check amount and currency |
| `PAYMENT_004` | Order not found | Verify order ID |
| `PAYMENT_005` | Access denied | Check user permissions |
| `PAYMENT_006` | Callback verification failed | Check signature validation |
| `PAYMENT_007` | Database error | Check database connectivity |
| `PAYMENT_008` | Rate limit exceeded | Implement backoff strategy |

### Support Resources

#### Debug Information Collection
```php
// Add to functions.php for debugging
function stablecoinpay_debug_info() {
    if (!current_user_can('manage_options')) return;

    echo "<h3>StablecoinPay Debug Info</h3>";
    echo "<pre>";
    echo "WordPress Version: " . get_bloginfo('version') . "\n";
    echo "WooCommerce Version: " . WC()->version . "\n";
    echo "PHP Version: " . PHP_VERSION . "\n";
    echo "Plugin Version: " . STABLECOINPAY_VERSION . "\n";
    echo "Test Mode: " . (defined('STABLECOINPAY_TEST_MODE') && STABLECOINPAY_TEST_MODE ? 'Yes' : 'No') . "\n";
    echo "Debug Mode: " . (defined('STABLECOINPAY_DEBUG') && STABLECOINPAY_DEBUG ? 'Yes' : 'No') . "\n";
    echo "</pre>";
}
add_action('wp_dashboard_setup', function() {
    wp_add_dashboard_widget('stablecoinpay_debug', 'StablecoinPay Debug', 'stablecoinpay_debug_info');
});
```

#### Performance Monitoring
```php
// Monitor API response times
function stablecoinpay_log_api_performance($endpoint, $duration) {
    if ($duration > 5) { // Log slow requests (>5 seconds)
        error_log("StablecoinPay API slow response: {$endpoint} took {$duration}s");
    }
}
```

---

## Project Timeline Summary

| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| **Phase 1** | 3-4 days | HPOS-compatible plugin foundation, comprehensive settings page, WooCommerce integration |
| **Phase 2** | 3-4 days | Configurable API integration, callback handling |
| **Phase 3** | 4-5 days | Self-hosted payment page, UI components |
| **Phase 4** | 3-4 days | Real-time updates, JavaScript functionality |
| **Phase 5** | 2-3 days | UI/UX optimization, responsive design |
| **Phase 6** | 2-3 days | Security implementation, error handling |
| **Testing** | 3-4 days | HPOS compatibility testing, comprehensive testing and bug fixes |
| **Deployment** | 1-2 days | Production deployment and monitoring |

**Total Estimated Duration**: 21-29 days

---

## Success Criteria

### Functional Requirements ✅
- ✅ Complete WooCommerce integration
- ✅ Self-hosted payment page with modern UI
- ✅ Support for all StablecoinPay tokens and blockchains
- ✅ Real-time payment status updates
- ✅ Callback URL integration for order updates
- ✅ Mobile-responsive design
- ✅ QR code generation and display
- ✅ Payment timer and expiry handling

### Technical Requirements ✅
- ✅ **HPOS Compatibility**: Full support for WooCommerce High-Performance Order Storage
- ✅ **Configurable Settings**: All API URLs and settings manageable from admin interface
- ✅ **Dedicated Settings Page**: Comprehensive admin settings page with validation
- ✅ **Secure API Communication**: Configurable API endpoints with proper authentication
- ✅ **Proper Error Handling**: Debug logging and comprehensive error management
- ✅ **Database Optimization**: HPOS-compatible data storage and indexing
- ✅ **Rate Limiting**: Security measures and API rate limiting
- ✅ **Comprehensive Test Coverage**: Including HPOS compatibility testing
- ✅ **Performance Optimization**: Efficient data handling and caching

### User Experience Requirements ✅
- ✅ Intuitive token/blockchain selection
- ✅ Clear payment instructions
- ✅ Smooth animations and transitions
- ✅ Copy-to-clipboard functionality
- ✅ Success animations and feedback
- ✅ Accessibility compliance

This comprehensive implementation plan provides a complete roadmap for developing a professional WooCommerce StablecoinPay plugin with all the specified features and requirements.
