# StablecoinPay API Integration Guide

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Payment Request Flow](#payment-request-flow)
4. [Payment Status & Monitoring](#payment-status--monitoring)
5. [Webhook System](#webhook-system)
6. [Error Handling](#error-handling)
7. [Rate Limiting](#rate-limiting)
8. [Integration Examples](#integration-examples)
9. [Testing Guide](#testing-guide)
10. [Troubleshooting](#troubleshooting)

---

## Overview

StablecoinPay is a payment acceptance system that supports stablecoins across multiple blockchains. The system uses **unique payment amounts** and **time correlation** for payment attribution, eliminating the need for payment memos or complex address generation.

### Supported Networks & Tokens

| Blockchain | Network | Supported Tokens | Native Token |
|------------|---------|------------------|---------------|
| Ethereum   | Mainnet/Testnet | USDT, USDC, DAI, PYUSD | ETH |
| BSC        | Mainnet/Testnet | USDT, USDC, DAI, BUSD | BNB |
| Tron       | Mainnet/Testnet | USDT, USDC | TRX |
| Solana     | Mainnet/Testnet | USDT, USDC | SOL |
| TON        | Mainnet/Testnet | USDT, USDC | TON |

### Key Features

- **Static wallet addresses** - No need to generate new addresses per payment
- **Unique amount attribution** - Each payment gets a slightly modified amount (e.g., $10.00 becomes $10.0237)
- **Real-time monitoring** - Automatic transaction detection and confirmation tracking
- **Webhook notifications** - Instant payment status updates via configurable webhook endpoints
- **Manual verification** - Fallback system for missed transactions
- **IP whitelisting** - Enhanced security for payment endpoints

---

## Authentication

StablecoinPay uses **API Key authentication** for payment-related endpoints. JWT authentication is used for administrative functions.

### API Key Structure

API keys consist of two parts:
- **API Key ID**: Public identifier
- **API Secret**: Private key for request signing

### Required Headers

```http
X-API-Key: your_api_key_id
X-API-Secret: your_api_secret
Content-Type: application/json
```

### Authentication Example

```bash
curl -X POST "http://localhost:3000/api/v1/payments" \
  -H "X-API-Key: YOUR_API_KEY_HERE" \
  -H "X-API-Secret: YOUR_API_SECRET_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 10.00,
    "currency": "USD",
    "token": "USDT",
    "blockchain": "ETHEREUM",
    "network": "MAINNET"
  }'
```

### IP Whitelisting

Payment endpoints support IP whitelisting for enhanced security:

```json
{
  "payment.ip_whitelist": [
    "*************",
    "10.0.0.0/8",
    "0.0.0.0/0"
  ]
}
```

---

## Payment Request Flow

### Step 1: Create Payment Request

**Endpoint:** `POST /api/v1/payments`

**Required Parameters:**

```json
{
  "amount": 10.00,           // Fiat amount (minimum 0.01)
  "currency": "USD",         // Fiat currency (USD, EUR, GBP, INR, CNY, RUB)
  "token": "USDT",          // Stablecoin token
  "blockchain": "ETHEREUM",  // Target blockchain
  "network": "MAINNET"      // Network type
}
```

**Optional Parameters:**

```json
{
  "webhookEndpointId": 123,                               // Webhook endpoint ID (optional - uses default if not provided)
  "metadata": {                                            // Custom data
    "orderId": "12345",
    "customerId": "user_456"
  },
  "expiryMinutes": 30                                     // Payment expiry (1-1440 minutes)
}
```

### Step 2: Payment Response

```json
{
  "success": true,
  "data": {
    "id": 123,
    "targetAmount": "10.0237",      // Exact amount to pay
    "requestAmount": "10.0000",     // Original requested amount
    "fiatAmount": "10.00",
    "fiatCurrency": "USD",
    "exchangeRate": "1.0023",
    "token": "USDT",
    "blockchain": "ETHEREUM",
    "network": "MAINNET",
    "address": "******************************************",
    "status": "WAITING",
    "expiryTime": "2024-01-15T10:30:00.000Z",
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "webhookEndpointId": 123,
    "metadata": {
      "orderId": "12345",
      "customerId": "user_456"
    },
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  },
  "message": "Payment created successfully",
  "timestamp": "2024-01-15T10:00:00.000Z"
}
```

### Step 3: Display Payment Information

Present the following to your customer:

1. **Payment Address**: `data.address`
2. **Exact Amount**: `data.targetAmount` (critical - must be exact)
3. **Token**: `data.token`
4. **Network**: `data.network`
5. **QR Code**: `data.qrCode` (optional)
6. **Expiry Time**: `data.expiryTime`

---

## Payment Status & Monitoring

### Check Payment Status

**Endpoint:** `GET /api/v1/payments/{id}`

```bash
curl -X GET "http://localhost:3000/api/v1/payments/123" \
  -H "X-API-Key: your_api_key" \
  -H "X-API-Secret: your_secret"
```

### Payment Status Values

| Status | Description |
|--------|-------------|
| `WAITING` | Payment created, waiting for transaction |
| `DETECTED` | Transaction found but not yet confirmed |
| `CONFIRMED` | Payment confirmed and completed |
| `EXPIRED` | Payment expired without completion |
| `CANCELED` | Payment manually canceled |
| `PENDING_VERIFICATION` | Requires manual verification |

### Manual Transaction Check

Force check for transactions:

**Endpoint:** `POST /api/v1/payments/{id}/check`

```bash
curl -X POST "http://localhost:3000/api/v1/payments/123/check" \
  -H "X-API-Key: your_api_key" \
  -H "X-API-Secret: your_secret"
```

### List Payments with Filters

**Endpoint:** `GET /api/v1/payments`

```bash
curl -X GET "http://localhost:3000/api/v1/payments?status=CONFIRMED&page=1&limit=20" \
  -H "X-API-Key: your_api_key" \
  -H "X-API-Secret: your_secret"
```

**Query Parameters:**

```json
{
  "status": "CONFIRMED",        // Filter by status
  "token": "USDT",             // Filter by token
  "network": "MAINNET",        // Filter by network
  "fromDate": "2024-01-01",    // Date range start
  "toDate": "2024-01-31",      // Date range end
  "page": 1,                   // Page number
  "limit": 20                  // Items per page (max 100)
}
```

---

## Webhook System

### Webhook Endpoint Management

StablecoinPay uses a **webhook endpoint system** where users can create and manage multiple webhook endpoints for different purposes. Each payment can specify which webhook endpoint to use, or use a default endpoint if none is specified.

### Creating Webhook Endpoints

**Endpoint:** `POST /api/admin/users/{userId}/webhooks/endpoints`

```json
{
  "name": "My Store Webhooks",
  "url": "https://yoursite.com/payment-webhook",
  "events": [
    "payment.created",
    "payment.completed",
    "payment.failed",
    "payment.expired",
    "transaction.detected",
    "transaction.confirmed"
  ],
  "domain": "yoursite.com",
  "timeout": 30000,
  "maxRetries": 3,
  "retryDelay": 5000,
  "retryBackoff": "exponential",
  "verifySSL": true
}
```

### Getting Webhook Endpoints

**Endpoint:** `GET /api/admin/users/{userId}/webhooks/endpoints`

```bash
curl -X GET "http://localhost:3000/api/admin/users/1/webhooks/endpoints" \
  -H "X-API-Key: your_api_key" \
  -H "X-API-Secret: your_secret"
```

### Using Webhook Endpoints in Payments

Configure webhook endpoint in your payment requests:

```json
{
  "amount": 10.00,
  "currency": "USD",
  "token": "USDT",
  "blockchain": "ETHEREUM",
  "network": "MAINNET",
  "webhookEndpointId": 123,
  "metadata": {
    "orderId": "12345",
    "customerId": "user_456"
  }
}
```

**Note:** If `webhookEndpointId` is not provided, the system will automatically use or create a default webhook endpoint for the user.

### Managing Webhook Endpoints

#### Update Webhook Endpoint

**Endpoint:** `PUT /api/admin/users/{userId}/webhooks/endpoints/{endpointId}`

```json
{
  "name": "Updated Webhook Name",
  "url": "https://newsite.com/payment-webhook",
  "events": ["payment.completed", "payment.failed"],
  "isActive": true
}
```

#### Delete Webhook Endpoint

**Endpoint:** `DELETE /api/admin/users/{userId}/webhooks/endpoints/{endpointId}`

#### Test Webhook Endpoint

**Endpoint:** `POST /api/admin/users/{userId}/webhooks/endpoints/{endpointId}/test`

```json
{
  "payload": {
    "id": 123,
    "status": "COMPLETED",
    "amount": "10.00"
  }
}
```

#### Get Webhook Deliveries

**Endpoint:** `GET /api/admin/users/{userId}/webhooks/endpoints/{endpointId}/deliveries`

Query parameters:
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20)
- `status` - Filter by success/failed
- `eventType` - Filter by event type
- `fromDate` - Filter from date
- `toDate` - Filter to date

### Webhook Payload Structure

When a payment status changes, StablecoinPay sends a POST request to the configured webhook endpoint:

```json
{
  "id": 123,
  "status": "CONFIRMED",
  "targetAmount": "10.0237",
  "requestAmount": "10.0000",
  "token": "USDT",
  "network": "MAINNET",
  "wallet": "******************************************",
  "timestamp": "2024-01-15T10:15:00.000Z",
  "transaction": {
    "txHash": "0x1234567890abcdef...",
    "amount": "10.0237",
    "confirmations": 12,
    "blockNumber": 18500000
  }
}
```

**Note:** The webhook payload structure follows the payment service implementation and does not include the original payment metadata. To access the original metadata, you should store the payment ID and retrieve the full payment details using the GET endpoint when processing the webhook.

### Metadata Handling Best Practices

Since webhook payloads don't include the original payment metadata, follow these patterns:

1. **Use Unique Webhook URLs**: Include identifiers in the webhook URL path (e.g., `/payment-webhook/{orderId}`)
2. **Store Payment ID Locally**: When creating a payment, store the payment ID with your order/transaction record
3. **Retrieve Full Details in Webhook**: Use the payment ID to fetch complete payment details including metadata
4. **Cache Payment Details**: Consider caching payment details to avoid repeated API calls
5. **Handle Missing Metadata**: Always check if metadata exists before accessing properties

```javascript
// Example: Robust webhook handling with URL-based routing
app.post('/payment-webhook/:orderId', async (req, res) => {
    const orderId = req.params.orderId;
    const payload = req.body;

    try {
        // Get full payment details
        const payment = await getPaymentDetails(payload.id);

        // Safely access metadata
        const metadata = payment?.metadata || {};
        const customerId = metadata.customerId;

        // Verify order ID matches
        if (metadata.orderId !== orderId) {
            console.warn(`Order ID mismatch: URL=${orderId}, metadata=${metadata.orderId}`);
            return res.status(400).send('Order ID mismatch');
        }

        // Process with metadata
        await processOrderUpdate(orderId, payload.status, {
            paymentId: payload.id,
            customerId,
            amount: payload.targetAmount
        });

        res.status(200).send('OK');

    } catch (error) {
        console.error('Webhook processing failed:', error);
        res.status(500).send('Internal server error');
    }
});
```

### Webhook Security

Webhooks include an HMAC-SHA256 signature in the `X-Signature` header for verification. Each webhook endpoint has its own secret:

```javascript
const crypto = require('crypto');

function verifyWebhook(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(JSON.stringify(payload))
    .digest('hex');

  return signature === expectedSignature;
}
```

### Webhook Implementation Examples

#### 1. URL-Based Routing (Recommended)

```javascript
// Route with order ID in URL path
app.post('/payment-webhook/:orderId', async (req, res) => {
  const orderId = req.params.orderId;
  const signature = req.headers['x-signature'];
  const payload = req.body;

  // Verify webhook signature
  if (!verifyWebhook(payload, signature, process.env.WEBHOOK_SECRET)) {
    return res.status(401).send('Invalid signature');
  }

  try {
    // Process payment update directly using URL parameter
    switch (payload.status) {
      case 'CONFIRMED':
        // Payment successful - fulfill order
        await fulfillOrder(orderId, {
          paymentId: payload.id,
          txHash: payload.transaction?.txHash,
          amount: payload.targetAmount
        });
        break;
      case 'EXPIRED':
        // Payment expired - cancel order
        await cancelOrder(orderId);
        break;
    }

    res.status(200).send('OK');
  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).send('Internal server error');
  }
});
```

#### 2. Metadata-Based Routing

```javascript
// Single webhook endpoint that uses metadata for routing
app.post('/payment-webhook', async (req, res) => {
  const signature = req.headers['x-signature'];
  const payload = req.body;

  // Verify webhook signature
  if (!verifyWebhook(payload, signature, process.env.WEBHOOK_SECRET)) {
    return res.status(401).send('Invalid signature');
  }

  try {
    // Get full payment details including metadata
    const paymentDetails = await getPaymentDetails(payload.id);

    if (!paymentDetails || !paymentDetails.metadata) {
      return res.status(400).send('Payment metadata not found');
    }

    const { orderId, customerId } = paymentDetails.metadata;

    // Process payment update using metadata
    switch (payload.status) {
      case 'CONFIRMED':
        await fulfillOrder(orderId, {
          paymentId: payload.id,
          txHash: payload.transaction?.txHash,
          amount: payload.targetAmount,
          customerId
        });
        break;
      case 'EXPIRED':
        await cancelOrder(orderId);
        break;
    }

    res.status(200).send('OK');
  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).send('Internal server error');
  }
});

// Helper function to get full payment details
async function getPaymentDetails(paymentId) {
  const response = await fetch(`http://localhost:3000/api/v1/payments/${paymentId}`, {
    headers: {
      'X-API-Key': process.env.STABLECOINPAY_API_KEY,
      'X-API-Secret': process.env.STABLECOINPAY_API_SECRET
    }
  });

  const result = await response.json();
  return result.success ? result.data : null;
}
```

### Webhook Events

| Event | Trigger |
|-------|---------|
| payment.created | New payment request created |
| payment.completed | Payment successfully completed |
| payment.failed | Payment failed |
| payment.expired | Payment expired without completion |
| transaction.detected | Transaction found on blockchain |
| transaction.confirmed | Transaction confirmed |

### Webhook URL Patterns

#### Pattern 1: Order-Specific URLs (Recommended)
```
https://yoursite.com/payment-webhook/order-12345
https://yoursite.com/payment-webhook/invoice-67890
https://shop.example.com/stablecoin-webhook/order-abc123
```

#### Pattern 2: Domain-Specific URLs
```
https://tenant1.yourapp.com/payment-webhook
https://tenant2.yourapp.com/payment-webhook
https://custom-domain.com/crypto-payment-webhook
```

#### Pattern 3: Service-Specific URLs
```
https://yoursite.com/webhooks/subscription-payment
https://yoursite.com/webhooks/one-time-payment
https://yoursite.com/webhooks/donation
```

---

## Error Handling

### Standard Error Response

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Amount must be at least 0.01",
    "details": {
      "field": "amount",
      "value": 0.005
    },
    "requestId": "req_123456789",
    "timestamp": "2024-01-15T10:00:00.000Z"
  }
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Invalid request parameters |
| `AUTH_ERROR` | 401 | Authentication failed |
| `PERMISSION_ERROR` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `BLOCKCHAIN_ERROR` | 502 | Blockchain service unavailable |
| `INTERNAL_SERVER_ERROR` | 500 | Server error |

### Validation Errors

**Amount Validation:**
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Amount must be at least 0.01"
  }
}
```

**Currency Validation:**
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid currency code"
  }
}
```

**Blockchain/Network Validation:**
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Blockchain must be one of: ETHEREUM, BSC, TRON, SOLANA, TON"
  }
}
```

### Error Handling Best Practices

1. **Always check the `success` field** in responses
2. **Log error details** for debugging
3. **Implement retry logic** for temporary failures
4. **Handle rate limiting** with exponential backoff
5. **Validate webhook signatures** to prevent fraud

```javascript
async function createPayment(paymentData) {
  try {
    const response = await fetch('/api/v1/payments', {
      method: 'POST',
      headers: {
        'X-API-Key': process.env.API_KEY,
        'X-API-Secret': process.env.API_SECRET,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(paymentData)
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(`Payment creation failed: ${result.error.message}`);
    }

    return result.data;
  } catch (error) {
    console.error('Payment creation error:', error);
    throw error;
  }
}
```

---

## Rate Limiting

### Rate Limit Headers

All responses include rate limiting information:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248000
```

### Rate Limits by Endpoint Type

| Endpoint Type | Limit | Window |
|---------------|-------|--------|
| Payment Creation | 100 requests | 1 hour |
| Payment Status | 200 requests | 1 hour |
| Manual Verification | 10 requests | 1 minute |
| Webhook Endpoints | 1000 requests | 1 hour |

### Handling Rate Limits

```javascript
async function makeApiRequest(url, options) {
  const response = await fetch(url, options);

  if (response.status === 429) {
    const resetTime = response.headers.get('X-RateLimit-Reset');
    const waitTime = (resetTime * 1000) - Date.now();

    console.log(`Rate limited. Waiting ${waitTime}ms`);
    await new Promise(resolve => setTimeout(resolve, waitTime));

    // Retry the request
    return makeApiRequest(url, options);
  }

  return response;
}
```

---

## Integration Examples

### WooCommerce Integration

```php
<?php
class StablecoinPayGateway extends WC_Payment_Gateway {
    public function __construct() {
        $this->id = 'stablecoinpay';
        $this->title = 'Stablecoin Payment';
        $this->description = 'Pay with USDT, USDC, or other stablecoins';

        $this->init_form_fields();
        $this->init_settings();
    }

    public function process_payment($order_id) {
        $order = wc_get_order($order_id);

        $payment_data = [
            'amount' => $order->get_total(),
            'currency' => $order->get_currency(),
            'token' => $this->get_option('default_token', 'USDT'),
            'blockchain' => $this->get_option('blockchain', 'ETHEREUM'),
            'network' => $this->get_option('network', 'MAINNET'),
            'webhookEndpointId' => $this->get_woocommerce_webhook_endpoint_id(),
            'metadata' => [
                'order_id' => $order_id,
                'customer_id' => $order->get_customer_id()
            ]
        ];

        $payment = $this->create_payment($payment_data);

        if ($payment) {
            // Store payment ID in order meta
            $order->update_meta_data('_stablecoinpay_id', $payment['id']);
            $order->save();

            // Redirect to payment page
            return [
                'result' => 'success',
                'redirect' => $this->get_payment_url($payment)
            ];
        }

        return ['result' => 'failure'];
    }

    private function create_payment($data) {
        $response = wp_remote_post('http://localhost:3000/api/v1/payments', [
            'headers' => [
                'X-API-Key' => $this->get_option('api_key'),
                'X-API-Secret' => $this->get_option('api_secret'),
                'Content-Type' => 'application/json'
            ],
            'body' => json_encode($data)
        ]);

        if (is_wp_error($response)) {
            return false;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);
        return $body['success'] ? $body['data'] : false;
    }
}
```

### Node.js/Express Integration

```javascript
const express = require('express');
const crypto = require('crypto');
const app = express();

class StablecoinPayClient {
    constructor(apiKey, apiSecret, baseUrl = 'http://localhost:3000/api/v1') {
        this.apiKey = apiKey;
        this.apiSecret = apiSecret;
        this.baseUrl = baseUrl;
    }

    async createPayment(paymentData) {
        const response = await fetch(`${this.baseUrl}/payments`, {
            method: 'POST',
            headers: {
                'X-API-Key': this.apiKey,
                'X-API-Secret': this.apiSecret,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(paymentData)
        });

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.error.message);
        }

        return result.data;
    }

    async getPayment(paymentId) {
        const response = await fetch(`${this.baseUrl}/payments/${paymentId}`, {
            headers: {
                'X-API-Key': this.apiKey,
                'X-API-Secret': this.apiSecret
            }
        });

        const result = await response.json();
        return result.success ? result.data : null;
    }
}

// Initialize client
const stablecoinPay = new StablecoinPayClient(
    process.env.STABLECOINPAY_API_KEY,
    process.env.STABLECOINPAY_API_SECRET
);

// Create payment endpoint
app.post('/create-payment', async (req, res) => {
    try {
        const payment = await stablecoinPay.createPayment({
            amount: req.body.amount,
            currency: 'USD',
            token: 'USDT',
            blockchain: 'ETHEREUM',
            network: 'MAINNET',
            webhookEndpointId: await getOrCreateWebhookEndpoint(),
            metadata: {
                orderId: req.body.orderId,
                userId: req.body.userId
            }
        });

        res.json({ success: true, payment });
    } catch (error) {
        res.status(400).json({ success: false, error: error.message });
    }
});

// Webhook handler with order ID in URL
app.post('/payment-webhook/:orderId', async (req, res) => {
    const orderId = req.params.orderId;
    const signature = req.headers['x-signature'];
    const payload = req.body;

    // Verify signature
    const expectedSignature = crypto
        .createHmac('sha256', process.env.WEBHOOK_SECRET)
        .update(JSON.stringify(payload))
        .digest('hex');

    if (signature !== expectedSignature) {
        return res.status(401).send('Invalid signature');
    }

    try {
        // Get full payment details including metadata
        const paymentDetails = await stablecoinPay.getPayment(payload.id);

        if (!paymentDetails) {
            console.error('Payment not found:', payload.id);
            return res.status(404).send('Payment not found');
        }

        // Process payment update with metadata
        console.log('Payment update:', {
            paymentId: payload.id,
            status: payload.status,
            metadata: paymentDetails.metadata
        });

        // Update order status using URL parameter (more efficient)
        await updateOrderStatus(
            orderId,
            payload.status,
            {
                paymentId: payload.id,
                txHash: payload.transaction?.txHash,
                amount: payload.targetAmount,
                userId: paymentDetails.metadata?.userId
            }
        );

        res.status(200).send('OK');
    } catch (error) {
        console.error('Webhook processing error:', error);
        res.status(500).send('Internal server error');
    }
});
```

### Python/Django Integration

```python
import requests
import hmac
import hashlib
import json
from django.conf import settings
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods

class StablecoinPayClient:
    def __init__(self, api_key, api_secret, base_url='http://localhost:3000/api/v1'):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = base_url

    def create_payment(self, payment_data):
        headers = {
            'X-API-Key': self.api_key,
            'X-API-Secret': self.api_secret,
            'Content-Type': 'application/json'
        }

        response = requests.post(
            f'{self.base_url}/payments',
            headers=headers,
            json=payment_data
        )

        result = response.json()
        if not result.get('success'):
            raise Exception(result.get('error', {}).get('message', 'Payment creation failed'))

        return result['data']

    def get_payment(self, payment_id):
        headers = {
            'X-API-Key': self.api_key,
            'X-API-Secret': self.api_secret
        }

        response = requests.get(
            f'{self.base_url}/payments/{payment_id}',
            headers=headers
        )

        result = response.json()
        return result['data'] if result.get('success') else None

# Initialize client
stablecoin_pay = StablecoinPayClient(
    settings.STABLECOINPAY_API_KEY,
    settings.STABLECOINPAY_API_SECRET
)

@require_http_methods(["POST"])
def create_payment(request):
    try:
        data = json.loads(request.body)

        payment = stablecoin_pay.create_payment({
            'amount': data['amount'],
            'currency': 'USD',
            'token': 'USDT',
            'blockchain': 'ETHEREUM',
            'network': 'MAINNET',
            'webhookEndpointId': get_or_create_webhook_endpoint(),
            'metadata': {
                'order_id': data.get('order_id'),
                'user_id': data.get('user_id')
            }
        })

        return JsonResponse({'success': True, 'payment': payment})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=400)

@csrf_exempt
@require_http_methods(["POST"])
def payment_webhook_handler(request, order_id):
    signature = request.headers.get('X-Signature')
    payload = request.body

    # Verify signature
    expected_signature = hmac.new(
        settings.WEBHOOK_SECRET.encode(),
        payload,
        hashlib.sha256
    ).hexdigest()

    if signature != expected_signature:
        return HttpResponse('Invalid signature', status=401)

    try:
        # Process payment update
        data = json.loads(payload)

        # Get full payment details including metadata
        payment_details = stablecoin_pay.get_payment(data['id'])

        if not payment_details:
            return HttpResponse('Payment not found', status=404)

        print(f"Payment update: {data}")
        print(f"Payment metadata: {payment_details.get('metadata', {})}")

        # Update order status using URL parameter (more efficient)
        update_order_status(
            order_id,
            data['status'],
            {
                'payment_id': data['id'],
                'tx_hash': data.get('transaction', {}).get('txHash'),
                'amount': data['targetAmount'],
                'user_id': payment_details.get('metadata', {}).get('user_id')
            }
        )

        return HttpResponse('OK')

    except Exception as e:
        print(f"Webhook processing error: {e}")
        return HttpResponse('Internal server error', status=500)
```

---

## Testing Guide

### Test Environment Setup

1. **Use testnet networks** for testing
2. **Configure test API keys** (separate from production)
3. **Set up webhook URL testing** with tools like ngrok

### Test API Key

For testing purposes, you can use:
```
API Key: 441976f3aca75f5a0434a945b77717ad65c3755a9220045f
```

### Test Payment Creation

```bash
curl -X POST "http://localhost:3000/api/v1/payments" \
  -H "X-API-Key: 441976f3aca75f5a0434a945b77717ad65c3755a9220045f" \
  -H "X-API-Secret: test_secret" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 1.00,
    "currency": "USD",
    "token": "USDT",
    "blockchain": "ETHEREUM",
    "network": "TESTNET",
    "webhookEndpointId": 123
  }'
```

### Webhook Testing with ngrok

1. Install ngrok: `npm install -g ngrok`
2. Start your local server: `node server.js`
3. Expose with ngrok: `ngrok http 3000`
4. Create a webhook endpoint with the ngrok URL

Example webhook URLs:
```
https://abc123.ngrok.io/payment-webhook/order-123
https://abc123.ngrok.io/payment-webhook/invoice-456
https://abc123.ngrok.io/stablecoin-webhook/subscription-789
```

### Manual Testing Scenarios

1. **Successful Payment Flow**
   - Create payment
   - Send exact amount to provided address
   - Verify status changes to CONFIRMED

2. **Expired Payment**
   - Create payment with short expiry
   - Wait for expiration
   - Verify status changes to EXPIRED

3. **Incorrect Amount**
   - Send wrong amount
   - Verify payment remains WAITING
   - Test manual verification if needed

4. **Network Issues**
   - Test with blockchain service down
   - Verify graceful error handling
   - Test manual verification fallback

---

## Troubleshooting

### Common Issues

#### 1. Payment Not Detected

**Symptoms:** Payment status remains WAITING despite transaction sent

**Possible Causes:**
- Incorrect amount sent (must be exact)
- Wrong network or token
- Blockchain service temporarily down
- Transaction not yet confirmed

**Solutions:**
```bash
# Force check for transactions
curl -X POST "http://localhost:3000/api/v1/payments/{id}/check" \
  -H "X-API-Key: your_api_key" \
  -H "X-API-Secret: your_secret"

# Check payment details
curl -X GET "http://localhost:3000/api/v1/payments/{id}" \
  -H "X-API-Key: your_api_key" \
  -H "X-API-Secret: your_secret"
```

#### 2. Webhook Not Received

**Symptoms:** Payment confirmed but webhook not triggered

**Possible Causes:**
- Incorrect webhook endpoint configuration
- Webhook endpoint down
- Firewall blocking requests
- Invalid SSL certificate
- URL routing issues
- Inactive webhook endpoint

**Solutions:**
- Test webhook URL manually
- Check server logs and routing configuration
- Verify SSL certificate
- Use ngrok for local testing
- Ensure webhook endpoint is active
- Verify webhook endpoint URL pattern matches your route handler

#### 3. Authentication Errors

**Symptoms:** 401 Unauthorized responses

**Possible Causes:**
- Invalid API key or secret
- IP not whitelisted
- Expired credentials

**Solutions:**
- Verify API credentials
- Check IP whitelist configuration
- Contact administrator for new credentials

#### 4. Rate Limiting

**Symptoms:** 429 Too Many Requests

**Solutions:**
```javascript
// Implement exponential backoff
async function retryWithBackoff(fn, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await fn();
        } catch (error) {
            if (error.status === 429 && i < maxRetries - 1) {
                const delay = Math.pow(2, i) * 1000; // Exponential backoff
                await new Promise(resolve => setTimeout(resolve, delay));
                continue;
            }
            throw error;
        }
    }
}
```

### Debug Mode

Enable debug logging in your integration:

```javascript
const DEBUG = process.env.NODE_ENV === 'development';

function debugLog(message, data) {
    if (DEBUG) {
        console.log(`[StablecoinPay Debug] ${message}`, data);
    }
}

// Use in your integration
debugLog('Creating payment', paymentData);
debugLog('Payment response', paymentResponse);
debugLog('Webhook received', webhookPayload);
```

### Support Resources

- **API Documentation**: Available at `/api/docs` when server is running
- **Test Environment**: Use testnet networks for safe testing
- **Error Logs**: Check server logs for detailed error information
- **Manual Verification**: Use admin dashboard for manual payment verification

---

## Security Best Practices

### API Key Management

1. **Store securely** - Never commit API keys to version control
2. **Use environment variables** - Store in `.env` files or secure vaults
3. **Rotate regularly** - Change API keys periodically
4. **Separate environments** - Use different keys for test/production

### Webhook Security

1. **Always verify signatures** - Prevent webhook spoofing
2. **Use HTTPS** - Encrypt webhook data in transit
3. **Implement idempotency** - Handle duplicate webhook deliveries
4. **Rate limit webhook endpoints** - Prevent abuse
5. **Validate URL parameters** - Ensure order IDs match expected patterns
6. **Use unique webhook URLs** - Include unpredictable tokens in URLs
7. **Secure webhook endpoint secrets** - Store secrets securely and rotate regularly

### Network Security

1. **IP Whitelisting** - Restrict API access to known IPs
2. **HTTPS Only** - Never use HTTP in production
3. **Firewall Rules** - Limit access to necessary ports only

### Data Protection

1. **Log minimal data** - Don't log sensitive information
2. **Encrypt at rest** - Secure stored payment data
3. **Regular backups** - Maintain secure data backups
4. **Access controls** - Limit who can access payment data

---

This comprehensive guide provides everything needed to integrate with the StablecoinPay API. For additional support or questions, refer to the API documentation or contact the development team.
