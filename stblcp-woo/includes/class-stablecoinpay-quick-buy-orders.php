<?php
/**
 * StablecoinPay Quick Buy Orders
 * 
 * Handles order creation for Quick Buy functionality
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_Quick_Buy_Orders {
    
    /**
     * Create order from product
     */
    public function create_product_order($product_id, $quantity, $variation_id = 0, $variation_data = array()) {
        // Execute order creation within a database transaction
        $result = StablecoinPay_Database::execute_in_transaction(
            array($this, 'create_product_order_transaction'),
            array($product_id, $quantity, $variation_id, $variation_data)
        );

        if ($result === false) {
            throw new Exception('Failed to create order due to database transaction error');
        }

        return $result;
    }

    /**
     * Create order from product within transaction
     */
    public function create_product_order_transaction($product_id, $quantity, $variation_id = 0, $variation_data = array()) {
        // Get product
        $product = wc_get_product($variation_id ?: $product_id);
        if (!$product) {
            throw new Exception('Product not found');
        }

        // Validate stock
        if (!$product->is_in_stock()) {
            throw new Exception('Product is out of stock');
        }

        // Check stock quantity
        if ($product->managing_stock() && $product->get_stock_quantity() < $quantity) {
            throw new Exception('Insufficient stock available');
        }

        // Create order
        $order = wc_create_order();

        if (!$order) {
            throw new Exception('Failed to create WooCommerce order');
        }

        // Add product to order
        $item_id = $order->add_product($product, $quantity, array(
            'variation' => $variation_data
        ));

        if (!$item_id) {
            throw new Exception('Failed to add product to order');
        }

        // Set customer data
        $this->set_customer_data($order);

        // Set payment method
        $order->set_payment_method('stablecoinpay');
        $order->set_payment_method_title('StablecoinPay');

        // Calculate totals
        $order->calculate_totals();

        // Set order status
        $order->set_status('pending');

        // Add order note
        $order->add_order_note('Order created via StablecoinPay Quick Buy');

        // Save order
        $order_id = $order->save();

        if (!$order_id) {
            throw new Exception('Failed to save order');
        }

        return $order;
    }
    
    /**
     * Create order from cart
     */
    public function create_cart_order() {
        // Execute order creation within a database transaction
        $result = StablecoinPay_Database::execute_in_transaction(
            array($this, 'create_cart_order_transaction'),
            array()
        );

        if ($result === false) {
            throw new Exception('Failed to create order due to database transaction error');
        }

        return $result;
    }

    /**
     * Create order from cart within transaction
     */
    public function create_cart_order_transaction() {
        // Check if cart is not empty
        if (!WC() || !WC()->cart || WC()->cart->is_empty()) {
            throw new Exception('Cart is empty');
        }

        // Validate cart items
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            $product = $cart_item['data'];

            if (!$product || !$product->is_purchasable()) {
                throw new Exception('One or more cart items are not available for purchase');
            }

            if (!$product->is_in_stock()) {
                throw new Exception('One or more cart items are out of stock');
            }

            // Check stock quantity
            if ($product->managing_stock() && $product->get_stock_quantity() < $cart_item['quantity']) {
                throw new Exception('Insufficient stock for ' . $product->get_name());
            }
        }

        // Create order
        $order = wc_create_order();

        if (!$order) {
            throw new Exception('Failed to create WooCommerce order');
        }

        // Add cart items to order
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            $product = $cart_item['data'];
            $quantity = $cart_item['quantity'];
            $variation = $cart_item['variation'];

            $item_id = $order->add_product($product, $quantity, array(
                'variation' => $variation
            ));

            if (!$item_id) {
                throw new Exception('Failed to add cart item to order');
            }
        }

        // Set customer data
        $this->set_customer_data($order);

        // Set payment method
        $order->set_payment_method('stablecoinpay');
        $order->set_payment_method_title('StablecoinPay');

        // Calculate totals
        $order->calculate_totals();

        // Set order status
        $order->set_status('pending');

        // Add order note
        $order->add_order_note('Order created via StablecoinPay Quick Buy');

        // Save order
        $order_id = $order->save();

        if (!$order_id) {
            throw new Exception('Failed to save order');
        }

        // Clear cart
        WC()->cart->empty_cart();

        return $order;
    }
    
    /**
     * Set customer data on order
     */
    private function set_customer_data($order) {
        if (is_user_logged_in()) {
            $user = wp_get_current_user();
            $customer = new WC_Customer($user->ID);
            
            // Set billing data from customer
            $order->set_billing_email($customer->get_billing_email() ?: $user->user_email);
            $order->set_billing_first_name($customer->get_billing_first_name() ?: $user->first_name);
            $order->set_billing_last_name($customer->get_billing_last_name() ?: $user->last_name);
            $order->set_billing_phone($customer->get_billing_phone());
            $order->set_billing_company($customer->get_billing_company());
            $order->set_billing_address_1($customer->get_billing_address_1());
            $order->set_billing_address_2($customer->get_billing_address_2());
            $order->set_billing_city($customer->get_billing_city());
            $order->set_billing_state($customer->get_billing_state());
            $order->set_billing_postcode($customer->get_billing_postcode());
            $order->set_billing_country($customer->get_billing_country());
            
            // Set shipping data from customer
            $order->set_shipping_first_name($customer->get_shipping_first_name() ?: $customer->get_billing_first_name());
            $order->set_shipping_last_name($customer->get_shipping_last_name() ?: $customer->get_billing_last_name());
            $order->set_shipping_company($customer->get_shipping_company());
            $order->set_shipping_address_1($customer->get_shipping_address_1());
            $order->set_shipping_address_2($customer->get_shipping_address_2());
            $order->set_shipping_city($customer->get_shipping_city());
            $order->set_shipping_state($customer->get_shipping_state());
            $order->set_shipping_postcode($customer->get_shipping_postcode());
            $order->set_shipping_country($customer->get_shipping_country());
            
            // Set customer ID
            $order->set_customer_id($user->ID);
        } else {
            // Get email from session
            $email = WC()->session ? WC()->session->get('quick_buy_email') : '';
            $name = WC()->session ? WC()->session->get('quick_buy_name') : '';
            
            if ($email) {
                $order->set_billing_email($email);
                
                if ($name) {
                    $name_parts = explode(' ', trim($name), 2);
                    $order->set_billing_first_name($name_parts[0]);
                    if (isset($name_parts[1])) {
                        $order->set_billing_last_name($name_parts[1]);
                    }
                }
            }
        }
    }
}
