<?php
/**
 * StablecoinPay Quick Buy Buttons
 * 
 * Handles rendering of Quick Buy buttons on product and cart pages
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_Quick_Buy_Buttons {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Product page hook - ONLY use after add to cart button
        add_action('woocommerce_after_add_to_cart_button', array($this, 'render_product_quick_buy'));

        // Legacy cart page hooks for shortcode-based cart
        add_action('woocommerce_proceed_to_checkout', array($this, 'render_cart_quick_buy'), 15);
        add_action('woocommerce_after_cart_totals', array($this, 'render_cart_quick_buy_alt'), 10);
        add_action('woocommerce_cart_actions', array($this, 'render_cart_quick_buy_fallback'), 20);

        // WooCommerce Cart Block support
        add_filter('render_block', array($this, 'add_cart_block_hooks'), 9999, 2);
        add_action('stablecoinpay_before_woocommerce/proceed-to-checkout-block', array($this, 'render_cart_quick_buy_block'));
    }
    
    /**
     * Render Quick Buy button on product page
     */
    public function render_product_quick_buy() {
        global $product;

        if (!$this->should_show_quick_buy($product)) {
            return;
        }

        $this->render_quick_buy_button('product', array(
            'product_id' => $product->get_id(),
            'context' => 'single_product'
        ));
    }
    
    /**
     * Render Quick Buy button on cart page (unified method)
     */
    public function render_cart_quick_buy() {
        // Prevent multiple renders on same page
        static $cart_button_rendered = false;
        if ($cart_button_rendered) {
            return;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('StablecoinPay Quick Buy: render_cart_quick_buy() called');
        }

        if (!$this->should_show_cart_quick_buy()) {
            return;
        }

        $this->render_quick_buy_button('cart', array(
            'context' => 'cart'
        ));

        $cart_button_rendered = true;
    }

    /**
     * Alternative cart page hook (alias for main method)
     */
    public function render_cart_quick_buy_alt() {
        $this->render_cart_quick_buy();
    }

    /**
     * Fallback cart page hook (alias for main method)
     */
    public function render_cart_quick_buy_fallback() {
        $this->render_cart_quick_buy();
    }

    /**
     * Add hooks for WooCommerce Cart Block support
     */
    public function add_cart_block_hooks($block_content, $block) {
        $cart_blocks = array(
            'woocommerce/cart',
            'woocommerce/filled-cart-block',
            'woocommerce/cart-items-block',
            'woocommerce/cart-line-items-block',
            'woocommerce/cart-cross-sells-block',
            'woocommerce/cart-cross-sells-products-block',
            'woocommerce/cart-totals-block',
            'woocommerce/cart-order-summary-block',
            'woocommerce/cart-order-summary-heading-block',
            'woocommerce/cart-order-summary-coupon-form-block',
            'woocommerce/cart-order-summary-subtotal-block',
            'woocommerce/cart-order-summary-fee-block',
            'woocommerce/cart-order-summary-discount-block',
            'woocommerce/cart-order-summary-shipping-block',
            'woocommerce/cart-order-summary-taxes-block',
            'woocommerce/cart-express-payment-block',
            'woocommerce/proceed-to-checkout-block',
            'woocommerce/cart-accepted-payment-methods-block',
        );

        if (in_array($block['blockName'], $cart_blocks)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Quick Buy: Processing cart block: ' . $block['blockName']);
            }

            ob_start();
            do_action('stablecoinpay_before_' . $block['blockName']);
            echo $block_content;
            do_action('stablecoinpay_after_' . $block['blockName']);
            $block_content = ob_get_contents();
            ob_end_clean();
        }

        return $block_content;
    }

    /**
     * Render Quick Buy button for WooCommerce Cart Block (alias for main method)
     */
    public function render_cart_quick_buy_block() {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('StablecoinPay Quick Buy: render_cart_quick_buy_block() called');
        }

        $this->render_cart_quick_buy();
    }


    
    /**
     * Render Quick Buy button
     */
    private function render_quick_buy_button($context, $data = array()) {
        $enabled_methods = StablecoinPay_Settings::get_enabled_payment_methods();
        
        if (empty($enabled_methods)) {
            return;
        }
        
        // Get default method
        $default_method = $this->get_default_method($enabled_methods);
        
        // Include template
        include STABLECOINPAY_PLUGIN_PATH . 'templates/quick-buy-button.php';
    }
    
    /**
     * Get default payment method
     */
    private function get_default_method($enabled_methods) {
        $default_token = StablecoinPay_Settings::get_setting('default_token', 'USDT');
        $default_blockchain = StablecoinPay_Settings::get_setting('default_blockchain', 'ETHEREUM');
        
        // Try to find exact match
        foreach ($enabled_methods as $method_key => $method) {
            if ($method['token'] === $default_token && $method['blockchain'] === $default_blockchain) {
                return array_merge($method, array('key' => $method_key));
            }
        }
        
        // Try to find token match with any blockchain
        foreach ($enabled_methods as $method_key => $method) {
            if ($method['token'] === $default_token) {
                return array_merge($method, array('key' => $method_key));
            }
        }
        
        // Return first available method if default not found
        $first_method = reset($enabled_methods);
        $first_key = key($enabled_methods);
        return array_merge($first_method, array('key' => $first_key));
    }
    
    /**
     * Check if Quick Buy is enabled (shared validation)
     */
    private function is_quick_buy_enabled() {
        return StablecoinPay_Settings::get_setting('enable_quick_buy', 1);
    }

    /**
     * Check if Quick Buy should be shown on product page
     */
    private function should_show_quick_buy($product) {
        // Check base requirements
        if (!$this->is_quick_buy_enabled()) {
            return false;
        }

        // Check if product exists and is purchasable
        if (!$product || !$product->is_purchasable()) {
            return false;
        }

        // Check product-specific settings (if implemented)
        $disable_quick_buy = get_post_meta($product->get_id(), '_disable_stablecoinpay_quick_buy', true);
        if ($disable_quick_buy === 'yes') {
            return false;
        }

        // Check if product is in stock
        if (!$product->is_in_stock()) {
            return false;
        }

        return true;
    }
    
    /**
     * Check if Quick Buy should be shown on cart page
     */
    private function should_show_cart_quick_buy() {
        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('StablecoinPay Quick Buy: Checking cart button visibility');
        }

        // Check base requirements
        if (!$this->is_quick_buy_enabled()) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Quick Buy: Quick Buy disabled in settings');
            }
            return false;
        }

        // Check if we're on cart page
        if (!is_cart()) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Quick Buy: Not on cart page');
            }
            return false;
        }

        // Check if WooCommerce and cart are available
        if (!function_exists('WC') || !WC() || !WC()->cart) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Quick Buy: WooCommerce or cart not available');
            }
            return false;
        }

        // Check if cart has items
        if (WC()->cart->is_empty()) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Quick Buy: Cart is empty');
            }
            return false;
        }

        // Check if all cart items are in stock and purchasable
        foreach (WC()->cart->get_cart() as $cart_item) {
            $product = $cart_item['data'];
            if (!$product || !$product->is_purchasable() || !$product->is_in_stock()) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('StablecoinPay Quick Buy: Cart contains non-purchasable items');
                }
                return false;
            }
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('StablecoinPay Quick Buy: Cart button should be shown');
        }

        return true;
    }
}
