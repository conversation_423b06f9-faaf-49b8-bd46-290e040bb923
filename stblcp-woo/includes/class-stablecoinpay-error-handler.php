<?php
/**
 * Standardized Error Handler
 * 
 * Provides consistent error handling across the plugin
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_Error_Handler {

    /**
     * Error types
     */
    const ERROR_TYPE_VALIDATION = 'validation';
    const ERROR_TYPE_API = 'api';
    const ERROR_TYPE_DATABASE = 'database';
    const ERROR_TYPE_NETWORK = 'network';
    const ERROR_TYPE_CONFIGURATION = 'configuration';
    const ERROR_TYPE_PAYMENT = 'payment';
    const ERROR_TYPE_SECURITY = 'security';

    /**
     * Error severity levels
     */
    const SEVERITY_LOW = 'low';
    const SEVERITY_MEDIUM = 'medium';
    const SEVERITY_HIGH = 'high';
    const SEVERITY_CRITICAL = 'critical';

    /**
     * Logger instance
     */
    private static $logger = null;

    /**
     * Initialize error handler
     */
    public static function init() {
        if (class_exists('WC_Logger')) {
            self::$logger = wc_get_logger();
        }
    }

    /**
     * Handle error with standardized approach
     *
     * @param string $error_type Error type constant
     * @param string $message Error message
     * @param array $context Additional context
     * @param string $severity Severity level
     * @param bool $user_facing Whether error should be shown to user
     * @return StablecoinPay_Error
     */
    public static function handle_error($error_type, $message, $context = array(), $severity = self::SEVERITY_MEDIUM, $user_facing = true) {
        $error = new StablecoinPay_Error($error_type, $message, $context, $severity, $user_facing);
        
        // Log the error
        self::log_error($error);
        
        return $error;
    }

    /**
     * Create validation error
     *
     * @param string $message Error message
     * @param array $context Additional context
     * @return StablecoinPay_Error
     */
    public static function validation_error($message, $context = array()) {
        return self::handle_error(
            self::ERROR_TYPE_VALIDATION,
            $message,
            $context,
            self::SEVERITY_LOW,
            true
        );
    }

    /**
     * Create API error
     *
     * @param string $message Error message
     * @param array $context Additional context
     * @param string $severity Severity level
     * @return StablecoinPay_Error
     */
    public static function api_error($message, $context = array(), $severity = self::SEVERITY_HIGH) {
        return self::handle_error(
            self::ERROR_TYPE_API,
            $message,
            $context,
            $severity,
            true
        );
    }

    /**
     * Create database error
     *
     * @param string $message Error message
     * @param array $context Additional context
     * @return StablecoinPay_Error
     */
    public static function database_error($message, $context = array()) {
        return self::handle_error(
            self::ERROR_TYPE_DATABASE,
            $message,
            $context,
            self::SEVERITY_HIGH,
            false // Don't show DB errors to users
        );
    }

    /**
     * Create network error
     *
     * @param string $message Error message
     * @param array $context Additional context
     * @return StablecoinPay_Error
     */
    public static function network_error($message, $context = array()) {
        return self::handle_error(
            self::ERROR_TYPE_NETWORK,
            $message,
            $context,
            self::SEVERITY_MEDIUM,
            true
        );
    }

    /**
     * Create configuration error
     *
     * @param string $message Error message
     * @param array $context Additional context
     * @return StablecoinPay_Error
     */
    public static function configuration_error($message, $context = array()) {
        return self::handle_error(
            self::ERROR_TYPE_CONFIGURATION,
            $message,
            $context,
            self::SEVERITY_CRITICAL,
            false // Don't expose config errors to users
        );
    }

    /**
     * Create payment error
     *
     * @param string $message Error message
     * @param array $context Additional context
     * @return StablecoinPay_Error
     */
    public static function payment_error($message, $context = array()) {
        return self::handle_error(
            self::ERROR_TYPE_PAYMENT,
            $message,
            $context,
            self::SEVERITY_HIGH,
            true
        );
    }

    /**
     * Create security error
     *
     * @param string $message Error message
     * @param array $context Additional context
     * @return StablecoinPay_Error
     */
    public static function security_error($message, $context = array()) {
        return self::handle_error(
            self::ERROR_TYPE_SECURITY,
            $message,
            $context,
            self::SEVERITY_CRITICAL,
            false // Don't expose security details to users
        );
    }

    /**
     * Log error with context
     *
     * @param StablecoinPay_Error $error Error object
     */
    private static function log_error($error) {
        $log_message = sprintf(
            '[%s] %s - %s',
            strtoupper($error->get_type()),
            strtoupper($error->get_severity()),
            $error->get_message()
        );

        $context = array_merge(
            array('source' => 'stablecoinpay'),
            $error->get_context()
        );

        // Use WooCommerce logger if available
        if (self::$logger) {
            $level = self::get_log_level($error->get_severity());
            self::$logger->log($level, $log_message, $context);
        } else {
            // Fallback to error_log
            error_log('StablecoinPay: ' . $log_message . ' Context: ' . wp_json_encode($context));
        }
    }

    /**
     * Get log level for severity
     *
     * @param string $severity Severity level
     * @return string Log level
     */
    private static function get_log_level($severity) {
        switch ($severity) {
            case self::SEVERITY_CRITICAL:
                return 'critical';
            case self::SEVERITY_HIGH:
                return 'error';
            case self::SEVERITY_MEDIUM:
                return 'warning';
            case self::SEVERITY_LOW:
            default:
                return 'info';
        }
    }

    /**
     * Get user-friendly error message
     *
     * @param StablecoinPay_Error $error Error object
     * @return string User-friendly message
     */
    public static function get_user_message($error) {
        if (!$error->is_user_facing()) {
            return __('An error occurred. Please try again or contact support.', 'stablecoinpay-gateway');
        }

        $type = $error->get_type();
        $message = $error->get_message();

        // Add user-friendly prefixes based on error type
        switch ($type) {
            case self::ERROR_TYPE_VALIDATION:
                return $message; // Validation messages are already user-friendly
            
            case self::ERROR_TYPE_API:
                return __('Payment service error: ', 'stablecoinpay-gateway') . $message;
            
            case self::ERROR_TYPE_NETWORK:
                return __('Connection error: ', 'stablecoinpay-gateway') . $message;
            
            case self::ERROR_TYPE_PAYMENT:
                return __('Payment error: ', 'stablecoinpay-gateway') . $message;
            
            default:
                return $message;
        }
    }

    /**
     * Send AJAX error response
     *
     * @param StablecoinPay_Error $error Error object
     */
    public static function send_ajax_error($error) {
        wp_send_json_error(array(
            'message' => self::get_user_message($error),
            'type' => $error->get_type(),
            'code' => $error->get_code()
        ));
    }

    /**
     * Add WooCommerce notice
     *
     * @param StablecoinPay_Error $error Error object
     */
    public static function add_wc_notice($error) {
        if (function_exists('wc_add_notice')) {
            wc_add_notice(self::get_user_message($error), 'error');
        }
    }
}

/**
 * Error object class
 */
class StablecoinPay_Error {
    private $type;
    private $message;
    private $context;
    private $severity;
    private $user_facing;
    private $code;
    private $timestamp;

    public function __construct($type, $message, $context = array(), $severity = 'medium', $user_facing = true) {
        $this->type = $type;
        $this->message = $message;
        $this->context = $context;
        $this->severity = $severity;
        $this->user_facing = $user_facing;
        $this->code = $this->generate_error_code();
        $this->timestamp = current_time('mysql');
    }

    public function get_type() { return $this->type; }
    public function get_message() { return $this->message; }
    public function get_context() { return $this->context; }
    public function get_severity() { return $this->severity; }
    public function is_user_facing() { return $this->user_facing; }
    public function get_code() { return $this->code; }
    public function get_timestamp() { return $this->timestamp; }

    private function generate_error_code() {
        return strtoupper(substr($this->type, 0, 3)) . '_' . substr(md5($this->message), 0, 6);
    }
}

// Initialize error handler
StablecoinPay_Error_Handler::init();
