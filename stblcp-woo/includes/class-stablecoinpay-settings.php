<?php
/**
 * Settings Management
 * 
 * Handles plugin settings and admin interface
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_Settings {

    private $settings_key = 'stablecoinpay_settings';
    const SETTINGS_KEY = 'stablecoinpay_settings';

    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('StablecoinPay Settings', 'stablecoinpay-gateway'),
            __('StablecoinPay', 'stablecoinpay-gateway'),
            'manage_woocommerce',
            'stablecoinpay-settings',
            array($this, 'settings_page')
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        register_setting($this->settings_key, $this->settings_key, array($this, 'validate_settings'));

        // General Settings Section
        add_settings_section(
            'stablecoinpay_general',
            __('General Settings', 'stablecoinpay-gateway'),
            array($this, 'general_section_callback'),
            $this->settings_key
        );

        // API Settings Section
        add_settings_section(
            'stablecoinpay_api',
            __('API Configuration', 'stablecoinpay-gateway'),
            array($this, 'api_section_callback'),
            $this->settings_key
        );

        // Payment Settings Section
        add_settings_section(
            'stablecoinpay_payment',
            __('Payment Configuration', 'stablecoinpay-gateway'),
            array($this, 'payment_section_callback'),
            $this->settings_key
        );

        // Add settings fields
        $this->add_settings_fields();
    }

    /**
     * Add settings fields
     */
    private function add_settings_fields() {
        // General Settings
        add_settings_field(
            'enabled',
            __('Enable Gateway', 'stablecoinpay-gateway'),
            array($this, 'checkbox_field'),
            $this->settings_key,
            'stablecoinpay_general',
            array(
                'field' => 'enabled',
                'label_for' => 'enabled',
                'description' => __('Enable StablecoinPay payment gateway', 'stablecoinpay-gateway')
            )
        );

        add_settings_field(
            'test_mode',
            __('Test Mode', 'stablecoinpay-gateway'),
            array($this, 'checkbox_field'),
            $this->settings_key,
            'stablecoinpay_general',
            array(
                'field' => 'test_mode',
                'label_for' => 'test_mode',
                'description' => __('Use testnet for payments', 'stablecoinpay-gateway')
            )
        );

        add_settings_field(
            'debug_mode',
            __('Debug Mode', 'stablecoinpay-gateway'),
            array($this, 'checkbox_field'),
            $this->settings_key,
            'stablecoinpay_general',
            array(
                'field' => 'debug_mode',
                'label_for' => 'debug_mode',
                'description' => __('Enable debug logging', 'stablecoinpay-gateway')
            )
        );

        // API Settings
        add_settings_field(
            'api_url',
            __('API URL', 'stablecoinpay-gateway'),
            array($this, 'text_field'),
            $this->settings_key,
            'stablecoinpay_api',
            array(
                'field' => 'api_url',
                'label_for' => 'api_url',
                'description' => __('StablecoinPay API base URL (use localhost for development, production URL for live sites)', 'stablecoinpay-gateway'),
                'placeholder' => 'https://stblcp-backend.141822.xyz/api/v1'
            )
        );

        add_settings_field(
            'api_key',
            __('API Key', 'stablecoinpay-gateway'),
            array($this, 'text_field'),
            $this->settings_key,
            'stablecoinpay_api',
            array(
                'field' => 'api_key',
                'label_for' => 'api_key',
                'description' => __('Your StablecoinPay API key', 'stablecoinpay-gateway')
            )
        );

        add_settings_field(
            'api_secret',
            __('API Secret', 'stablecoinpay-gateway'),
            array($this, 'password_field'),
            $this->settings_key,
            'stablecoinpay_api',
            array(
                'field' => 'api_secret',
                'label_for' => 'api_secret',
                'description' => __('Your StablecoinPay API secret. Leave empty to keep existing secret unchanged.', 'stablecoinpay-gateway')
            )
        );

        // Payment Settings
        add_settings_field(
            'default_token',
            __('Default Token', 'stablecoinpay-gateway'),
            array($this, 'select_field'),
            $this->settings_key,
            'stablecoinpay_payment',
            array(
                'field' => 'default_token',
                'label_for' => 'default_token',
                'options' => array(
                    'USDT' => 'USDT - Tether USD',
                    'USDC' => 'USDC - USD Coin',
                    'DAI' => 'DAI - Dai Stablecoin',
                    'PYUSD' => 'PYUSD - PayPal USD',
                    'BUSD' => 'BUSD - Binance USD',
                    'ETH' => 'ETH - Ethereum',
                    'BNB' => 'BNB - Binance Coin',
                    'TRX' => 'TRX - Tron',
                    'SOL' => 'SOL - Solana',
                    'TON' => 'TON - Toncoin'
                ),
                'description' => __('Default cryptocurrency token', 'stablecoinpay-gateway')
            )
        );

        add_settings_field(
            'default_blockchain',
            __('Default Blockchain', 'stablecoinpay-gateway'),
            array($this, 'select_field'),
            $this->settings_key,
            'stablecoinpay_payment',
            array(
                'field' => 'default_blockchain',
                'label_for' => 'default_blockchain',
                'options' => array(
                    'ETHEREUM' => 'Ethereum',
                    'BSC' => 'Binance Smart Chain',
                    'TRON' => 'Tron',
                    'SOLANA' => 'Solana',
                    'TON' => 'TON'
                ),
                'description' => __('Default blockchain network', 'stablecoinpay-gateway')
            )
        );

        add_settings_field(
            'payment_expiry',
            __('Payment Expiry (minutes)', 'stablecoinpay-gateway'),
            array($this, 'number_field'),
            $this->settings_key,
            'stablecoinpay_payment',
            array(
                'field' => 'payment_expiry',
                'label_for' => 'payment_expiry',
                'min' => 1,
                'max' => 1440,
                'description' => __('How long payments remain valid (1-1440 minutes)', 'stablecoinpay-gateway')
            )
        );

        add_settings_field(
            'enable_webhooks',
            __('Enable Webhooks', 'stablecoinpay-gateway'),
            array($this, 'checkbox_field'),
            $this->settings_key,
            'stablecoinpay_payment',
            array(
                'field' => 'enable_webhooks',
                'label_for' => 'enable_webhooks',
                'description' => __('Enable automatic payment status webhooks (recommended for production)', 'stablecoinpay-gateway')
            )
        );

        add_settings_field(
            'webhook_endpoint_status',
            __('Webhook Configuration', 'stablecoinpay-gateway'),
            array($this, 'webhook_endpoint_status_field'),
            $this->settings_key,
            'stablecoinpay_payment',
            array(
                'field' => 'webhook_endpoint_status',
                'label_for' => 'webhook_endpoint_status',
                'description' => __('Generate webhook URL for manual configuration in StablecoinPay admin dashboard.', 'stablecoinpay-gateway')
            )
        );

        add_settings_field(
            'webhook_secret',
            __('Webhook Secret', 'stablecoinpay-gateway'),
            array($this, 'password_field'),
            $this->settings_key,
            'stablecoinpay_payment',
            array(
                'field' => 'webhook_secret',
                'label_for' => 'webhook_secret',
                'description' => __('Enter the webhook endpoint secret from the StablecoinPay admin dashboard. This is used to verify webhook signatures. Leave empty to keep existing secret unchanged.', 'stablecoinpay-gateway')
            )
        );

        add_settings_field(
            'webhook_ip_whitelist',
            __('Webhook IP Whitelist', 'stablecoinpay-gateway'),
            array($this, 'textarea_field'),
            $this->settings_key,
            'stablecoinpay_payment',
            array(
                'field' => 'webhook_ip_whitelist',
                'label_for' => 'webhook_ip_whitelist',
                'description' => __('Comma-separated list of IP addresses allowed to send webhooks (leave empty to allow all)', 'stablecoinpay-gateway'),
                'placeholder' => '*************, 10.0.0.0/8'
            )
        );

        add_settings_field(
            'enabled_payment_methods',
            __('Enabled Payment Methods', 'stablecoinpay-gateway'),
            array($this, 'payment_methods_field'),
            $this->settings_key,
            'stablecoinpay_payment',
            array(
                'field' => 'enabled_payment_methods',
                'description' => __('Select which payment methods to enable for customers', 'stablecoinpay-gateway')
            )
        );

        // Quick Buy Settings Section
        add_settings_section(
            'stablecoinpay_quick_buy',
            __('Quick Buy Settings', 'stablecoinpay-gateway'),
            array($this, 'quick_buy_section_callback'),
            $this->settings_key
        );

        add_settings_field(
            'enable_quick_buy',
            __('Enable Quick Buy', 'stablecoinpay-gateway'),
            array($this, 'checkbox_field'),
            $this->settings_key,
            'stablecoinpay_quick_buy',
            array(
                'field' => 'enable_quick_buy',
                'label_for' => 'enable_quick_buy',
                'description' => __('Enable Quick Buy buttons on product and cart pages', 'stablecoinpay-gateway')
            )
        );

        add_settings_field(
            'quick_buy_button_text',
            __('Button Text', 'stablecoinpay-gateway'),
            array($this, 'text_field'),
            $this->settings_key,
            'stablecoinpay_quick_buy',
            array(
                'field' => 'quick_buy_button_text',
                'label_for' => 'quick_buy_button_text',
                'description' => __('Text displayed on Quick Buy buttons', 'stablecoinpay-gateway'),
                'placeholder' => 'Quick Pay with'
            )
        );

        add_settings_field(
            'quick_buy_show_token',
            __('Show Token Name', 'stablecoinpay-gateway'),
            array($this, 'checkbox_field'),
            $this->settings_key,
            'stablecoinpay_quick_buy',
            array(
                'field' => 'quick_buy_show_token',
                'label_for' => 'quick_buy_show_token',
                'description' => __('Display selected token name on the button', 'stablecoinpay-gateway')
            )
        );

        add_settings_field(
            'quick_buy_require_name',
            __('Require Full Name', 'stablecoinpay-gateway'),
            array($this, 'checkbox_field'),
            $this->settings_key,
            'stablecoinpay_quick_buy',
            array(
                'field' => 'quick_buy_require_name',
                'label_for' => 'quick_buy_require_name',
                'description' => __('Show full name field for guest users. If disabled, the part before @ in email will be used as the name.', 'stablecoinpay-gateway')
            )
        );
    }

    /**
     * Settings page
     */
    public function settings_page() {
        ?>
        <div class="wrap stablecoinpay-admin-container">
                <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <!-- Notices Area (outside of header) -->
            <div class="stablecoinpay-notices-area">
                <?php settings_errors(); ?>
            </div>

            <?php $this->display_connection_status(); ?>

            <?php
            // Only show debug section if debug mode is enabled in settings AND WP_DEBUG is on
            $settings = get_option($this->settings_key, array());
            $debug_enabled = !empty($settings['debug_mode']) && defined('WP_DEBUG') && WP_DEBUG && current_user_can('manage_options');

            if ($debug_enabled): ?>
            <div class="stablecoinpay-settings-section">
                <div class="stablecoinpay-section-header">
                    <span class="dashicons dashicons-admin-tools stablecoinpay-section-icon"></span>
                    <h2><?php _e('Debug: Current Settings State', 'stablecoinpay-gateway'); ?></h2>
                </div>
                <div class="stablecoinpay-section-content">
                    <details>
                        <summary style="cursor: pointer; padding: 8px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
                            <strong><?php _e('Click to view settings data', 'stablecoinpay-gateway'); ?></strong>
                        </summary>
                        <div style="padding: 8px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; margin-top: 5px;">
                            <?php
                            $all_settings = get_option('stablecoinpay_settings', array());
                            // Sanitize sensitive data for display
                            $debug_settings = $all_settings;
                            if (isset($debug_settings['api_key'])) {
                                $debug_settings['api_key'] = substr($debug_settings['api_key'], 0, 8) . '...';
                            }
                            if (isset($debug_settings['api_secret'])) {
                                $debug_settings['api_secret'] = substr($debug_settings['api_secret'], 0, 8) . '...';
                            }
                            echo '<pre>' . esc_html(print_r($debug_settings, true)) . '</pre>';
                            ?>
                        </div>
                    </details>
                </div>
            </div>
            <?php endif; ?>

            <form method="post" action="options.php">
                <?php
                settings_fields($this->settings_key);
                $this->render_custom_sections();
                submit_button(__('Save Changes', 'stablecoinpay-gateway'), 'primary', 'submit', true, array('style' => 'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; padding: 12px 24px; font-size: 16px; font-weight: 600; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'));
                ?>
            </form>

            <div class="stablecoinpay-admin-footer">
                <h3><?php _e('Quick Links', 'stablecoinpay-gateway'); ?></h3>
                <ul class="stablecoinpay-quick-links">
                    <li><a href="<?php echo admin_url('admin.php?page=wc-settings&tab=checkout&section=stablecoinpay'); ?>" target="_blank" rel="noopener noreferrer">
                        <span class="dashicons dashicons-admin-settings"></span>
                        <?php _e('Payment Gateway Settings', 'stablecoinpay-gateway'); ?>
                    </a></li>
                    <li><a href="<?php echo admin_url('admin.php?page=wc-status&tab=logs'); ?>" target="_blank" rel="noopener noreferrer">
                        <span class="dashicons dashicons-media-text"></span>
                        <?php _e('View Logs', 'stablecoinpay-gateway'); ?>
                    </a></li>
                    <li><a href="#" id="test-api-connection">
                        <span class="dashicons dashicons-admin-plugins"></span>
                        <?php _e('Test API Connection', 'stablecoinpay-gateway'); ?>
                    </a></li>
                    <li><a href="#" id="show-cloudflare-ips">
                        <span class="dashicons dashicons-cloud"></span>
                        <?php _e('Show Cloudflare IP Ranges', 'stablecoinpay-gateway'); ?>
                    </a></li>
                </ul>
            </div>

            <!-- Cloudflare IP Ranges Display -->
            <div id="cloudflare-ips-section" style="display: none; margin-top: 20px;">
                <div class="stablecoinpay-settings-section">
                    <h3><?php _e('Cloudflare IP Ranges for Whitelisting', 'stablecoinpay-gateway'); ?></h3>
                    <p><?php _e('If your site uses Cloudflare and you need to whitelist IPs in your StablecoinPay backend, use these IP ranges:', 'stablecoinpay-gateway'); ?></p>
                    <div id="cloudflare-ips-content">
                        <p><?php _e('Loading Cloudflare IP ranges...', 'stablecoinpay-gateway'); ?></p>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render custom sections with enhanced styling
     */
    private function render_custom_sections() {
        global $wp_settings_sections, $wp_settings_fields;

        if (!isset($wp_settings_sections[$this->settings_key])) {
            return;
        }

        $sections = array(
            'stablecoinpay_general' => array(
                'title' => __('General Settings', 'stablecoinpay-gateway'),
                'icon' => 'dashicons-admin-generic',
                'description' => __('General plugin settings and operational modes.', 'stablecoinpay-gateway')
            ),
            'stablecoinpay_api' => array(
                'title' => __('API Configuration', 'stablecoinpay-gateway'),
                'icon' => 'dashicons-admin-links',
                'description' => __('Configure your StablecoinPay API connection settings.', 'stablecoinpay-gateway')
            ),
            'stablecoinpay_payment' => array(
                'title' => __('Payment Configuration', 'stablecoinpay-gateway'),
                'icon' => 'dashicons-money-alt',
                'description' => __('Default payment options and behavior settings.', 'stablecoinpay-gateway')
            ),
            'stablecoinpay_quick_buy' => array(
                'title' => __('Quick Buy Settings', 'stablecoinpay-gateway'),
                'icon' => 'dashicons-cart',
                'description' => __('Configure Quick Buy button behavior and appearance.', 'stablecoinpay-gateway')
            )
        );

        foreach ($sections as $section_id => $section_config) {
            if (!isset($wp_settings_sections[$this->settings_key][$section_id])) {
                continue;
            }

            // Determine if section should be expanded by default
            $is_expanded = $this->is_section_expanded_by_default($section_id);
            $expanded_class = $is_expanded ? ' expanded' : '';
            $aria_expanded = $is_expanded ? 'true' : 'false';

            echo '<div class="stablecoinpay-settings-section collapsible' . $expanded_class . '" data-section="' . esc_attr($section_id) . '">';

            // Collapsible header
            echo '<div class="stablecoinpay-section-header collapsible-header" role="button" tabindex="0" aria-expanded="' . $aria_expanded . '" aria-controls="' . esc_attr($section_id) . '-content">';
            echo '<span class="dashicons ' . esc_attr($section_config['icon']) . ' stablecoinpay-section-icon"></span>';
            echo '<h2>' . esc_html($section_config['title']) . '</h2>';

            // Add collapse indicator with proper initial state
            $indicator_class = $is_expanded ? 'dashicons-arrow-up-alt2' : 'dashicons-arrow-down-alt2';
            echo '<span class="dashicons ' . $indicator_class . ' collapse-indicator"></span>';
            echo '</div>';

            // Collapsible content
            echo '<div class="stablecoinpay-section-content collapsible-content" id="' . esc_attr($section_id) . '-content">';
            echo '<p class="stablecoinpay-section-description">' . esc_html($section_config['description']) . '</p>';

            if (isset($wp_settings_fields[$this->settings_key][$section_id])) {
                echo '<table class="stablecoinpay-form-table">';
                foreach ($wp_settings_fields[$this->settings_key][$section_id] as $field) {
                    echo '<tr class="stablecoinpay-form-row">';
                    echo '<th scope="row">';
                    if (isset($field['args']['label_for']) && !empty($field['args']['label_for'])) {
                        echo '<label for="' . esc_attr($field['args']['label_for']) . '">' . $field['title'] . '</label>';
                    } else {
                        echo $field['title'];
                    }
                    echo '</th>';
                    echo '<td>';
                    call_user_func($field['callback'], $field['args']);
                    echo '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            }

            echo '</div>'; // Close content
            echo '</div>'; // Close section
        }

        // Add JavaScript for collapsible functionality
        $this->add_collapsible_javascript();
    }

    /**
     * Determine if a section should be expanded by default
     *
     * @param string $section_id Section identifier
     * @return bool True if should be expanded
     */
    private function is_section_expanded_by_default($section_id) {
        $settings = get_option($this->settings_key, array());

        // Check if API is configured
        $api_configured = !empty($settings['api_key']) && !empty($settings['api_secret']) && !empty($settings['api_url']);

        switch ($section_id) {
            case 'stablecoinpay_general':
                // Always expand general settings for new users
                return !$api_configured;

            case 'stablecoinpay_api':
                // Expand API settings if not configured, or if there are validation errors
                return !$api_configured || $this->has_api_validation_errors();

            case 'stablecoinpay_payment':
                // Expand payment settings if API is configured but payment methods aren't selected
                $enabled_methods = self::get_setting('enabled_payment_methods', array());
                return $api_configured && empty($enabled_methods);

            case 'stablecoinpay_quick_buy':
                // Collapse Quick Buy by default (advanced feature)
                return false;

            default:
                return false;
        }
    }

    /**
     * Check if there are API validation errors
     *
     * @return bool True if there are API-related errors
     */
    private function has_api_validation_errors() {
        $errors = get_settings_errors($this->settings_key);

        foreach ($errors as $error) {
            if (strpos($error['code'], 'api_') === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * Add JavaScript for collapsible functionality
     */
    private function add_collapsible_javascript() {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Handle collapsible sections
            $('.stablecoinpay-section-header.collapsible-header').on('click keypress', function(e) {
                // Handle both click and Enter/Space key press
                if (e.type === 'click' || e.which === 13 || e.which === 32) {
                    e.preventDefault();

                    const $header = $(this);
                    const $section = $header.closest('.stablecoinpay-settings-section');
                    const $content = $section.find('.collapsible-content');
                    const $indicator = $header.find('.collapse-indicator');

                    // Toggle expanded state
                    const isExpanded = $section.hasClass('expanded');

                    if (isExpanded) {
                        // Collapse
                        $content.slideUp(300, function() {
                            $section.removeClass('expanded');
                            $header.attr('aria-expanded', 'false');
                            $indicator.removeClass('dashicons-arrow-up-alt2').addClass('dashicons-arrow-down-alt2');
                        });
                    } else {
                        // Expand
                        $section.addClass('expanded');
                        $header.attr('aria-expanded', 'true');
                        $indicator.removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-up-alt2');
                        $content.slideDown(300);
                    }

                    // Save state to localStorage
                    const sectionId = $section.data('section');
                    const expandedSections = JSON.parse(localStorage.getItem('stablecoinpay_expanded_sections') || '{}');
                    expandedSections[sectionId] = !isExpanded;
                    localStorage.setItem('stablecoinpay_expanded_sections', JSON.stringify(expandedSections));
                }
            });

            // Restore saved states from localStorage
            const savedStates = JSON.parse(localStorage.getItem('stablecoinpay_expanded_sections') || '{}');

            $('.stablecoinpay-settings-section.collapsible').each(function() {
                const $section = $(this);
                const sectionId = $section.data('section');
                const $header = $section.find('.collapsible-header');
                const $content = $section.find('.collapsible-content');
                const $indicator = $header.find('.collapse-indicator');

                // Check if we have a saved state for this section
                if (savedStates.hasOwnProperty(sectionId)) {
                    const shouldExpand = savedStates[sectionId];

                    if (shouldExpand && !$section.hasClass('expanded')) {
                        // Expand without animation on page load
                        $section.addClass('expanded');
                        $header.attr('aria-expanded', 'true');
                        $indicator.removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-up-alt2');
                        $content.show();
                    } else if (!shouldExpand && $section.hasClass('expanded')) {
                        // Collapse without animation on page load
                        $section.removeClass('expanded');
                        $header.attr('aria-expanded', 'false');
                        $indicator.removeClass('dashicons-arrow-up-alt2').addClass('dashicons-arrow-down-alt2');
                        $content.hide();
                    }
                }

                // Ensure indicator state matches section state (icons are already set correctly in PHP)
                // This is just a safety check in case of any inconsistencies
                if ($section.hasClass('expanded') && $indicator.hasClass('dashicons-arrow-down-alt2')) {
                    $indicator.removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-up-alt2');
                } else if (!$section.hasClass('expanded') && $indicator.hasClass('dashicons-arrow-up-alt2')) {
                    $indicator.removeClass('dashicons-arrow-up-alt2').addClass('dashicons-arrow-down-alt2');
                }
            });
        });
        </script>
        <?php
    }

    /**
     * Display connection status
     */
    private function display_connection_status() {
        $settings = get_option($this->settings_key, array());
        $api_configured = !empty($settings['api_key']) && !empty($settings['api_secret']) && !empty($settings['api_url']);

        // Count how many cards we'll show
        $card_count = 1; // Always show at least one card (API status or getting started)
        if ($api_configured) {
            $card_count++; // Production readiness check
            if (is_admin() && !wp_doing_ajax() && current_user_can('manage_options')) {
                $card_count++; // Rewrite rules card
            }
        }

        $single_card_class = $card_count === 1 ? ' single-card' : '';
        echo '<div class="stablecoinpay-status-cards' . $single_card_class . '">';

        // Only show status cards if there's something meaningful to display
        if ($api_configured) {
            // API Configuration Status - only show when configured
            echo '<div class="stablecoinpay-status-card success">';
            echo '<div class="stablecoinpay-status-header">';
            echo '<span class="dashicons dashicons-yes-alt stablecoinpay-status-icon"></span>';
            echo '<h3 class="stablecoinpay-status-title">' . __('API Configuration', 'stablecoinpay-gateway') . '</h3>';
            echo '</div>';
            echo '<p class="stablecoinpay-status-message">' . __('API credentials configured. Click "Test API Connection" below to verify connectivity.', 'stablecoinpay-gateway') . '</p>';
            echo '</div>';

            // Add production readiness check only when API is configured
            $this->display_production_readiness_check($settings);
        } else {
            // Show a welcoming getting started card when not configured
            echo '<div class="stablecoinpay-status-card info">';
            echo '<div class="stablecoinpay-status-header">';
            echo '<span class="dashicons dashicons-welcome-learn-more stablecoinpay-status-icon"></span>';
            echo '<h3 class="stablecoinpay-status-title">' . __('Welcome to StablecoinPay! 🚀', 'stablecoinpay-gateway') . '</h3>';
            echo '</div>';
            echo '<div class="stablecoinpay-status-message">';
            echo '<p>' . __('Accept cryptocurrency payments with ease. To get started:', 'stablecoinpay-gateway') . '</p>';
            echo '<ol style="margin: 8px 0 0 20px; line-height: 1.6;">';
            echo '<li>' . __('Configure your API credentials in the <strong>API Configuration</strong> section below', 'stablecoinpay-gateway') . '</li>';
            echo '<li>' . __('Select your preferred payment methods in <strong>Payment Configuration</strong>', 'stablecoinpay-gateway') . '</li>';
            echo '<li>' . __('Enable the gateway in WooCommerce payment settings', 'stablecoinpay-gateway') . '</li>';
            echo '</ol>';
            echo '</div>';
            echo '</div>';
        }

        // Rewrite Rules Status - only show if there might be issues
        if (is_admin() && !wp_doing_ajax() && current_user_can('manage_options') && $api_configured) {
            $flush_url = wp_nonce_url(
                admin_url('admin.php?page=stablecoinpay-settings&stablecoinpay_flush_rules=1'),
                'stablecoinpay_flush_rules'
            );

            echo '<div class="stablecoinpay-status-card info">';
            echo '<div class="stablecoinpay-status-header">';
            echo '<span class="dashicons dashicons-admin-tools stablecoinpay-status-icon"></span>';
            echo '<h3 class="stablecoinpay-status-title">' . __('URL Rewrite Rules', 'stablecoinpay-gateway') . '</h3>';
            echo '</div>';
            echo '<p class="stablecoinpay-status-message">';
            echo __('If payment page URLs are not working (showing "Page not found"), ', 'stablecoinpay-gateway');
            echo '<a href="' . esc_url($flush_url) . '" class="button button-small" style="margin-left: 8px;">';
            echo __('Click here to flush rewrite rules', 'stablecoinpay-gateway');
            echo '</a>';
            echo '</p>';
            echo '</div>';
        }

        echo '</div>';
    }

    /**
     * Display production readiness check
     */
    private function display_production_readiness_check($settings) {
        $issues = array();

        // Check API URL
        if (strpos($settings['api_url'], 'localhost') !== false && !WP_DEBUG) {
            $issues[] = __('API URL contains localhost - not suitable for production', 'stablecoinpay-gateway');
        }

        // Check webhook configuration
        $enable_webhooks = $settings['enable_webhooks'] ?? $settings['enable_callbacks'] ?? false;
        if (empty($enable_webhooks)) {
            $issues[] = __('Webhooks disabled - using polling only mode', 'stablecoinpay-gateway');
        }

        // Check webhook endpoint status
        $webhook_endpoint_id = get_option('stablecoinpay_webhook_endpoint_id');
        if (!empty($enable_webhooks) && empty($webhook_endpoint_id)) {
            $issues[] = __('Webhook endpoint not configured - will be created automatically on first payment', 'stablecoinpay-gateway');
        }

        // Test webhook endpoint accessibility if configured
        if (!empty($enable_webhooks) && !empty($webhook_endpoint_id)) {
            $webhook_test_result = $this->test_webhook_endpoint_accessibility();
            if (!$webhook_test_result) {
                $issues[] = __('Webhook endpoint may not be accessible from external services', 'stablecoinpay-gateway');
            }
        }

        if (!empty($issues)) {
            echo '<div class="stablecoinpay-status-card warning">';
            echo '<div class="stablecoinpay-status-header">';
            echo '<span class="dashicons dashicons-warning stablecoinpay-status-icon"></span>';
            echo '<h3 class="stablecoinpay-status-title">' . __('Production Readiness Issues', 'stablecoinpay-gateway') . '</h3>';
            echo '</div>';
            echo '<div class="stablecoinpay-status-message">';
            echo '<ul style="margin: 8px 0 0 20px;">';
            foreach ($issues as $issue) {
                echo '<li>' . esc_html($issue) . '</li>';
            }
            echo '</ul>';
            echo '</div>';
            echo '</div>';
        } else {
            echo '<div class="stablecoinpay-status-card success">';
            echo '<div class="stablecoinpay-status-header">';
            echo '<span class="dashicons dashicons-yes-alt stablecoinpay-status-icon"></span>';
            echo '<h3 class="stablecoinpay-status-title">' . __('Production Ready', 'stablecoinpay-gateway') . '</h3>';
            echo '</div>';
            echo '<p class="stablecoinpay-status-message">' . __('Configuration appears ready for production use.', 'stablecoinpay-gateway') . '</p>';
            echo '</div>';
        }
    }

    /**
     * Test callback URL accessibility
     *
     * @param string $callback_base Base callback URL
     * @return bool True if accessible, false otherwise
     */
    private function test_callback_accessibility($callback_base) {
        // Create test callback URL
        $test_url = rtrim($callback_base, '/') . '/?wc-api=stablecoinpay&test=1';

        // Perform lightweight HEAD request to test accessibility
        $response = wp_remote_head($test_url, array(
            'timeout' => 10,
            'sslverify' => true,
            'user-agent' => 'StablecoinPay-Callback-Test/1.0'
        ));

        // Consider it accessible if we get any HTTP response (even 404 is fine)
        // We just want to ensure the domain/URL is reachable
        if (is_wp_error($response)) {
            return false;
        }

        $status_code = wp_remote_retrieve_response_code($response);

        // Any HTTP response code means the URL is accessible
        // 404 is fine - it means the server is reachable
        return ($status_code >= 200 && $status_code < 600);
    }

    /**
     * Test webhook endpoint accessibility
     *
     * @return bool True if accessible, false otherwise
     */
    private function test_webhook_endpoint_accessibility() {
        // Create test webhook URL
        $test_url = home_url('/wc-api/stablecoinpay_callback/');

        // Perform lightweight HEAD request to test accessibility
        $response = wp_remote_head($test_url, array(
            'timeout' => 10,
            'sslverify' => true,
            'user-agent' => 'StablecoinPay-Webhook-Test/1.0'
        ));

        // Consider it accessible if we get any HTTP response (even 404 is fine)
        // We just want to ensure the domain/URL is reachable
        if (is_wp_error($response)) {
            return false;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        return ($status_code >= 200 && $status_code < 600);
    }

    /**
     * Checkbox field
     */
    public function checkbox_field($args) {
        // Use get_setting method for consistency
        $value = self::get_setting($args['field'], '');

        echo '<input type="checkbox" id="' . esc_attr($args['field']) . '" name="' . $this->settings_key . '[' . esc_attr($args['field']) . ']" value="1" ' . checked(1, $value, false) . ' />';
        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }
    }

    /**
     * Text field
     */
    public function text_field($args) {
        // Use get_setting method to handle decryption for sensitive fields
        $value = self::get_setting($args['field'], '');
        $placeholder = isset($args['placeholder']) ? $args['placeholder'] : '';

        echo '<input type="text" id="' . esc_attr($args['field']) . '" name="' . $this->settings_key . '[' . esc_attr($args['field']) . ']" value="' . esc_attr($value) . '" placeholder="' . esc_attr($placeholder) . '" class="regular-text" />';
        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }
    }

    /**
     * Password field
     */
    public function password_field($args) {
        // Use get_setting method to handle decryption for sensitive fields
        $value = self::get_setting($args['field'], '');

        echo '<input type="password" id="' . esc_attr($args['field']) . '" name="' . $this->settings_key . '[' . esc_attr($args['field']) . ']" value="' . esc_attr($value) . '" class="regular-text" />';
        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }
    }

    /**
     * Select field
     */
    public function select_field($args) {
        // Use get_setting method for consistency
        $value = self::get_setting($args['field'], '');

        echo '<select id="' . esc_attr($args['field']) . '" name="' . $this->settings_key . '[' . esc_attr($args['field']) . ']">';
        foreach ($args['options'] as $option_value => $option_label) {
            echo '<option value="' . esc_attr($option_value) . '" ' . selected($value, $option_value, false) . '>' . esc_html($option_label) . '</option>';
        }
        echo '</select>';
        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }
    }

    /**
     * Number field
     */
    public function number_field($args) {
        // Use get_setting method for consistency
        $value = self::get_setting($args['field'], '');
        $min = isset($args['min']) ? $args['min'] : '';
        $max = isset($args['max']) ? $args['max'] : '';

        echo '<input type="number" id="' . esc_attr($args['field']) . '" name="' . $this->settings_key . '[' . esc_attr($args['field']) . ']" value="' . esc_attr($value) . '" min="' . esc_attr($min) . '" max="' . esc_attr($max) . '" class="small-text" />';
        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }
    }

    /**
     * Textarea field
     */
    public function textarea_field($args) {
        // Use get_setting method for consistency
        $value = self::get_setting($args['field'], '');
        $placeholder = isset($args['placeholder']) ? $args['placeholder'] : '';

        echo '<textarea id="' . esc_attr($args['field']) . '" name="' . $this->settings_key . '[' . esc_attr($args['field']) . ']" placeholder="' . esc_attr($placeholder) . '" class="large-text" rows="3">' . esc_textarea($value) . '</textarea>';
        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }
    }

    /**
     * Webhook Configuration field - generates webhook URL for manual configuration
     */
    public function webhook_endpoint_status_field($args) {
        // Generate the webhook URL for this WooCommerce installation
        $webhook_url = home_url('/wc-api/stablecoinpay_callback/');
        $site_name = get_bloginfo('name');
        $suggested_name = 'WooCommerce - ' . $site_name;

        echo '<div class="stablecoinpay-webhook-config-container">';

        // Main configuration card
        echo '<div class="webhook-config-card">';
        echo '<div class="config-header">';
        echo '<span class="config-icon">🔗</span>';
        echo '<strong>' . __('Manual Webhook Configuration', 'stablecoinpay-gateway') . '</strong>';
        echo '</div>';

        echo '<div class="config-content">';
        echo '<p>' . __('Copy the webhook URL below and manually add it to your StablecoinPay admin dashboard:', 'stablecoinpay-gateway') . '</p>';

        // Webhook URL section
        echo '<div class="webhook-url-section">';
        echo '<label for="webhook-url-display"><strong>' . __('Webhook URL:', 'stablecoinpay-gateway') . '</strong></label>';
        echo '<div class="url-input-group">';
        echo '<input type="text" id="webhook-url-display" value="' . esc_attr($webhook_url) . '" readonly class="webhook-url-input" />';
        echo '<button type="button" id="copy-webhook-url" class="button button-secondary copy-btn">';
        echo '<span class="dashicons dashicons-admin-page"></span> ' . __('Copy', 'stablecoinpay-gateway');
        echo '</button>';
        echo '</div>';
        echo '</div>';

        // Suggested name section
        echo '<div class="webhook-name-section">';
        echo '<label for="webhook-name-display"><strong>' . __('Suggested Name:', 'stablecoinpay-gateway') . '</strong></label>';
        echo '<div class="name-input-group">';
        echo '<input type="text" id="webhook-name-display" value="' . esc_attr($suggested_name) . '" readonly class="webhook-name-input" />';
        echo '<button type="button" id="copy-webhook-name" class="button button-secondary copy-btn">';
        echo '<span class="dashicons dashicons-admin-page"></span> ' . __('Copy', 'stablecoinpay-gateway');
        echo '</button>';
        echo '</div>';
        echo '</div>';

        // Configuration instructions
        echo '<div class="config-instructions">';
        echo '<h4>' . __('Configuration Steps:', 'stablecoinpay-gateway') . '</h4>';
        echo '<ol>';
        echo '<li>' . __('Copy the webhook URL above', 'stablecoinpay-gateway') . '</li>';
        echo '<li>' . __('Log in to your StablecoinPay admin dashboard', 'stablecoinpay-gateway') . '</li>';
        echo '<li>' . __('Navigate to Webhook Management section', 'stablecoinpay-gateway') . '</li>';
        echo '<li>' . __('Create a new webhook endpoint with the copied URL', 'stablecoinpay-gateway') . '</li>';
        echo '<li>' . __('Select events: payment.created, payment.confirmed, payment.expired, payment.canceled, transaction.detected, transaction.confirmed', 'stablecoinpay-gateway') . '</li>';
        echo '<li>' . __('Save the webhook endpoint configuration', 'stablecoinpay-gateway') . '</li>';
        echo '<li>' . __('Copy the webhook endpoint secret from the admin dashboard', 'stablecoinpay-gateway') . '</li>';
        echo '<li>' . __('Paste the secret in the "Webhook Secret" field below', 'stablecoinpay-gateway') . '</li>';
        echo '</ol>';
        echo '</div>';

        echo '</div>'; // config-content
        echo '</div>'; // webhook-config-card

        // Status section
        $webhook_endpoint_id = get_option('stablecoinpay_webhook_endpoint_id');
        if ($webhook_endpoint_id) {
            echo '<div class="webhook-status-info">';
            echo '<div class="status-card active">';
            echo '<span class="status-icon">✅</span>';
            echo '<span>' . sprintf(__('Webhook endpoint configured (ID: %s)', 'stablecoinpay-gateway'), $webhook_endpoint_id) . '</span>';
            echo '</div>';
            echo '</div>';
        } else {
            echo '<div class="webhook-status-info">';
            echo '<div class="status-card pending">';
            echo '<span class="status-icon">⏳</span>';
            echo '<span>' . __('Webhook endpoint configured manually - endpoint ID will be detected when first webhook is received', 'stablecoinpay-gateway') . '</span>';
            echo '</div>';
            echo '</div>';
        }

        echo '</div>'; // stablecoinpay-webhook-config-container

        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }

        // Add JavaScript for copy functionality
        $this->add_webhook_config_javascript();
    }

    /**
     * Legacy Callback URL Base field - kept for backward compatibility during migration
     * @deprecated Use webhook_endpoint_status_field instead
     */
    public function callback_url_base_field($args) {
        // Use get_setting method for consistency
        $value = self::get_setting($args['field'], '');
        $placeholder = isset($args['placeholder']) ? $args['placeholder'] : '';

        // Generate suggested callback URL
        $suggested_url = $this->generate_callback_url_base();
        $current_url = !empty($value) ? $value : $suggested_url;

        echo '<div class="stablecoinpay-callback-url-container">';

        // Input and buttons container for better alignment
        echo '<div class="stablecoinpay-input-button-group">';

        // Main input field
        echo '<input type="text" id="' . esc_attr($args['field']) . '" name="' . $this->settings_key . '[' . esc_attr($args['field']) . ']" value="' . esc_attr($value) . '" placeholder="' . esc_attr($placeholder) . '" class="regular-text stablecoinpay-callback-url-input" />';

        // Buttons container
        echo '<div class="stablecoinpay-button-group">';

        // Auto-generate button
        echo '<button type="button" id="auto-generate-callback-url" class="button button-primary stablecoinpay-btn-auto-generate">';
        echo '<span class="dashicons dashicons-admin-site-alt3"></span>';
        echo '<span class="btn-text">' . __('Auto-Generate', 'stablecoinpay-gateway') . '</span>';
        echo '</button>';

        // Test URL button
        echo '<button type="button" id="test-callback-url" class="button button-secondary stablecoinpay-btn-test-url">';
        echo '<span class="dashicons dashicons-admin-links"></span>';
        echo '<span class="btn-text">' . __('Test URL', 'stablecoinpay-gateway') . '</span>';
        echo '</button>';

        echo '</div>'; // Close button group
        echo '</div>'; // Close input-button group

        echo '<br>';

        // URL preview and status
        echo '<div class="stablecoinpay-callback-preview" style="margin-top: 10px;">';
        echo '<div class="callback-url-preview">';
        echo '<strong>' . __('Full Callback URL Preview:', 'stablecoinpay-gateway') . '</strong><br>';
        echo '<code id="callback-url-preview">' . esc_html($current_url . '/?wc-api=stablecoinpay_callback&order_id=12345') . '</code>';
        echo '</div>';

        // Status indicator
        echo '<div id="callback-url-status" class="callback-url-status" style="margin-top: 8px;"></div>';
        echo '</div>';

        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }

        // Environment detection info
        $environment_info = $this->detect_environment();
        if ($environment_info['show_warning']) {
            echo '<div class="notice notice-warning inline" style="margin-top: 10px; padding: 8px 12px;">';
            echo '<p><strong>' . __('Environment Notice:', 'stablecoinpay-gateway') . '</strong> ' . esc_html($environment_info['message']) . '</p>';
            echo '</div>';
        }

        echo '</div>';

        // Add JavaScript for auto-generation functionality (legacy)
        $this->add_callback_url_javascript($suggested_url);
    }

    /**
     * Add JavaScript for webhook configuration functionality
     */
    private function add_webhook_config_javascript() {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Copy webhook URL
            $('#copy-webhook-url').on('click', function(e) {
                e.preventDefault();
                const $button = $(this);
                const $input = $('#webhook-url-display');
                const originalText = $button.html();

                // Select and copy the text
                $input.select();
                $input[0].setSelectionRange(0, 99999); // For mobile devices

                try {
                    document.execCommand('copy');
                    $button.html('<span class="dashicons dashicons-yes"></span> ' + <?php echo wp_json_encode(__('Copied!', 'stablecoinpay-gateway')); ?>);
                    $button.addClass('copied');

                    setTimeout(function() {
                        $button.html(originalText);
                        $button.removeClass('copied');
                    }, 2000);
                } catch (err) {
                    console.error('Failed to copy: ', err);
                    $button.html('<span class="dashicons dashicons-no"></span> ' + <?php echo wp_json_encode(__('Failed', 'stablecoinpay-gateway')); ?>);
                    setTimeout(function() {
                        $button.html(originalText);
                    }, 2000);
                }
            });

            // Copy webhook name
            $('#copy-webhook-name').on('click', function(e) {
                e.preventDefault();
                const $button = $(this);
                const $input = $('#webhook-name-display');
                const originalText = $button.html();

                // Select and copy the text
                $input.select();
                $input[0].setSelectionRange(0, 99999); // For mobile devices

                try {
                    document.execCommand('copy');
                    $button.html('<span class="dashicons dashicons-yes"></span> ' + <?php echo wp_json_encode(__('Copied!', 'stablecoinpay-gateway')); ?>);
                    $button.addClass('copied');

                    setTimeout(function() {
                        $button.html(originalText);
                        $button.removeClass('copied');
                    }, 2000);
                } catch (err) {
                    console.error('Failed to copy: ', err);
                    $button.html('<span class="dashicons dashicons-no"></span> ' + <?php echo wp_json_encode(__('Failed', 'stablecoinpay-gateway')); ?>);
                    setTimeout(function() {
                        $button.html(originalText);
                    }, 2000);
                }
            });
        });
        </script>

        <style>
        /* Webhook Configuration Styling */
        .stablecoinpay-webhook-config-container {
            max-width: 100%;
        }

        .webhook-config-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-left: 4px solid #667eea;
        }

        .config-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            font-size: 16px;
        }

        .config-icon {
            font-size: 20px;
            margin-right: 8px;
        }

        .config-content p {
            margin-bottom: 16px;
            color: #374151;
        }

        .webhook-url-section,
        .webhook-name-section {
            margin-bottom: 20px;
        }

        .webhook-url-section label,
        .webhook-name-section label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #1f2937;
        }

        .url-input-group,
        .name-input-group {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .webhook-url-input,
        .webhook-name-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: #ffffff;
            font-family: monospace;
            font-size: 13px;
        }

        .webhook-url-input {
            min-width: 400px;
        }

        .copy-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            white-space: nowrap;
        }

        .copy-btn.copied {
            background: #10b981;
            border-color: #10b981;
            color: white;
        }

        .config-instructions {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 16px;
            margin-top: 16px;
        }

        .config-instructions h4 {
            margin: 0 0 12px 0;
            color: #1f2937;
            font-size: 14px;
            font-weight: 600;
        }

        .config-instructions ol {
            margin: 0;
            padding-left: 20px;
        }

        .config-instructions li {
            margin-bottom: 6px;
            color: #374151;
            font-size: 13px;
        }

        .webhook-status-info {
            margin-top: 16px;
        }

        .status-card {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            border-radius: 6px;
            font-size: 14px;
        }

        .status-card.active {
            background: linear-gradient(135deg, #f0fff4 0%, #ecfdf5 100%);
            border: 1px solid #10b981;
            color: #065f46;
        }

        .status-card.pending {
            background: linear-gradient(135deg, #fffbf0 0%, #fef3c7 100%);
            border: 1px solid #f59e0b;
            color: #92400e;
        }

        .status-icon {
            font-size: 16px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .url-input-group,
            .name-input-group {
                flex-direction: column;
                align-items: stretch;
            }

            .webhook-url-input,
            .webhook-name-input {
                min-width: 100%;
                margin-bottom: 8px;
            }
        }
        </style>
        <?php
    }

    /**
     * Add JavaScript for webhook management functionality (legacy)
     */
    private function add_webhook_management_javascript() {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Test webhook endpoint
            $('#test-webhook-endpoint').on('click', function(e) {
                e.preventDefault();
                const $button = $(this);
                const originalText = $button.html();

                $button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> ' + <?php echo wp_json_encode(__('Testing...', 'stablecoinpay-gateway')); ?>);

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'stablecoinpay_test_webhook_endpoint',
                        nonce: <?php echo wp_json_encode(wp_create_nonce('stablecoinpay_webhook_test')); ?>
                    },
                    success: function(response) {
                        if (response.success) {
                            showWebhookStatus('success', response.data.message);
                        } else {
                            showWebhookStatus('error', response.data.message || <?php echo wp_json_encode(__('Test failed', 'stablecoinpay-gateway')); ?>);
                        }
                    },
                    error: function() {
                        showWebhookStatus('error', <?php echo wp_json_encode(__('Test request failed', 'stablecoinpay-gateway')); ?>);
                    },
                    complete: function() {
                        $button.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Create/Recreate webhook endpoint
            $('#create-webhook-endpoint, #recreate-webhook-endpoint').on('click', function(e) {
                e.preventDefault();
                const $button = $(this);
                const originalText = $button.html();
                const isRecreate = $button.attr('id') === 'recreate-webhook-endpoint';

                $button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> ' +
                    (isRecreate ? <?php echo wp_json_encode(__('Recreating...', 'stablecoinpay-gateway')); ?> : <?php echo wp_json_encode(__('Creating...', 'stablecoinpay-gateway')); ?>));

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'stablecoinpay_create_webhook_endpoint',
                        recreate: isRecreate ? 1 : 0,
                        nonce: <?php echo wp_json_encode(wp_create_nonce('stablecoinpay_webhook_create')); ?>
                    },
                    success: function(response) {
                        if (response.success) {
                            showWebhookStatus('success', response.data.message);
                            // Reload the page to show updated status
                            setTimeout(function() {
                                location.reload();
                            }, 2000);
                        } else {
                            showWebhookStatus('error', response.data.message || <?php echo wp_json_encode(__('Creation failed', 'stablecoinpay-gateway')); ?>);
                        }
                    },
                    error: function() {
                        showWebhookStatus('error', <?php echo wp_json_encode(__('Creation request failed', 'stablecoinpay-gateway')); ?>);
                    },
                    complete: function() {
                        $button.prop('disabled', false).html(originalText);
                    }
                });
            });

            function showWebhookStatus(type, message) {
                const $container = $('.stablecoinpay-webhook-status-container');
                const $existing = $container.find('.webhook-status-message');

                if ($existing.length) {
                    $existing.remove();
                }

                const statusClass = type === 'success' ? 'notice-success' : 'notice-error';
                const statusHtml = '<div class="webhook-status-message notice ' + statusClass + ' inline"><p>' + message + '</p></div>';

                $container.append(statusHtml);

                // Auto-hide success messages
                if (type === 'success') {
                    setTimeout(function() {
                        $container.find('.webhook-status-message').fadeOut();
                    }, 5000);
                }
            }
        });
        </script>

        <style>
        /* Webhook Status Styling */
        .stablecoinpay-webhook-status-container {
            max-width: 100%;
        }

        .webhook-status-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background: #fff;
        }

        .webhook-status-card.active {
            border-color: #46b450;
            background: linear-gradient(135deg, #f0fff4 0%, #ecfdf5 100%);
        }

        .webhook-status-card.warning {
            border-color: #ffb900;
            background: linear-gradient(135deg, #fffbf0 0%, #fef3c7 100%);
        }

        .webhook-status-card.error {
            border-color: #dc3232;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        }

        .status-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .status-icon {
            font-size: 18px;
            margin-right: 8px;
            font-weight: bold;
        }

        .status-icon.active {
            color: #46b450;
        }

        .status-icon.warning {
            color: #ffb900;
        }

        .status-icon.error {
            color: #dc3232;
        }

        .endpoint-details p {
            margin: 8px 0;
            font-size: 14px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-badge.active {
            background: #46b450;
            color: white;
        }

        .webhook-actions {
            margin-top: 16px;
            display: flex;
            gap: 8px;
        }

        .webhook-actions .button {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .webhook-status-message {
            margin-top: 16px;
        }

        .dashicons.spin {
            animation: rotation 1s infinite linear;
        }

        @keyframes rotation {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(359deg);
            }
        }
        </style>
        <?php
    }

    /**
     * Payment methods field
     */
    public function payment_methods_field($args) {
        // Use get_setting method for consistency
        $enabled_methods = self::get_setting($args['field'], array());

        if (!is_array($enabled_methods)) {
            $enabled_methods = array();
        }

        $payment_methods = $this->get_available_payment_methods();

        echo '<div class="stablecoinpay-payment-methods">';

        // Add select all controls
        echo '<div class="payment-methods-controls">';
        echo '<div class="select-all-controls">';
        echo '<strong>' . __('Quick Select:', 'stablecoinpay-gateway') . '</strong>';
        echo '<button type="button" id="select-all-methods">' . __('Select All', 'stablecoinpay-gateway') . '</button>';
        echo '<button type="button" id="select-none-methods">' . __('Select None', 'stablecoinpay-gateway') . '</button>';
        echo '<button type="button" id="select-stablecoins">' . __('Stablecoins Only', 'stablecoinpay-gateway') . '</button>';
        echo '<button type="button" id="select-native">' . __('Native Coins Only', 'stablecoinpay-gateway') . '</button>';
        echo '<span id="selection-counter" style="margin-left: 12px; font-weight: bold; color: #0073aa;"></span>';
        echo '</div>';
        echo '</div>';

        foreach ($payment_methods as $method_key => $method) {
            $checked = in_array($method_key, $enabled_methods);
            $icon_url = STABLECOINPAY_PLUGIN_URL . 'assets/icons/' . strtolower($method['token']) . '.svg';

            $is_stablecoin = in_array($method['token'], array('USDT', 'USDC', 'DAI', 'PYUSD', 'BUSD'));
            $data_type = $is_stablecoin ? 'stablecoin' : 'native';

            echo '<div class="payment-method-wrapper">';
            echo '<label class="payment-method-item">';
            echo '<input type="checkbox" name="' . $this->settings_key . '[' . esc_attr($args['field']) . '][]" value="' . esc_attr($method_key) . '" data-type="' . esc_attr($data_type) . '" ' . checked($checked, true, false) . ' />';
            echo '<img src="' . esc_url($icon_url) . '" alt="' . esc_attr($method['token']) . '" class="payment-method-icon" />';
            echo '<span class="payment-method-label">' . esc_html($method['label']) . '</span>';
            echo '<span class="payment-method-network">(' . esc_html($method['network_label']) . ')</span>';
            echo '</label>';
            echo '</div>';
        }

        echo '</div>';

        // Add JavaScript for select all functionality
        echo '<script>
        document.addEventListener("DOMContentLoaded", function() {
            const selectAllBtn = document.getElementById("select-all-methods");
            const selectNoneBtn = document.getElementById("select-none-methods");
            const selectStablecoinsBtn = document.getElementById("select-stablecoins");
            const selectNativeBtn = document.getElementById("select-native");
            const checkboxes = document.querySelectorAll(".stablecoinpay-payment-methods input[type=checkbox]");
            const counter = document.getElementById("selection-counter");

            function updateCounter() {
                const checkedCount = document.querySelectorAll(".stablecoinpay-payment-methods input[type=checkbox]:checked").length;
                const totalCount = checkboxes.length;
                if (counter) {
                    counter.textContent = "(" + checkedCount + "/" + totalCount + " selected)";
                }
            }

            // Update counter on page load
            updateCounter();

            // Add change listeners to all checkboxes
            checkboxes.forEach(cb => {
                cb.addEventListener("change", updateCounter);
            });

            if (selectAllBtn) {
                selectAllBtn.addEventListener("click", function(e) {
                    e.preventDefault();
                    checkboxes.forEach(cb => cb.checked = true);
                    updateCounter();
                });
            }

            if (selectNoneBtn) {
                selectNoneBtn.addEventListener("click", function(e) {
                    e.preventDefault();
                    checkboxes.forEach(cb => cb.checked = false);
                    updateCounter();
                });
            }

            if (selectStablecoinsBtn) {
                selectStablecoinsBtn.addEventListener("click", function(e) {
                    e.preventDefault();
                    checkboxes.forEach(cb => {
                        cb.checked = cb.getAttribute("data-type") === "stablecoin";
                    });
                    updateCounter();
                });
            }

            if (selectNativeBtn) {
                selectNativeBtn.addEventListener("click", function(e) {
                    e.preventDefault();
                    checkboxes.forEach(cb => {
                        cb.checked = cb.getAttribute("data-type") === "native";
                    });
                    updateCounter();
                });
            }
        });
        </script>';

        if (!empty($args['description'])) {
            echo '<p class="description">' . esc_html($args['description']) . '</p>';
        }

        // Debug: Show currently saved methods (only when debug mode is enabled in plugin settings)
        if (self::get_setting('debug_mode') && current_user_can('manage_options')) {
            $raw_settings = get_option('stablecoinpay_settings', array());
            $saved_methods = isset($raw_settings['enabled_payment_methods']) ? $raw_settings['enabled_payment_methods'] : 'NOT SET';

            echo '<details style="margin-top: 10px;">';
            echo '<summary style="cursor: pointer; padding: 8px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">';
            echo '<strong>Debug Info (Click to expand)</strong>';
            echo '</summary>';
            echo '<div style="padding: 8px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; margin-top: 5px;">';
            echo 'Currently displayed as checked: <code>' . esc_html(print_r($enabled_methods, true)) . '</code><br>';
            echo 'Raw saved in database: <code>' . esc_html(print_r($saved_methods, true)) . '</code><br>';
            echo 'Settings key: <code>' . esc_html($this->settings_key) . '</code>';
            echo '</div>';
            echo '</details>';
        }
    }

    /**
     * Get available payment methods
     */
    private function get_available_payment_methods() {
        return array(
            'usdt_erc20' => array(
                'token' => 'USDT',
                'blockchain' => 'ETHEREUM',
                'label' => 'USDT ERC20',
                'network_label' => 'Ethereum'
            ),
            'usdt_trc20' => array(
                'token' => 'USDT',
                'blockchain' => 'TRON',
                'label' => 'USDT TRC20',
                'network_label' => 'Tron'
            ),
            'usdt_bep20' => array(
                'token' => 'USDT',
                'blockchain' => 'BSC',
                'label' => 'USDT BEP20',
                'network_label' => 'BSC'
            ),
            'usdt_spl' => array(
                'token' => 'USDT',
                'blockchain' => 'SOLANA',
                'label' => 'USDT SPL',
                'network_label' => 'Solana'
            ),
            'usdt_ton' => array(
                'token' => 'USDT',
                'blockchain' => 'TON',
                'label' => 'USDT TON',
                'network_label' => 'TON'
            ),
            'usdc_erc20' => array(
                'token' => 'USDC',
                'blockchain' => 'ETHEREUM',
                'label' => 'USDC ERC20',
                'network_label' => 'Ethereum'
            ),
            'usdc_trc20' => array(
                'token' => 'USDC',
                'blockchain' => 'TRON',
                'label' => 'USDC TRC20',
                'network_label' => 'Tron'
            ),
            'usdc_bep20' => array(
                'token' => 'USDC',
                'blockchain' => 'BSC',
                'label' => 'USDC BEP20',
                'network_label' => 'BSC'
            ),
            'usdc_spl' => array(
                'token' => 'USDC',
                'blockchain' => 'SOLANA',
                'label' => 'USDC SPL',
                'network_label' => 'Solana'
            ),
            'usdc_ton' => array(
                'token' => 'USDC',
                'blockchain' => 'TON',
                'label' => 'USDC TON',
                'network_label' => 'TON'
            ),
            'dai_erc20' => array(
                'token' => 'DAI',
                'blockchain' => 'ETHEREUM',
                'label' => 'DAI ERC20',
                'network_label' => 'Ethereum'
            ),
            'dai_bep20' => array(
                'token' => 'DAI',
                'blockchain' => 'BSC',
                'label' => 'DAI BEP20',
                'network_label' => 'BSC'
            ),
            'pyusd_erc20' => array(
                'token' => 'PYUSD',
                'blockchain' => 'ETHEREUM',
                'label' => 'PYUSD ERC20',
                'network_label' => 'Ethereum'
            ),
            'busd_bep20' => array(
                'token' => 'BUSD',
                'blockchain' => 'BSC',
                'label' => 'BUSD BEP20',
                'network_label' => 'BSC'
            ),
            'eth_native' => array(
                'token' => 'ETH',
                'blockchain' => 'ETHEREUM',
                'label' => 'Ethereum',
                'network_label' => 'Native'
            ),
            'bnb_native' => array(
                'token' => 'BNB',
                'blockchain' => 'BSC',
                'label' => 'Binance Coin',
                'network_label' => 'Native'
            ),
            'trx_native' => array(
                'token' => 'TRX',
                'blockchain' => 'TRON',
                'label' => 'Tron',
                'network_label' => 'Native'
            ),
            'sol_native' => array(
                'token' => 'SOL',
                'blockchain' => 'SOLANA',
                'label' => 'Solana',
                'network_label' => 'Native'
            ),
            'ton_native' => array(
                'token' => 'TON',
                'blockchain' => 'TON',
                'label' => 'Toncoin',
                'network_label' => 'Native'
            )
        );
    }

    /**
     * Section callbacks (kept for compatibility but not used in custom rendering)
     */
    public function general_section_callback() {
        // Content handled by render_custom_sections()
    }

    public function api_section_callback() {
        // Content handled by render_custom_sections()
    }

    public function payment_section_callback() {
        // Content handled by render_custom_sections()
    }

    public function quick_buy_section_callback() {
        // Content handled by render_custom_sections()
    }

    /**
     * Validate settings
     */
    public function validate_settings($input) {
        $validated = array();
        $has_critical_errors = false;

        // Always validate basic settings first (these don't require API credentials)
        $validated['enabled'] = !empty($input['enabled']) ? 1 : 0;
        $validated['test_mode'] = !empty($input['test_mode']) ? 1 : 0;
        $validated['debug_mode'] = !empty($input['debug_mode']) ? 1 : 0;
        $validated['default_token'] = sanitize_text_field($input['default_token'] ?? 'USDT');
        $validated['default_blockchain'] = sanitize_text_field($input['default_blockchain'] ?? 'ETHEREUM');

        // Validate webhook settings (updated from callback settings)
        $validated['enable_webhooks'] = isset($input['enable_webhooks']) ? 1 : 0;
        $validated['webhook_ip_whitelist'] = sanitize_textarea_field($input['webhook_ip_whitelist'] ?? '');

        // Validate webhook secret
        $webhook_secret = sanitize_text_field($input['webhook_secret'] ?? '');
        if (!empty($webhook_secret)) {
            // Validate webhook secret format and length
            if (strlen($webhook_secret) < 16) {
                add_settings_error($this->settings_key, 'webhook_secret_length',
                    __('Webhook secret must be at least 16 characters long.', 'stablecoinpay-gateway'));
                // Preserve existing webhook secret if validation fails
                $existing_settings = get_option($this->settings_key, array());
                $validated['webhook_secret'] = $existing_settings['webhook_secret'] ?? '';
            } elseif (strlen($webhook_secret) > 256) {
                add_settings_error($this->settings_key, 'webhook_secret_too_long',
                    __('Webhook secret is too long. Maximum 256 characters allowed.', 'stablecoinpay-gateway'));
                // Preserve existing webhook secret if validation fails
                $existing_settings = get_option($this->settings_key, array());
                $validated['webhook_secret'] = $existing_settings['webhook_secret'] ?? '';
            } else {
                // SECURITY FIX: Encrypt webhook secret before storage
                $validated['webhook_secret'] = $this->encrypt_credential($webhook_secret);
            }
        } else {
            // If no webhook secret provided, preserve existing one (webhook secret is optional)
            $existing_settings = get_option($this->settings_key, array());
            $validated['webhook_secret'] = $existing_settings['webhook_secret'] ?? '';
        }

        // Maintain backward compatibility for existing callback settings during migration
        if (isset($input['enable_callbacks'])) {
            $validated['enable_webhooks'] = isset($input['enable_callbacks']) ? 1 : 0;
        }
        if (isset($input['callback_ip_whitelist'])) {
            $validated['webhook_ip_whitelist'] = sanitize_textarea_field($input['callback_ip_whitelist']);
        }

        // Legacy callback settings (deprecated but maintained for compatibility)
        $validated['enable_callbacks'] = $validated['enable_webhooks']; // Mirror webhook setting
        $validated['callback_url_base'] = esc_url_raw($input['callback_url_base'] ?? '');
        $validated['callback_ip_whitelist'] = $validated['webhook_ip_whitelist']; // Mirror webhook setting

        // Validate payment expiry
        $payment_expiry = intval($input['payment_expiry'] ?? 30);
        if ($payment_expiry < 1 || $payment_expiry > 1440) {
            add_settings_error($this->settings_key, 'payment_expiry', __('Payment expiry must be between 1 and 1440 minutes.', 'stablecoinpay-gateway'));
            $validated['payment_expiry'] = 30;
        } else {
            $validated['payment_expiry'] = $payment_expiry;
        }

        // Validate Quick Buy settings
        $validated['enable_quick_buy'] = !empty($input['enable_quick_buy']) ? 1 : 0;
        $validated['quick_buy_button_text'] = sanitize_text_field($input['quick_buy_button_text'] ?? 'Quick Pay with');
        $validated['quick_buy_show_token'] = !empty($input['quick_buy_show_token']) ? 1 : 0;
        $validated['quick_buy_require_name'] = !empty($input['quick_buy_require_name']) ? 1 : 0;

        // Validate enabled payment methods
        if (isset($input['enabled_payment_methods']) && is_array($input['enabled_payment_methods'])) {
            $available_methods = $this->get_available_payment_methods();
            $validated_methods = array();

            foreach ($input['enabled_payment_methods'] as $method_key) {
                $method_key = sanitize_text_field($method_key);
                if (isset($available_methods[$method_key])) {
                    $validated_methods[] = $method_key;
                }
            }

            $validated['enabled_payment_methods'] = $validated_methods;

            // Add success message if methods were saved
            if (!empty($validated_methods)) {
                add_settings_error(
                    $this->settings_key,
                    'payment_methods_saved',
                    sprintf(__('%d payment methods enabled successfully.', 'stablecoinpay-gateway'), count($validated_methods)),
                    'success'
                );
            }
        } else {
            // If no methods selected, save empty array explicitly
            $validated['enabled_payment_methods'] = array();
        }

        // Now validate API settings (these are more critical)
        if (!empty($input['api_url'])) {
            $api_url = esc_url_raw($input['api_url']);
            if (filter_var($api_url, FILTER_VALIDATE_URL)) {
                // Validate URL scheme
                $parsed_url = parse_url($api_url);
                if (!in_array($parsed_url['scheme'], ['http', 'https'])) {
                    add_settings_error($this->settings_key, 'api_url_scheme',
                        __('API URL must use HTTP or HTTPS protocol.', 'stablecoinpay-gateway'));
                    $has_critical_errors = true;
                } else {
                    // Warn about localhost in production
                    if (strpos($api_url, 'localhost') !== false && !WP_DEBUG) {
                        add_settings_error($this->settings_key, 'api_url_localhost',
                            __('Warning: localhost API URL is not suitable for production environment.', 'stablecoinpay-gateway'),
                            'notice-warning');
                    }

                    // Warn about HTTP in production
                    if ($parsed_url['scheme'] === 'http' && !WP_DEBUG) {
                        add_settings_error($this->settings_key, 'api_url_insecure',
                            __('Warning: Using HTTP instead of HTTPS in production is not recommended for security.', 'stablecoinpay-gateway'),
                            'notice-warning');
                    }

                    $validated['api_url'] = rtrim($api_url, '/');
                }
            } else {
                add_settings_error($this->settings_key, 'api_url',
                    __('Invalid API URL format. Please enter a valid URL.', 'stablecoinpay-gateway'));
                $has_critical_errors = true;
            }
        } else {
            // API URL is optional during initial setup
            $validated['api_url'] = '';
        }

        // Validate API credentials (also optional during initial setup)
        $this->validate_api_credentials($input, $validated);

        // Add a general success message if no critical errors
        if (!$has_critical_errors && !get_settings_errors($this->settings_key)) {
            add_settings_error(
                $this->settings_key,
                'settings_saved',
                __('Settings saved successfully!', 'stablecoinpay-gateway'),
                'success'
            );
        }

        return $validated;
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if ('woocommerce_page_stablecoinpay-settings' !== $hook) {
            return;
        }

        wp_enqueue_script(
            'stablecoinpay-admin',
            STABLECOINPAY_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            STABLECOINPAY_VERSION,
            true
        );

        wp_localize_script('stablecoinpay-admin', 'stablecoinpay_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('stablecoinpay_admin'),
            'cloudflare_nonce' => wp_create_nonce('stablecoinpay_cloudflare'),
            'strings' => array(
                'testing_connection' => __('Testing connection...', 'stablecoinpay-gateway'),
                'connection_success' => __('Connection successful!', 'stablecoinpay-gateway'),
                'connection_failed' => __('Connection failed. Please check your settings.', 'stablecoinpay-gateway'),
                'loading_cloudflare_ips' => __('Loading Cloudflare IP ranges...', 'stablecoinpay-gateway'),
                'cloudflare_ips_loaded' => __('Cloudflare IP ranges loaded successfully!', 'stablecoinpay-gateway'),
                'cloudflare_ips_error' => __('Failed to load Cloudflare IP ranges.', 'stablecoinpay-gateway')
            )
        ));

        wp_enqueue_style(
            'stablecoinpay-admin',
            STABLECOINPAY_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            STABLECOINPAY_VERSION
        );
    }

    /**
     * Get setting value with automatic decryption for sensitive credentials
     *
     * @param string $key Setting key
     * @param mixed $default Default value
     * @return mixed Setting value (decrypted if it's a credential)
     */
    public static function get_setting($key, $default = '') {
        $settings = get_option(self::SETTINGS_KEY, array());
        $value = isset($settings[$key]) ? $settings[$key] : $default;

        // SECURITY FIX: Decrypt sensitive credentials
        if (in_array($key, ['api_key', 'api_secret', 'webhook_secret']) && !empty($value)) {
            $instance = new self();
            $decrypted = $instance->decrypt_credential($value);
            return $decrypted !== false ? $decrypted : $default;
        }

        return $value;
    }

    /**
     * Get enabled payment methods
     */
    public static function get_enabled_payment_methods() {
        $enabled_methods = self::get_setting('enabled_payment_methods', null);

        // If the setting has never been saved (null), show all methods
        // If it's an empty array, it means user explicitly selected none, so show all as fallback
        // If it has values, show only selected methods
        if ($enabled_methods === null || (is_array($enabled_methods) && empty($enabled_methods))) {
            // Default to all methods if none selected or never configured
            $settings_instance = new self();
            $all_methods = $settings_instance->get_available_payment_methods();
            return $all_methods;
        }

        if (!is_array($enabled_methods)) {
            // Fallback if data is corrupted
            $settings_instance = new self();
            $all_methods = $settings_instance->get_available_payment_methods();
            return $all_methods;
        }

        $settings_instance = new self();
        $all_methods = $settings_instance->get_available_payment_methods();
        $enabled = array();

        foreach ($enabled_methods as $method_key) {
            if (isset($all_methods[$method_key])) {
                $enabled[$method_key] = $all_methods[$method_key];
            }
        }

        return $enabled;
    }

    /**
     * Check if Quick Buy requires full name from guest users
     *
     * @return bool True if name field should be shown, false if email prefix should be used
     */
    public static function is_quick_buy_name_required() {
        return (bool) self::get_setting('quick_buy_require_name', 1); // Default to true (show name field)
    }

    /**
     * Generate optimal callback URL base based on site configuration
     *
     * @return string Generated callback URL base
     */
    private function generate_callback_url_base() {
        // Start with the WordPress home URL
        $base_url = home_url();

        // Parse the URL to analyze components
        $parsed = parse_url($base_url);

        // Detect if we're on localhost or development environment
        $is_local = $this->is_local_environment($parsed['host'] ?? '');

        // For production environments, prefer HTTPS
        if (!$is_local && (!isset($parsed['scheme']) || $parsed['scheme'] !== 'https')) {
            // Try to construct HTTPS version
            $https_url = 'https://' . ($parsed['host'] ?? '') . ($parsed['path'] ?? '');

            // Test if HTTPS is available
            if ($this->test_url_accessibility($https_url)) {
                $base_url = $https_url;
            }
        }

        // For local development, check if we can determine the correct external URL
        if ($is_local) {
            // Check for common development environment variables
            $external_url = $this->detect_external_url();
            if ($external_url) {
                $base_url = $external_url;
            }
        }

        // Remove trailing slash
        return rtrim($base_url, '/');
    }

    /**
     * Detect if we're in a local development environment
     *
     * @param string $host Hostname to check
     * @return bool True if local environment
     */
    private function is_local_environment($host) {
        $local_hosts = array(
            'localhost',
            '127.0.0.1',
            '::1',
            '0.0.0.0'
        );

        // Check for exact matches
        if (in_array($host, $local_hosts)) {
            return true;
        }

        // Check for local IP ranges
        if (filter_var($host, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            // Check for private IP ranges
            if (filter_var($host, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 | FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
                return true;
            }
        }

        // Check for .local domains
        if (strpos($host, '.local') !== false) {
            return true;
        }

        // Check for development subdomains
        $dev_patterns = array('dev.', 'staging.', 'test.', 'local.');
        foreach ($dev_patterns as $pattern) {
            if (strpos($host, $pattern) === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect external URL for local development environments
     *
     * @return string|null External URL if detected
     */
    private function detect_external_url() {
        // Check for ngrok
        if (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && isset($_SERVER['HTTP_HOST'])) {
            $proto = $_SERVER['HTTP_X_FORWARDED_PROTO'];
            $host = $_SERVER['HTTP_HOST'];

            // Check if it looks like ngrok
            if (strpos($host, '.ngrok.io') !== false || strpos($host, '.ngrok-free.app') !== false) {
                return $proto . '://' . $host;
            }
        }

        // Check for other tunneling services
        $tunnel_patterns = array(
            '.localtunnel.me',
            '.serveo.net',
            '.pagekite.me'
        );

        if (isset($_SERVER['HTTP_HOST'])) {
            $host = $_SERVER['HTTP_HOST'];
            foreach ($tunnel_patterns as $pattern) {
                if (strpos($host, $pattern) !== false) {
                    $proto = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                    return $proto . '://' . $host;
                }
            }
        }

        return null;
    }

    /**
     * Detect current environment and provide appropriate messaging
     *
     * @return array Environment information
     */
    private function detect_environment() {
        $home_url = home_url();
        $parsed = parse_url($home_url);
        $host = $parsed['host'] ?? '';
        $is_local = $this->is_local_environment($host);
        $is_https = isset($parsed['scheme']) && $parsed['scheme'] === 'https';

        $info = array(
            'is_local' => $is_local,
            'is_https' => $is_https,
            'show_warning' => false,
            'message' => ''
        );

        if ($is_local) {
            $info['show_warning'] = true;
            $info['message'] = __('You are in a local development environment. For production, ensure your callback URL is publicly accessible.', 'stablecoinpay-gateway');
        } elseif (!$is_https) {
            $info['show_warning'] = true;
            $info['message'] = __('Your site is not using HTTPS. For security, consider enabling SSL/TLS for production environments.', 'stablecoinpay-gateway');
        }

        return $info;
    }

    /**
     * Test URL accessibility
     *
     * @param string $url URL to test
     * @return bool True if accessible
     */
    private function test_url_accessibility($url) {
        // Simple HEAD request to test if URL is accessible
        $response = wp_remote_head($url, array(
            'timeout' => 5,
            'sslverify' => false // For testing purposes
        ));

        if (is_wp_error($response)) {
            return false;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        return $status_code < 400; // Consider anything under 400 as accessible
    }

    /**
     * Add JavaScript for callback URL functionality
     *
     * @param string $suggested_url Suggested callback URL
     */
    private function add_callback_url_javascript($suggested_url) {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            const suggestedUrl = <?php echo wp_json_encode($suggested_url); ?>;
            const $input = $('#callback_url_base');
            const $preview = $('#callback-url-preview');
            const $status = $('#callback-url-status');

            // Update preview when input changes
            function updatePreview() {
                const baseUrl = $input.val() || suggestedUrl;
                const fullUrl = baseUrl.replace(/\/$/, '') + '/?wc-api=stablecoinpay_callback&order_id=12345';
                $preview.text(fullUrl);
            }

            // Auto-generate button click
            $('#auto-generate-callback-url').on('click', function(e) {
                e.preventDefault();
                const $button = $(this);

                // Add loading state
                $button.addClass('loading').prop('disabled', true);
                $button.find('.dashicons').removeClass('dashicons-admin-site-alt3').addClass('dashicons-update');

                // Simulate brief loading for better UX
                setTimeout(function() {
                    $input.val(suggestedUrl);
                    updatePreview();

                    // Remove loading state
                    $button.removeClass('loading').prop('disabled', false);
                    $button.find('.dashicons').removeClass('dashicons-update').addClass('dashicons-admin-site-alt3');

                    showStatus('success', <?php echo wp_json_encode(__('✨ Callback URL auto-generated successfully!', 'stablecoinpay-gateway')); ?>);
                }, 300);
            });

            // Test URL button click
            $('#test-callback-url').on('click', function(e) {
                e.preventDefault();
                const $button = $(this);
                const originalIcon = $button.find('.dashicons').attr('class');
                const originalText = $button.find('.btn-text').text();

                // Add loading state
                $button.addClass('loading').prop('disabled', true);
                $button.find('.dashicons').attr('class', 'dashicons dashicons-update');
                $button.find('.btn-text').text(<?php echo wp_json_encode(__('Testing...', 'stablecoinpay-gateway')); ?>);

                const testUrl = $input.val() || suggestedUrl;

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'stablecoinpay_test_callback_url',
                        url: testUrl,
                        nonce: <?php echo wp_json_encode(wp_create_nonce('stablecoinpay_test_callback')); ?>
                    },
                    success: function(response) {
                        if (response.success) {
                            showStatus('success', response.data.message);

                            // Briefly show success state on button
                            $button.find('.dashicons').attr('class', 'dashicons dashicons-yes-alt');
                            $button.find('.btn-text').text(<?php echo wp_json_encode(__('Success!', 'stablecoinpay-gateway')); ?>);

                            setTimeout(function() {
                                $button.find('.dashicons').attr('class', originalIcon);
                                $button.find('.btn-text').text(originalText);
                            }, 1500);
                        } else {
                            showStatus('error', response.data || <?php echo wp_json_encode(__('URL test failed', 'stablecoinpay-gateway')); ?>);

                            // Briefly show error state on button
                            $button.find('.dashicons').attr('class', 'dashicons dashicons-warning');
                            $button.find('.btn-text').text(<?php echo wp_json_encode(__('Failed', 'stablecoinpay-gateway')); ?>);

                            setTimeout(function() {
                                $button.find('.dashicons').attr('class', originalIcon);
                                $button.find('.btn-text').text(originalText);
                            }, 1500);
                        }
                    },
                    error: function() {
                        showStatus('error', <?php echo wp_json_encode(__('Connection error during URL test', 'stablecoinpay-gateway')); ?>);

                        // Show error state on button
                        $button.find('.dashicons').attr('class', 'dashicons dashicons-warning');
                        $button.find('.btn-text').text(<?php echo wp_json_encode(__('Error', 'stablecoinpay-gateway')); ?>);

                        setTimeout(function() {
                            $button.find('.dashicons').attr('class', originalIcon);
                            $button.find('.btn-text').text(originalText);
                        }, 1500);
                    },
                    complete: function() {
                        $button.removeClass('loading').prop('disabled', false);
                    }
                });
            });

            // Show status message with enhanced styling
            function showStatus(type, message) {
                const iconClass = type === 'success' ? 'dashicons-yes-alt' : 'dashicons-warning';
                const statusClass = type === 'success' ? 'notice-success' : 'notice-error';

                // Create status message with animation
                const statusHtml =
                    '<div class="notice ' + statusClass + ' inline" style="padding: 12px 16px; margin: 0; opacity: 0; transform: translateY(-10px); transition: all 0.3s ease;">' +
                    '<span class="dashicons ' + iconClass + '" style="margin-right: 8px; vertical-align: middle;"></span>' +
                    '<span style="vertical-align: middle;">' + message + '</span>' +
                    '</div>';

                $status.html(statusHtml);

                // Animate in
                setTimeout(function() {
                    $status.find('.notice').css({
                        'opacity': '1',
                        'transform': 'translateY(0)'
                    });
                }, 50);

                // Auto-hide after 6 seconds with fade out animation
                setTimeout(function() {
                    $status.find('.notice').css({
                        'opacity': '0',
                        'transform': 'translateY(-10px)'
                    });

                    setTimeout(function() {
                        $status.empty();
                    }, 300);
                }, 6000);
            }

            // Update preview on input change
            $input.on('input', updatePreview);

            // Initial preview update
            updatePreview();
        });
        </script>
        <style>
        /* Callback URL Container Styling */
        .stablecoinpay-callback-url-container {
            max-width: 100%;
        }

        /* Input and Button Group Layout */
        .stablecoinpay-input-button-group {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 8px;
        }

        .stablecoinpay-button-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stablecoinpay-callback-url-container .stablecoinpay-callback-url-input {
            min-width: 350px;
            max-width: 500px;
            height: 32px;
            padding: 6px 12px;
            border-radius: 4px;
            border: 1px solid #ddd;
            font-size: 13px;
            line-height: 1.2;
            vertical-align: top;
            flex: 1;
            box-sizing: border-box;
        }

        .stablecoinpay-callback-url-container .stablecoinpay-callback-url-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
            outline: none;
        }

        /* Enhanced Button Styling */
        .stablecoinpay-btn-auto-generate,
        .stablecoinpay-btn-test-url {
            position: relative;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
            font-size: 13px;
            line-height: 1.2;
            height: 32px;
            min-height: 32px;
            transition: all 0.2s ease;
            border: 1px solid;
            text-decoration: none;
            cursor: pointer;
            vertical-align: top;
            white-space: nowrap;
        }

        /* Auto-Generate Button (Primary) */
        .stablecoinpay-btn-auto-generate {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #5a67d8;
            color: #ffffff;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
        }

        .stablecoinpay-btn-auto-generate:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            border-color: #4c51bf;
            color: #ffffff;
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
            transform: translateY(-1px);
        }

        .stablecoinpay-btn-auto-generate:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
        }

        /* Test URL Button (Secondary) */
        .stablecoinpay-btn-test-url {
            background: #ffffff;
            border-color: #d1d5db;
            color: #374151;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stablecoinpay-btn-test-url:hover {
            background: #f9fafb;
            border-color: #9ca3af;
            color: #1f2937;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .stablecoinpay-btn-test-url:active {
            transform: translateY(0);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* Button Icons - Perfect Alignment */
        .stablecoinpay-btn-auto-generate .dashicons,
        .stablecoinpay-btn-test-url .dashicons {
            font-size: 16px;
            width: 16px;
            height: 16px;
            margin: 0;
            padding: 0;
            line-height: 1;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            vertical-align: middle;
        }

        /* Button Text Alignment */
        .stablecoinpay-btn-auto-generate .btn-text,
        .stablecoinpay-btn-test-url .btn-text {
            line-height: 1.2;
            vertical-align: middle;
            display: inline-flex;
            align-items: center;
        }

        /* Disabled State */
        .stablecoinpay-btn-auto-generate:disabled,
        .stablecoinpay-btn-test-url:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        /* Loading State */
        .stablecoinpay-btn-auto-generate.loading,
        .stablecoinpay-btn-test-url.loading {
            pointer-events: none;
        }

        .stablecoinpay-btn-auto-generate.loading .dashicons,
        .stablecoinpay-btn-test-url.loading .dashicons {
            animation: rotation 1s infinite linear;
        }

        /* Preview Section */
        .stablecoinpay-callback-url-container .callback-url-preview {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-top: 12px;
            position: relative;
        }

        .stablecoinpay-callback-url-container .callback-url-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px 8px 0 0;
        }

        .stablecoinpay-callback-url-container .callback-url-preview strong {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }

        .stablecoinpay-callback-url-container .callback-url-preview code {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 8px 12px;
            word-break: break-all;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 12px;
            color: #475569;
            display: block;
            margin-top: 4px;
        }

        /* Status Messages */
        .callback-url-status .notice {
            border-radius: 6px;
            border-left-width: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .callback-url-status .notice-success {
            background: linear-gradient(135deg, #f0fff4 0%, #ecfdf5 100%);
            border-left-color: #10b981;
        }

        .callback-url-status .notice-error {
            background: linear-gradient(135deg, #fef2f2 0%, #fef1f1 100%);
            border-left-color: #ef4444;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .stablecoinpay-input-button-group {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .stablecoinpay-callback-url-container .stablecoinpay-callback-url-input {
                min-width: 100%;
                max-width: 100%;
            }

            .stablecoinpay-button-group {
                justify-content: flex-start;
                flex-wrap: wrap;
            }

            .stablecoinpay-btn-auto-generate,
            .stablecoinpay-btn-test-url {
                flex: 0 0 auto;
            }
        }

        /* Animation Keyframes */
        @keyframes rotation {
            from { transform: rotate(0deg); }
            to { transform: rotate(359deg); }
        }

        /* Focus States for Accessibility */
        .stablecoinpay-btn-auto-generate:focus,
        .stablecoinpay-btn-test-url:focus {
            outline: 2px solid #667eea;
            outline-offset: 2px;
        }
        </style>
        <?php
    }

    /**
     * Validate API credentials with comprehensive checks
     *
     * @param array $input Input data
     * @param array &$validated Validated data array (passed by reference)
     * @return bool True if valid, false otherwise
     */
    private function validate_api_credentials($input, &$validated) {
        // Get existing settings for fallback
        $existing_settings = get_option($this->settings_key, array());

        // Validate API key
        $api_key = sanitize_text_field($input['api_key'] ?? '');

        if (empty($api_key)) {
            // If no API key provided, preserve existing one
            $validated['api_key'] = $existing_settings['api_key'] ?? '';
            $api_key_valid = false;
        } else {
            // Validate new API key
            $api_key_valid = true;

            // Check minimum length
            if (strlen($api_key) < 32) {
                add_settings_error($this->settings_key, 'api_key_length',
                    __('API key must be at least 32 characters long.', 'stablecoinpay-gateway'));
                $validated['api_key'] = $existing_settings['api_key'] ?? '';
                $api_key_valid = false;
            }
            // Check maximum length (prevent extremely long keys)
            elseif (strlen($api_key) > 128) {
                add_settings_error($this->settings_key, 'api_key_too_long',
                    __('API key is too long. Maximum 128 characters allowed.', 'stablecoinpay-gateway'));
                $validated['api_key'] = $existing_settings['api_key'] ?? '';
                $api_key_valid = false;
            }
            // Check format (alphanumeric and common special characters)
            elseif (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $api_key)) {
                add_settings_error($this->settings_key, 'api_key_format',
                    __('API key contains invalid characters. Only alphanumeric characters, underscores, hyphens, and dots are allowed.', 'stablecoinpay-gateway'));
                $validated['api_key'] = $existing_settings['api_key'] ?? '';
                $api_key_valid = false;
            } else {
                // Check for common weak patterns (warning only)
                if (preg_match('/^(test|demo|sample|example|default)/i', $api_key)) {
                    add_settings_error($this->settings_key, 'api_key_weak',
                        __('API key appears to be a test/demo key. Please use your production API key.', 'stablecoinpay-gateway'),
                        'notice-warning');
                }

                // SECURITY FIX: Encrypt API key before storage
                $validated['api_key'] = $this->encrypt_credential($api_key);
            }
        }

        // Validate API secret
        $api_secret = sanitize_text_field($input['api_secret'] ?? '');

        if (empty($api_secret)) {
            // If no API secret provided, preserve existing one
            $validated['api_secret'] = $existing_settings['api_secret'] ?? '';
            $api_secret_valid = false;
        } else {
            // Validate new API secret
            $api_secret_valid = true;

            // Check minimum length for secret
            if (strlen($api_secret) < 32) {
                add_settings_error($this->settings_key, 'api_secret_length',
                    __('API secret must be at least 32 characters long.', 'stablecoinpay-gateway'));
                $validated['api_secret'] = $existing_settings['api_secret'] ?? '';
                $api_secret_valid = false;
            }
            // Check maximum length
            elseif (strlen($api_secret) > 128) {
                add_settings_error($this->settings_key, 'api_secret_too_long',
                    __('API secret is too long. Maximum 128 characters allowed.', 'stablecoinpay-gateway'));
                $validated['api_secret'] = $existing_settings['api_secret'] ?? '';
                $api_secret_valid = false;
            }
            // Check format for secret
            elseif (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $api_secret)) {
                add_settings_error($this->settings_key, 'api_secret_format',
                    __('API secret contains invalid characters. Only alphanumeric characters, underscores, hyphens, and dots are allowed.', 'stablecoinpay-gateway'));
                $validated['api_secret'] = $existing_settings['api_secret'] ?? '';
                $api_secret_valid = false;
            } else {
                // Check for weak patterns in secret (warning only)
                if (preg_match('/^(test|demo|sample|example|default)/i', $api_secret)) {
                    add_settings_error($this->settings_key, 'api_secret_weak',
                        __('API secret appears to be a test/demo secret. Please use your production API secret.', 'stablecoinpay-gateway'),
                        'notice-warning');
                }

                // SECURITY FIX: Encrypt API secret before storage
                $validated['api_secret'] = $this->encrypt_credential($api_secret);
            }
        }

        // Check that key and secret are different (only if both are newly provided)
        if ($api_key_valid && $api_secret_valid && $api_key === $api_secret) {
            add_settings_error($this->settings_key, 'credentials_same',
                __('API key and secret cannot be the same. Please check your credentials.', 'stablecoinpay-gateway'));
            $validated['api_key'] = $existing_settings['api_key'] ?? '';
            $validated['api_secret'] = $existing_settings['api_secret'] ?? '';
            return false;
        }

        // Test API connection if both credentials are provided and valid (use plain text for testing)
        if (!empty($validated['api_url']) && $api_key_valid && $api_secret_valid && !empty($api_key) && !empty($api_secret)) {
            $this->test_api_connection($validated['api_url'], $api_key, $api_secret);
        }

        // Return true if at least one credential was successfully validated or preserved
        return $api_key_valid || $api_secret_valid || !empty($validated['api_key']) || !empty($validated['api_secret']);
    }

    /**
     * Test API connection with provided credentials
     *
     * @param string $api_url API URL
     * @param string $api_key API key
     * @param string $api_secret API secret
     */
    private function test_api_connection($api_url, $api_key, $api_secret) {
        try {
            $response = wp_remote_get($api_url . '/health', array(
                'timeout' => 10,
                'headers' => array(
                    'Authorization' => 'Bearer ' . $api_key,
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'StablecoinPay-WooCommerce/' . STABLECOINPAY_VERSION
                )
            ));

            if (is_wp_error($response)) {
                add_settings_error($this->settings_key, 'api_connection_error',
                    sprintf(__('Could not connect to API: %s', 'stablecoinpay-gateway'), $response->get_error_message()),
                    'notice-warning');
                return;
            }

            $status_code = wp_remote_retrieve_response_code($response);

            if ($status_code === 200) {
                add_settings_error($this->settings_key, 'api_connection_success',
                    __('API connection successful! Your credentials are working correctly.', 'stablecoinpay-gateway'),
                    'success');
            } elseif ($status_code === 401) {
                add_settings_error($this->settings_key, 'api_auth_failed',
                    __('API authentication failed. Please check your API key and secret.', 'stablecoinpay-gateway'),
                    'error');
            } elseif ($status_code === 403) {
                add_settings_error($this->settings_key, 'api_forbidden',
                    __('API access forbidden. Please check your account permissions.', 'stablecoinpay-gateway'),
                    'error');
            } else {
                add_settings_error($this->settings_key, 'api_unexpected_response',
                    sprintf(__('Unexpected API response: HTTP %d', 'stablecoinpay-gateway'), $status_code),
                    'notice-warning');
            }

        } catch (Exception $e) {
            add_settings_error($this->settings_key, 'api_test_exception',
                sprintf(__('API test failed: %s', 'stablecoinpay-gateway'), $e->getMessage()),
                'notice-warning');
        }
    }

    /**
     * SECURITY FIX: Encrypt sensitive credentials before database storage
     *
     * @param string $credential The credential to encrypt
     * @return string Encrypted credential
     */
    private function encrypt_credential($credential) {
        if (empty($credential)) {
            return $credential;
        }

        try {
            // Use WordPress salts for encryption key derivation
            $key = $this->get_encryption_key();

            // Generate random IV for each encryption
            $iv = openssl_random_pseudo_bytes(16);

            // Encrypt using AES-256-CBC
            $encrypted = openssl_encrypt($credential, 'AES-256-CBC', $key, 0, $iv);

            if ($encrypted === false) {
                error_log('StablecoinPay: Failed to encrypt credential');
                return $credential; // Fallback to plain text if encryption fails
            }

            // Combine IV and encrypted data, then base64 encode
            $result = base64_encode($iv . $encrypted);

            return 'encrypted:' . $result;

        } catch (Exception $e) {
            error_log('StablecoinPay: Encryption error: ' . $e->getMessage());
            return $credential; // Fallback to plain text if encryption fails
        }
    }

    /**
     * SECURITY FIX: Decrypt sensitive credentials from database storage
     *
     * @param string $encrypted_credential The encrypted credential
     * @return string|false Decrypted credential or false on failure
     */
    private function decrypt_credential($encrypted_credential) {
        if (empty($encrypted_credential)) {
            return $encrypted_credential;
        }

        // Check if credential is encrypted
        if (strpos($encrypted_credential, 'encrypted:') !== 0) {
            // Not encrypted, return as-is (backward compatibility)
            return $encrypted_credential;
        }

        try {
            // Remove encryption prefix
            $encrypted_data = substr($encrypted_credential, 10);

            // Decode base64
            $data = base64_decode($encrypted_data);

            if ($data === false || strlen($data) < 16) {
                error_log('StablecoinPay: Invalid encrypted credential format');
                return false;
            }

            // Extract IV and encrypted content
            $iv = substr($data, 0, 16);
            $encrypted = substr($data, 16);

            // Get encryption key
            $key = $this->get_encryption_key();

            // Decrypt
            $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);

            if ($decrypted === false) {
                error_log('StablecoinPay: Failed to decrypt credential');
                return false;
            }

            return $decrypted;

        } catch (Exception $e) {
            error_log('StablecoinPay: Decryption error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * SECURITY FIX: Generate encryption key from WordPress salts
     *
     * @return string Encryption key
     */
    private function get_encryption_key() {
        // Use WordPress salts to create a consistent encryption key
        $salt_data = AUTH_KEY . SECURE_AUTH_KEY . LOGGED_IN_KEY . NONCE_KEY .
                    AUTH_SALT . SECURE_AUTH_SALT . LOGGED_IN_SALT . NONCE_SALT;

        // Add plugin-specific salt
        $plugin_salt = 'stablecoinpay_credential_encryption_v1';

        // Create 32-byte key using hash
        return hash('sha256', $salt_data . $plugin_salt, true);
    }

    /**
     * SECURITY FIX: Migrate existing plain text credentials to encrypted format
     * This method should be called during plugin update
     */
    public static function migrate_credentials_to_encrypted() {
        $settings = get_option(self::SETTINGS_KEY, array());
        $updated = false;
        $instance = new self();

        // Migrate API key if it's not encrypted
        if (!empty($settings['api_key']) && strpos($settings['api_key'], 'encrypted:') !== 0) {
            $settings['api_key'] = $instance->encrypt_credential($settings['api_key']);
            $updated = true;
        }

        // Migrate API secret if it's not encrypted
        if (!empty($settings['api_secret']) && strpos($settings['api_secret'], 'encrypted:') !== 0) {
            $settings['api_secret'] = $instance->encrypt_credential($settings['api_secret']);
            $updated = true;
        }

        // Migrate webhook secret if it's not encrypted
        if (!empty($settings['webhook_secret']) && strpos($settings['webhook_secret'], 'encrypted:') !== 0) {
            $settings['webhook_secret'] = $instance->encrypt_credential($settings['webhook_secret']);
            $updated = true;
        }

        // Update settings if any credentials were migrated
        if ($updated) {
            update_option(self::SETTINGS_KEY, $settings);
            error_log('StablecoinPay: Migrated credentials to encrypted format');
        }
    }
}
