<?php
/**
 * StablecoinPay Quick Buy Handler
 * 
 * Main class for handling Quick Buy functionality on product and cart pages
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_Quick_Buy {
    
    private static $instance = null;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Initialize Quick Buy functionality
     */
    public function init() {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('StablecoinPay Quick Buy: Initializing...');
        }

        // Check if Quick Buy is enabled in settings (default: enabled)
        if (!StablecoinPay_Settings::get_setting('enable_quick_buy', 1)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Quick Buy: Disabled in settings');
            }
            return;
        }

        // Initialize components regardless of gateway status for debugging
        $this->init_components();

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('StablecoinPay Quick Buy: Initialization complete');
        }
    }
    
    /**
     * Initialize Quick Buy components
     */
    private function init_components() {
        // Include required files
        require_once STABLECOINPAY_PLUGIN_PATH . 'includes/class-stablecoinpay-quick-buy-buttons.php';
        require_once STABLECOINPAY_PLUGIN_PATH . 'includes/class-stablecoinpay-quick-buy-ajax.php';
        require_once STABLECOINPAY_PLUGIN_PATH . 'includes/class-stablecoinpay-quick-buy-orders.php';
        
        // Initialize components
        new StablecoinPay_Quick_Buy_Buttons();
        new StablecoinPay_Quick_Buy_Ajax();
    }
    
    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        if (!$this->should_load_scripts()) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Quick Buy: Scripts not loaded - should_load_scripts() returned false');
            }
            return;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('StablecoinPay Quick Buy: Enqueuing scripts');
        }

        // Enqueue JavaScript
        wp_enqueue_script(
            'stablecoinpay-quick-buy',
            STABLECOINPAY_PLUGIN_URL . 'assets/js/quick-buy.js',
            array('jquery'),
            STABLECOINPAY_VERSION . '-' . time(), // Add timestamp to prevent caching issues
            true
        );
        
        // Enqueue CSS
        wp_enqueue_style(
            'stablecoinpay-quick-buy',
            STABLECOINPAY_PLUGIN_URL . 'assets/css/quick-buy.css',
            array(),
            STABLECOINPAY_VERSION
        );
        
        // Localize script with necessary data
        wp_localize_script('stablecoinpay-quick-buy', 'stablecoinpay_quick_buy', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('stablecoinpay_quick_buy'),
            'enabled_methods' => StablecoinPay_Settings::get_enabled_payment_methods(),
            'require_name' => StablecoinPay_Settings::is_quick_buy_name_required(),
            'strings' => array(
                'quick_pay_with' => __('Quick Pay with', 'stablecoinpay-gateway'),
                'button_text' => StablecoinPay_Settings::get_setting('quick_buy_button_text', 'Quick Pay with'),
                'select_token' => __('Select Token', 'stablecoinpay-gateway'),
                'enter_email' => __('Enter your email to continue', 'stablecoinpay-gateway'),
                'processing' => __('Processing...', 'stablecoinpay-gateway'),
                'error' => __('An error occurred. Please try again.', 'stablecoinpay-gateway'),
                'invalid_email' => __('Please enter a valid email address.', 'stablecoinpay-gateway'),
                'select_options' => __('Please select product options before proceeding.', 'stablecoinpay-gateway'),
                'invalid_quantity' => __('Please enter a valid quantity.', 'stablecoinpay-gateway')
            )
        ));
    }
    
    /**
     * Check if StablecoinPay gateway is enabled
     */
    private function is_gateway_enabled() {
        if (!class_exists('WC_StablecoinPay_Gateway')) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Quick Buy: Gateway class not found');
            }
            return false;
        }

        $gateway = new WC_StablecoinPay_Gateway();
        $is_available = $gateway->is_available();

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('StablecoinPay Quick Buy: Gateway available: ' . ($is_available ? 'YES' : 'NO'));
        }

        return $is_available;
    }
    
    /**
     * Check if scripts should be loaded on current page
     */
    private function should_load_scripts() {
        $should_load = is_product() || is_cart() || is_shop() || is_product_category() || is_woocommerce();

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('StablecoinPay Quick Buy: should_load_scripts() - ' .
                'is_product: ' . (is_product() ? 'YES' : 'NO') . ', ' .
                'is_cart: ' . (is_cart() ? 'YES' : 'NO') . ', ' .
                'is_shop: ' . (is_shop() ? 'YES' : 'NO') . ', ' .
                'is_woocommerce: ' . (is_woocommerce() ? 'YES' : 'NO') . ', ' .
                'result: ' . ($should_load ? 'YES' : 'NO')
            );
        }

        return $should_load;
    }
}

// Initialize Quick Buy functionality
add_action('plugins_loaded', function() {
    if (class_exists('StablecoinPay_Settings')) {
        StablecoinPay_Quick_Buy::get_instance();
    }
}, 20);
