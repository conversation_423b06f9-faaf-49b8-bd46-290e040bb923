<?php
/**
 * Security Utilities
 * 
 * Provides security functions for the StablecoinPay plugin
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_Security {

    /**
     * Validate payment access
     * 
     * @param int $order_id Order ID
     * @param string $payment_id Payment ID
     * @return bool True if access is allowed, false otherwise
     */
    public static function validate_payment_access($order_id, $payment_id) {
        // Verify order exists
        $order = StablecoinPay_HPOS::get_order($order_id);
        if (!$order) {
            return false;
        }

        // Check if user has access to this order
        $current_user_id = get_current_user_id();
        $order_user_id = $order->get_user_id();

        // Allow access if:
        // 1. User owns the order
        // 2. User is admin
        // 3. Order key matches (for guest orders)
        if ($current_user_id && $current_user_id === $order_user_id) {
            return true;
        }

        if (current_user_can('manage_woocommerce')) {
            return true;
        }

        // Check order key for guest orders
        $order_key = sanitize_text_field($_GET['key'] ?? '');
        if ($order_key && $order->get_order_key() === $order_key) {
            return true;
        }

        return false;
    }

    /**
     * Sanitize payment data
     * 
     * @param array $data Payment data
     * @return array Sanitized payment data
     * @throws Exception If validation fails
     */
    public static function sanitize_payment_data($data) {
        $sanitized = array();

        $sanitized['amount'] = floatval($data['amount'] ?? 0);
        $sanitized['currency'] = sanitize_text_field($data['currency'] ?? 'USD');
        $sanitized['token'] = sanitize_text_field($data['token'] ?? 'USDT');
        $sanitized['blockchain'] = sanitize_text_field($data['blockchain'] ?? 'ETHEREUM');
        $sanitized['network'] = sanitize_text_field($data['network'] ?? 'MAINNET');

        // Validate and standardize amount with token-specific precision
        $amount_validation = self::validate_and_standardize_amount($data['amount'] ?? 0, $sanitized['token']);

        if (!$amount_validation['valid']) {
            throw new Exception($amount_validation['error']);
        }

        // Use the standardized amount
        $sanitized['amount'] = $amount_validation['amount'];

        // Additional business logic validation
        $float_amount = floatval($sanitized['amount']);

        // Check minimum amount (equivalent to $0.01 USD)
        if ($float_amount < 0.01) {
            throw new Exception(__('Amount too small. Minimum amount is $0.01 USD equivalent', 'stablecoinpay-gateway'));
        }

        // Check maximum amount (equivalent to $100,000 USD for security)
        if ($float_amount > 100000) {
            throw new Exception(__('Amount too large. Maximum amount is $100,000 USD equivalent', 'stablecoinpay-gateway'));
        }

        // Validate currency against comprehensive list
        $allowed_currencies = array(
            'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
            'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'RUB', 'INR', 'BRL', 'ZAR', 'KRW',
            'PLN', 'CZK', 'HUF', 'RON', 'BGN', 'HRK', 'DKK', 'ISK', 'THB', 'MYR',
            'PHP', 'IDR', 'VND', 'ILS', 'CLP', 'PEN', 'COP', 'ARS', 'UYU'
        );

        if (!in_array($sanitized['currency'], $allowed_currencies)) {
            throw new Exception(sprintf(
                __('Invalid currency: %s. Supported currencies: %s', 'stablecoinpay-gateway'),
                $sanitized['currency'],
                implode(', ', $allowed_currencies)
            ));
        }

        // Validate token and blockchain combination with comprehensive mapping
        $supported_tokens = array(
            'ETHEREUM' => array('USDT', 'USDC', 'DAI', 'PYUSD', 'ETH'),
            'BSC' => array('USDT', 'USDC', 'DAI', 'BUSD', 'BNB'),
            'TRON' => array('USDT', 'USDC', 'TRX'),
            'SOLANA' => array('USDT', 'USDC', 'SOL'),
            'TON' => array('USDT', 'USDC', 'TON')
        );

        // Validate blockchain exists
        if (!isset($supported_tokens[$sanitized['blockchain']])) {
            throw new Exception(sprintf(
                __('Unsupported blockchain: %s. Supported blockchains: %s', 'stablecoinpay-gateway'),
                $sanitized['blockchain'],
                implode(', ', array_keys($supported_tokens))
            ));
        }

        // Validate token is supported on the blockchain
        if (!in_array($sanitized['token'], $supported_tokens[$sanitized['blockchain']])) {
            throw new Exception(sprintf(
                __('Token %s is not supported on %s blockchain. Supported tokens: %s', 'stablecoinpay-gateway'),
                $sanitized['token'],
                $sanitized['blockchain'],
                implode(', ', $supported_tokens[$sanitized['blockchain']])
            ));
        }

        return $sanitized;
    }

    /**
     * Verify nonce
     * 
     * @param string $action Nonce action
     * @param string $nonce Nonce value
     * @return bool True if nonce is valid, false otherwise
     */
    public static function verify_nonce($action, $nonce) {
        return wp_verify_nonce($nonce, $action);
    }

    /**
     * ARCHITECTURAL FIX: Rate limiting removed from plugin
     * Backend API handles all rate limiting - no need for duplicate limiting
     *
     * @deprecated Rate limiting moved to backend API for better architecture
     * @param string $action Action being rate limited
     * @param string $identifier Unique identifier (IP, user ID, etc.)
     * @param int $limit Maximum attempts (ignored)
     * @param int $window Time window in seconds (ignored)
     * @return bool Always returns true - rate limiting handled by backend
     */
    public static function rate_limit_check($action, $identifier, $limit = 10, $window = 3600) {
        // ARCHITECTURAL FIX: Backend API handles rate limiting
        // Removing duplicate rate limiting from plugin to prevent conflicts

        // Log deprecation warning in debug mode
        if (WP_DEBUG) {
            error_log('StablecoinPay: rate_limit_check() is deprecated - rate limiting handled by backend API');
        }

        // Always return true - backend handles rate limiting
        return true;
    }

    /**
     * DEPRECATED: Rate limiting table creation
     *
     * @deprecated Rate limiting moved to backend API
     */
    private static function create_rate_limit_table() {
        // ARCHITECTURAL FIX: No longer creating rate limit table
        // Backend API handles all rate limiting

        if (WP_DEBUG) {
            error_log('StablecoinPay: create_rate_limit_table() is deprecated - rate limiting handled by backend API');
        }
    }

    /**
     * Validate callback signature
     * 
     * @param string $payload Callback payload
     * @param string $signature Provided signature
     * @param string $secret API secret
     * @return bool True if signature is valid, false otherwise
     */
    public static function validate_callback_signature($payload, $signature, $secret) {
        if (empty($signature) || empty($secret)) {
            return false;
        }

        $expected_signature = hash_hmac('sha256', $payload, $secret);
        
        return hash_equals($expected_signature, $signature);
    }

    /**
     * Sanitize callback data
     * 
     * @param array $data Callback data
     * @return array Sanitized callback data
     */
    public static function sanitize_callback_data($data) {
        $sanitized = array();

        $sanitized['id'] = sanitize_text_field($data['id'] ?? '');
        $sanitized['status'] = sanitize_text_field($data['status'] ?? '');
        $sanitized['txHash'] = sanitize_text_field($data['txHash'] ?? '');
        $sanitized['amount'] = floatval($data['amount'] ?? 0);
        $sanitized['token'] = sanitize_text_field($data['token'] ?? '');
        $sanitized['blockchain'] = sanitize_text_field($data['blockchain'] ?? '');
        $sanitized['network'] = sanitize_text_field($data['network'] ?? '');

        // Sanitize metadata if present
        if (isset($data['metadata']) && is_array($data['metadata'])) {
            $sanitized['metadata'] = array();
            foreach ($data['metadata'] as $key => $value) {
                $sanitized['metadata'][sanitize_key($key)] = sanitize_text_field($value);
            }
        }

        return $sanitized;
    }

    /**
     * Generate secure random string
     * 
     * @param int $length String length
     * @return string Random string
     */
    public static function generate_random_string($length = 32) {
        if (function_exists('random_bytes')) {
            return bin2hex(random_bytes($length / 2));
        }

        // Fallback for older PHP versions
        return substr(str_shuffle(str_repeat('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length / 62))), 0, $length);
    }

    /**
     * Validate IP address
     * 
     * @param string $ip IP address
     * @return bool True if valid IP, false otherwise
     */
    public static function validate_ip($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * Check if IP is in whitelist
     * 
     * @param string $ip IP address to check
     * @param string $whitelist Comma-separated list of allowed IPs/ranges
     * @return bool True if IP is allowed, false otherwise
     */
    public static function is_ip_whitelisted($ip, $whitelist) {
        if (empty($whitelist)) {
            return true; // No whitelist means all IPs are allowed
        }

        $allowed_ips = array_map('trim', explode(',', $whitelist));

        foreach ($allowed_ips as $allowed_ip) {
            if (empty($allowed_ip)) {
                continue;
            }

            // Check for CIDR notation
            if (strpos($allowed_ip, '/') !== false) {
                if (self::ip_in_range($ip, $allowed_ip)) {
                    return true;
                }
            } else {
                if ($ip === $allowed_ip) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check if IP is in CIDR range
     * 
     * @param string $ip IP address
     * @param string $range CIDR range
     * @return bool True if IP is in range, false otherwise
     */
    public static function ip_in_range($ip, $range) {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }

        list($subnet, $bits) = explode('/', $range);
        
        if ($bits === null) {
            $bits = 32;
        }

        $ip_long = ip2long($ip);
        $subnet_long = ip2long($subnet);
        
        if ($ip_long === false || $subnet_long === false) {
            return false;
        }

        $mask = -1 << (32 - $bits);
        $subnet_long &= $mask;

        return ($ip_long & $mask) == $subnet_long;
    }

    /**
     * Escape output for display
     * 
     * @param mixed $data Data to escape
     * @return mixed Escaped data
     */
    public static function escape_output($data) {
        if (is_string($data)) {
            return esc_html($data);
        }

        if (is_array($data)) {
            return array_map(array(self::class, 'escape_output'), $data);
        }

        return $data;
    }

    /**
     * Log security event
     * 
     * @param string $event Event description
     * @param array $context Additional context
     */
    public static function log_security_event($event, $context = array()) {
        $logger = wc_get_logger();
        
        $message = sprintf(
            'Security Event: %s - IP: %s - User Agent: %s',
            $event,
            self::get_client_ip(),
            sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown')
        );

        if (!empty($context)) {
            $message .= ' - Context: ' . wp_json_encode($context);
        }

        $logger->warning($message, array('source' => 'stablecoinpay-security'));
    }

    /**
     * Get client IP address
     * 
     * @return string Client IP address
     */
    public static function get_client_ip() {
        $ip_keys = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = sanitize_text_field($_SERVER[$key]);
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (self::validate_ip($ip)) {
                    return $ip;
                }
            }
        }

        return sanitize_text_field($_SERVER['REMOTE_ADDR'] ?? '');
    }

    /**
     * Get precision for specific token
     *
     * @param string $token Token symbol
     * @return int Decimal precision
     */
    public static function get_token_precision($token) {
        $token_precision = array(
            // Stablecoins with their actual on-chain precision
            'USDT' => 6,  // Tether uses 6 decimals on most networks
            'USDC' => 6,  // USD Coin uses 6 decimals
            'DAI' => 18,  // DAI uses 18 decimals (ERC-20 standard)
            'PYUSD' => 6, // PayPal USD uses 6 decimals
            'BUSD' => 18, // Binance USD uses 18 decimals

            // Native coins
            'ETH' => 18,  // Ethereum uses 18 decimals
            'BNB' => 18,  // Binance Coin uses 18 decimals
            'TRX' => 6,   // Tron uses 6 decimals
            'SOL' => 9,   // Solana uses 9 decimals
            'TON' => 9    // TON uses 9 decimals
        );

        return $token_precision[strtoupper($token)] ?? 8; // Default to 8 decimals for unknown tokens
    }

    /**
     * DEPRECATED: Standardize decimal precision for cryptocurrency amounts
     *
     * CRITICAL WARNING: This function manipulates API amounts incorrectly.
     * API amounts should be used exactly as returned without modification.
     *
     * @deprecated This function causes critical amount display issues
     * @param float|string $amount Amount to standardize
     * @param string $token Token type for precision determination
     * @return string Original amount without manipulation
     */
    public static function standardize_decimal_precision($amount, $token = '') {
        // CRITICAL FIX: Return original amount without any manipulation
        // This function should not be used for API amounts
        error_log('WARNING: standardize_decimal_precision() is deprecated and should not be used for API amounts');

        // Return the original amount as-is to prevent manipulation
        return (string)$amount;
    }

    /**
     * Validate and standardize cryptocurrency amount
     *
     * @param float|string $amount Amount to validate and standardize
     * @param string $token Token type for specific validation
     * @return array Array with 'valid' boolean and 'amount' string
     */
    public static function validate_and_standardize_amount($amount, $token = '') {
        $result = array('valid' => false, 'amount' => '0', 'error' => '');

        // Basic validation
        if (!is_numeric($amount)) {
            $result['error'] = __('Amount must be a valid number', 'stablecoinpay-gateway');
            return $result;
        }

        $float_amount = floatval($amount);

        // Check for negative amounts
        if ($float_amount <= 0) {
            $result['error'] = __('Amount must be greater than zero', 'stablecoinpay-gateway');
            return $result;
        }

        // Get token-specific precision
        $precision = self::get_token_precision($token);

        // Check minimum amount based on token precision
        $min_amount = 1 / pow(10, $precision); // Smallest unit for this token
        if ($float_amount < $min_amount) {
            $result['error'] = sprintf(
                __('Amount too small. Minimum amount is %s %s', 'stablecoinpay-gateway'),
                $min_amount,
                $token
            );
            return $result;
        }

        // Check maximum amount (prevent overflow)
        $max_amount = 999999999.99999999;
        if ($float_amount > $max_amount) {
            $result['error'] = __('Amount too large. Maximum amount exceeded', 'stablecoinpay-gateway');
            return $result;
        }

        // Check decimal places don't exceed token precision
        $amount_str = (string)$amount;
        if (strpos($amount_str, '.') !== false) {
            $decimal_places = strlen(substr(strrchr($amount_str, "."), 1));
            if ($decimal_places > $precision) {
                $result['error'] = sprintf(
                    __('Too many decimal places for %s. Maximum %d decimal places allowed', 'stablecoinpay-gateway'),
                    $token,
                    $precision
                );
                return $result;
            }
        }

        $result['valid'] = true;
        // CRITICAL FIX: Return original amount without standardization
        $result['amount'] = (string)$amount;

        return $result;
    }

    /**
     * Compare amounts with token-specific precision
     *
     * @param float|string $amount1 First amount
     * @param float|string $amount2 Second amount
     * @param string $token Token type for precision
     * @return bool True if amounts are equal within token precision
     */
    public static function amounts_equal($amount1, $amount2, $token = '') {
        $standardized1 = self::standardize_decimal_precision($amount1, $token);
        $standardized2 = self::standardize_decimal_precision($amount2, $token);

        return $standardized1 === $standardized2;
    }

    /**
     * Generate unique payment amount with token-specific precision
     * This ensures payment detection works correctly by creating amounts
     * that are unique but respect token precision limits
     *
     * @param float $base_amount Base amount
     * @param string $token Token type
     * @return string Unique amount with proper precision
     */
    public static function generate_unique_payment_amount($base_amount, $token = '') {
        $precision = self::get_token_precision($token);

        // Generate a small random offset within token precision
        $max_offset = 1 / pow(10, $precision); // Smallest unit
        $random_offset = mt_rand(1, 99) * $max_offset; // 1-99 smallest units

        $unique_amount = $base_amount + $random_offset;

        return self::standardize_decimal_precision($unique_amount, $token);
    }
}
