<?php
/**
 * Payment Manager
 * 
 * Handles payment data storage and retrieval
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_Payment_Manager {

    /**
     * Store payment data with transaction safety
     *
     * @param int $order_id WooCommerce order ID
     * @param array $payment_data Payment data from API
     * @return bool True on success, false on failure
     */
    public static function store_payment($order_id, $payment_data) {
        global $wpdb;

        // Validate required fields
        if (empty($payment_data['id']) || empty($order_id)) {
            error_log('StablecoinPay: Missing required payment data - ID or Order ID');
            return false;
        }

        // Start transaction
        $wpdb->query('START TRANSACTION');

        try {
            $table_name = $wpdb->prefix . 'stablecoinpay_payments';

            // Validate payment data
            $validated_data = self::validate_payment_data($payment_data);
            if (!$validated_data) {
                throw new Exception('Invalid payment data provided');
            }

            $token = sanitize_text_field($validated_data['token'] ?? 'USDT');

            $data = array(
                'order_id' => intval($order_id),
                'payment_id' => sanitize_text_field($validated_data['id']),
                'status' => sanitize_text_field($validated_data['status']),
                'token' => $token,
                'blockchain' => sanitize_text_field($validated_data['blockchain'] ?? 'ETHEREUM'),
                'network' => sanitize_text_field($validated_data['network'] ?? 'MAINNET'),
                'target_amount' => self::sanitize_decimal($validated_data['targetAmount'] ?? 0, $token),
                'request_amount' => self::sanitize_decimal($validated_data['requestAmount'] ?? 0, $token),
                'fiat_amount' => self::sanitize_decimal($validated_data['fiatAmount'] ?? 0), // Fiat doesn't need token precision
                'fiat_currency' => sanitize_text_field($validated_data['fiatCurrency'] ?? 'USD'),
                'wallet_address' => sanitize_text_field($validated_data['walletAddress'] ?? ''),
                'tx_hash' => sanitize_text_field($validated_data['txHash'] ?? ''),
                'webhook_endpoint_id' => intval($validated_data['webhookEndpointId'] ?? 0),
                'metadata' => wp_json_encode($validated_data['metadata'] ?? array()),
                'created_at' => current_time('mysql')
            );

            // Check for duplicate payment ID
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $table_name WHERE payment_id = %s",
                $data['payment_id']
            ));

            if ($existing) {
                throw new Exception('Payment ID already exists: ' . $data['payment_id']);
            }

            $result = $wpdb->insert($table_name, $data);

            if ($result === false) {
                throw new Exception('Database insert failed: ' . $wpdb->last_error);
            }

            // Log the transaction
            self::log_transaction_event($data['payment_id'], 'payment_created', $validated_data);

            // Commit transaction
            $wpdb->query('COMMIT');
            return true;

        } catch (Exception $e) {
            // Rollback transaction
            $wpdb->query('ROLLBACK');
            error_log('StablecoinPay: Failed to store payment data: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Validate payment data structure and values
     *
     * @param array $payment_data Raw payment data
     * @return array|false Validated data or false on failure
     */
    private static function validate_payment_data($payment_data) {
        $required_fields = ['id', 'status'];

        foreach ($required_fields as $field) {
            if (empty($payment_data[$field])) {
                error_log("StablecoinPay: Missing required field: $field");
                return false;
            }
        }

        // Validate status
        $valid_statuses = ['pending', 'processing', 'completed', 'failed', 'expired', 'cancelled'];
        if (!in_array($payment_data['status'], $valid_statuses)) {
            error_log("StablecoinPay: Invalid status: " . $payment_data['status']);
            return false;
        }

        // Validate amounts are numeric
        $amount_fields = ['targetAmount', 'requestAmount', 'fiatAmount'];
        foreach ($amount_fields as $field) {
            if (isset($payment_data[$field]) && !is_numeric($payment_data[$field])) {
                error_log("StablecoinPay: Invalid amount field $field: " . $payment_data[$field]);
                return false;
            }
        }

        return $payment_data;
    }

    /**
     * Sanitize decimal values for database storage with token-specific precision
     *
     * @param mixed $value Value to sanitize
     * @param string $token Token type for precision determination
     * @return string Sanitized decimal value with proper precision
     */
    private static function sanitize_decimal($value, $token = '') {
        if (!is_numeric($value)) {
            // Return zero with appropriate precision
            $precision = StablecoinPay_Security::get_token_precision($token);
            return number_format(0, $precision, '.', '');
        }

        // Use token-specific standardization
        return StablecoinPay_Security::standardize_decimal_precision($value, $token);
    }

    /**
     * Update payment data with transaction safety
     *
     * @param string $payment_id Payment ID
     * @param array $update_data Data to update
     * @return bool True on success, false on failure
     */
    public static function update_payment($payment_id, $update_data) {
        return StablecoinPay_Database::execute_in_transaction(
            array(__CLASS__, 'update_payment_no_transaction'),
            array($payment_id, $update_data)
        );
    }

    /**
     * Update payment data without transaction wrapper (for use within existing transactions)
     *
     * @param string $payment_id Payment ID
     * @param array $update_data Data to update
     * @return bool True on success, false on failure
     */
    public static function update_payment_no_transaction($payment_id, $update_data) {
        global $wpdb;

        try {
            $table_name = $wpdb->prefix . 'stablecoinpay_payments';

            $data = array();

            if (isset($update_data['status'])) {
                // Validate status
                $valid_statuses = ['pending', 'processing', 'completed', 'failed', 'expired', 'cancelled'];
                if (!in_array($update_data['status'], $valid_statuses)) {
                    throw new Exception('Invalid payment status: ' . $update_data['status']);
                }
                $data['status'] = sanitize_text_field($update_data['status']);
            }

            if (isset($update_data['tx_hash'])) {
                $data['tx_hash'] = sanitize_text_field($update_data['tx_hash']);
            }

            // Get token for precision handling
            $token = '';
            if (isset($update_data['token'])) {
                $token = $update_data['token'];
            } else {
                // Get token from existing payment data
                $existing_payment = self::get_payment($payment_id);
                $token = $existing_payment['token'] ?? '';
            }

            if (isset($update_data['target_amount'])) {
                $data['target_amount'] = self::sanitize_decimal($update_data['target_amount'], $token);
            }

            if (isset($update_data['confirmed_amount'])) {
                $data['confirmed_amount'] = self::sanitize_decimal($update_data['confirmed_amount'], $token);
            }

            if (isset($update_data['confirmations'])) {
                $data['confirmations'] = intval($update_data['confirmations']);
            }

            if (isset($update_data['metadata'])) {
                $data['metadata'] = wp_json_encode($update_data['metadata']);
            }

            if (empty($data)) {
                throw new Exception('No valid data to update');
            }

            $data['updated_at'] = current_time('mysql');

            $result = $wpdb->update(
                $table_name,
                $data,
                array('payment_id' => sanitize_text_field($payment_id)),
                null,
                array('%s')
            );

            if ($result === false) {
                throw new Exception('Database update failed: ' . $wpdb->last_error);
            }

            // Log the update
            self::log_transaction_event($payment_id, 'payment_updated', $update_data);

            return true;

        } catch (Exception $e) {
            error_log('StablecoinPay: Failed to update payment: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get payment data
     * 
     * @param string $payment_id Payment ID
     * @return array|null Payment data or null if not found
     */
    public static function get_payment($payment_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'stablecoinpay_payments';

        $payment = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE payment_id = %s",
                sanitize_text_field($payment_id)
            ),
            ARRAY_A
        );

        if ($payment) {
            $payment['metadata'] = json_decode($payment['metadata'], true);
        }

        return $payment;
    }

    /**
     * Get payment by order ID
     *
     * @param int $order_id Order ID
     * @return array|null Payment data or null if not found
     */
    public static function get_payment_by_order($order_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'stablecoinpay_payments';

        $payment = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE order_id = %d ORDER BY created_at DESC LIMIT 1",
                intval($order_id)
            ),
            ARRAY_A
        );

        if ($payment) {
            $payment['metadata'] = json_decode($payment['metadata'], true);
        }

        return $payment;
    }

    /**
     * SECURITY FIX: Get payment by transaction hash (excluding specific order)
     *
     * @param string $tx_hash Transaction hash
     * @param int $exclude_order_id Order ID to exclude from search
     * @return array|null Payment data or null if not found
     */
    public static function get_payment_by_tx_hash($tx_hash, $exclude_order_id = 0) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'stablecoinpay_payments';

        $payment = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE tx_hash = %s AND order_id != %d LIMIT 1",
                sanitize_text_field($tx_hash),
                intval($exclude_order_id)
            ),
            ARRAY_A
        );

        if ($payment) {
            $payment['metadata'] = json_decode($payment['metadata'], true);
        }

        return $payment;
    }

    /**
     * Get payments by status
     * 
     * @param string $status Payment status
     * @param int $limit Number of payments to retrieve
     * @return array Array of payment data
     */
    public static function get_payments_by_status($status, $limit = 50) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'stablecoinpay_payments';

        $payments = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE status = %s ORDER BY created_at DESC LIMIT %d",
                sanitize_text_field($status),
                intval($limit)
            ),
            ARRAY_A
        );

        foreach ($payments as &$payment) {
            $payment['metadata'] = json_decode($payment['metadata'], true);
        }

        return $payments;
    }

    /**
     * Get recent payments
     * 
     * @param int $limit Number of payments to retrieve
     * @param int $days Number of days to look back
     * @return array Array of payment data
     */
    public static function get_recent_payments($limit = 20, $days = 30) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'stablecoinpay_payments';

        $payments = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE created_at >= DATE_SUB(NOW(), INTERVAL %d DAY) ORDER BY created_at DESC LIMIT %d",
                intval($days),
                intval($limit)
            ),
            ARRAY_A
        );

        foreach ($payments as &$payment) {
            $payment['metadata'] = json_decode($payment['metadata'], true);
        }

        return $payments;
    }

    /**
     * Log transaction event
     * 
     * @param string $payment_id Payment ID
     * @param string $event_type Event type
     * @param array $event_data Event data
     * @return bool True on success, false on failure
     */
    public static function log_transaction_event($payment_id, $event_type, $event_data = array()) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'stablecoinpay_transaction_logs';

        $data = array(
            'payment_id' => sanitize_text_field($payment_id),
            'event_type' => sanitize_text_field($event_type),
            'event_data' => wp_json_encode($event_data),
            'ip_address' => self::get_client_ip(),
            'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? ''),
            'created_at' => current_time('mysql')
        );

        $result = $wpdb->insert($table_name, $data);

        return $result !== false;
    }

    /**
     * Get transaction logs for payment
     * 
     * @param string $payment_id Payment ID
     * @return array Array of transaction logs
     */
    public static function get_transaction_logs($payment_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'stablecoinpay_transaction_logs';

        $logs = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE payment_id = %s ORDER BY created_at ASC",
                sanitize_text_field($payment_id)
            ),
            ARRAY_A
        );

        foreach ($logs as &$log) {
            $log['event_data'] = json_decode($log['event_data'], true);
        }

        return $logs;
    }

    /**
     * Delete old payment data
     * 
     * @param int $days Number of days to keep data
     * @return int Number of deleted records
     */
    public static function cleanup_old_payments($days = 90) {
        global $wpdb;

        $payments_table = $wpdb->prefix . 'stablecoinpay_payments';
        $logs_table = $wpdb->prefix . 'stablecoinpay_transaction_logs';

        // Delete old logs first (foreign key constraint)
        $logs_deleted = $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM $logs_table WHERE created_at < DATE_SUB(NOW(), INTERVAL %d DAY)",
                intval($days)
            )
        );

        // Delete old payments
        $payments_deleted = $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM $payments_table WHERE created_at < DATE_SUB(NOW(), INTERVAL %d DAY) AND status IN ('EXPIRED', 'CANCELED')",
                intval($days)
            )
        );

        return $payments_deleted + $logs_deleted;
    }

    /**
     * Get client IP address
     * 
     * @return string Client IP address
     */
    private static function get_client_ip() {
        $ip_keys = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = sanitize_text_field($_SERVER[$key]);
                // Handle comma-separated IPs (X-Forwarded-For)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return sanitize_text_field($_SERVER['REMOTE_ADDR'] ?? '');
    }
}
