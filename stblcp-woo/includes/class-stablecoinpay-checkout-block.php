<?php
/**
 * StablecoinPay Checkout Block Support
 *
 * Provides support for WooCommerce Checkout Block
 *
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

use Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType;

final class StablecoinPay_Checkout_Block extends AbstractPaymentMethodType {

    /**
     * Payment method name/id
     */
    protected $name = 'stablecoinpay';

    /**
     * Initializes the payment method type.
     */
    public function initialize() {
        $this->settings = get_option('woocommerce_stablecoinpay_settings', array());
    }

    /**
     * Returns if this payment method should be active. If false, the scripts will not be enqueued.
     */
    public function is_active() {
        $payment_gateways_class = WC()->payment_gateways();
        $payment_gateways       = $payment_gateways_class->payment_gateways();

        return isset($payment_gateways['stablecoinpay']) && $payment_gateways['stablecoinpay']->is_available();
    }

    /**
     * Returns an array of scripts/handles to be registered for this payment method.
     */
    public function get_payment_method_script_handles() {
        $script_path       = '/assets/js/checkout-block.js';
        $script_asset_path = STABLECOINPAY_PLUGIN_PATH . 'assets/js/checkout-block.asset.php';
        $script_asset      = file_exists($script_asset_path)
            ? require($script_asset_path)
            : array(
                'dependencies' => array('wc-blocks-registry', 'wp-element', 'wp-html-entities', 'wp-i18n'),
                'version'      => STABLECOINPAY_VERSION,
            );

        wp_register_script(
            'stablecoinpay-checkout-block-unified',
            STABLECOINPAY_PLUGIN_URL . $script_path,
            $script_asset['dependencies'],
            $script_asset['version'],
            true
        );

        return array('stablecoinpay-checkout-block-unified');
    }

    /**
     * Returns an array of key=>value pairs of data made available to the payment methods script.
     */
    public function get_payment_method_data() {
        $payment_gateways_class = WC()->payment_gateways();
        $payment_gateways       = $payment_gateways_class->payment_gateways();
        $gateway                = isset($payment_gateways['stablecoinpay']) ? $payment_gateways['stablecoinpay'] : null;

        if (!$gateway) {
            return array();
        }

        // Get the icon URL instead of HTML
        $icon_url = $gateway->icon; // Direct access to the icon property which is a URL

        return array(
            'title'       => $gateway->get_title(),
            'description' => $gateway->get_description() ?: 'Pay securely with cryptocurrency',
            'supports'    => array_filter($gateway->supports, array($gateway, 'supports')),
            'icon'        => $icon_url,
            'testMode'    => StablecoinPay_Settings::get_setting('test_mode', false),
            'enabledMethods' => StablecoinPay_Settings::get_enabled_payment_methods(),
            'pluginUrl'   => STABLECOINPAY_PLUGIN_URL,
            'defaultToken' => StablecoinPay_Settings::get_setting('default_token', 'USDT'),
            'defaultBlockchain' => StablecoinPay_Settings::get_setting('default_blockchain', 'ETHEREUM'),
            'supportedTokens' => array(
                'USDT' => __('Tether USD', 'stablecoinpay-gateway'),
                'USDC' => __('USD Coin', 'stablecoinpay-gateway'),
                'DAI'  => __('Dai Stablecoin', 'stablecoinpay-gateway'),
                'PYUSD' => __('PayPal USD', 'stablecoinpay-gateway'),
                'BUSD' => __('Binance USD', 'stablecoinpay-gateway'),
                'ETH'  => __('Ethereum', 'stablecoinpay-gateway'),
                'BNB'  => __('Binance Coin', 'stablecoinpay-gateway'),
                'TRX'  => __('Tron', 'stablecoinpay-gateway'),
                'SOL'  => __('Solana', 'stablecoinpay-gateway'),
                'TON'  => __('Toncoin', 'stablecoinpay-gateway')
            ),
            'supportedBlockchains' => array(
                'ETHEREUM' => __('Ethereum', 'stablecoinpay-gateway'),
                'BSC'      => __('Binance Smart Chain', 'stablecoinpay-gateway'),
                'TRON'     => __('Tron', 'stablecoinpay-gateway'),
                'SOLANA'   => __('Solana', 'stablecoinpay-gateway'),
                'TON'      => __('TON', 'stablecoinpay-gateway')
            )
        );
    }
}
