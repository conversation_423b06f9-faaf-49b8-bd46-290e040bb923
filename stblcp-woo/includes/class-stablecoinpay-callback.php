<?php
/**
 * Callback Handler
 * 
 * Handles payment status callbacks from StablecoinPay
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_Callback {

    public function __construct() {
        add_action('woocommerce_api_stablecoinpay_callback', array($this, 'handle_callback'));
        add_action('init', array($this, 'add_rewrite_rules'));
    }

    /**
     * Add rewrite rules for callback URLs
     */
    public function add_rewrite_rules() {
        add_rewrite_rule(
            '^stablecoinpay-callback/([0-9]+)/?$',
            'index.php?wc-api=stablecoinpay_callback&order_id=$matches[1]',
            'top'
        );
    }

    /**
     * Handle payment callback
     */
    public function handle_callback() {
        $order_id = intval($_GET['order_id'] ?? 0);

        if (!$order_id) {
            $this->send_response(400, 'Missing order ID');
            return;
        }

        // Verify IP whitelist if configured
        if (!$this->verify_ip_whitelist()) {
            $this->log_error('Callback rejected: IP not whitelisted - ' . $this->get_client_ip());
            $this->send_response(403, 'IP not allowed');
            return;
        }

        // Get request body
        $body = file_get_contents('php://input');

        // Verify HMAC signature first
        if (!$this->verify_callback_signature($body)) {
            $this->log_error('HMAC signature verification failed for order: ' . $order_id);
            $this->send_response(401, 'Unauthorized');
            return;
        }

        $data = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->log_error('Invalid JSON in callback: ' . json_last_error_msg());
            $this->send_response(400, 'Invalid JSON');
            return;
        }

        // Validate callback data structure
        if (!$this->validate_callback_data($data)) {
            $this->log_error('Invalid callback data structure for order: ' . $order_id);
            $this->send_response(400, 'Invalid data');
            return;
        }

        $this->log_info('Callback received for order ' . $order_id . ': ' . substr($body, 0, 200) . '...');

        // Verify order exists
        $order = StablecoinPay_HPOS::get_order($order_id);
        if (!$order) {
            $this->log_error('Order not found: ' . $order_id);
            $this->send_response(404, 'Order not found');
            return;
        }

        // CRITICAL SECURITY FIX: Enhanced payment validation with order binding
        $callback_payment_id = sanitize_text_field($data['id'] ?? '');

        if (!$this->validate_payment_ownership($order, $callback_payment_id, $data)) {
            $this->log_error('Payment ownership validation failed for order ' . $order_id . ', payment: ' . $callback_payment_id);
            $this->send_response(400, 'Payment validation failed');
            return;
        }

        // Auto-detect webhook endpoint ID if not already stored
        $this->auto_detect_webhook_endpoint($data);

        // Process the callback
        $this->process_payment_update($order, $data);

        $this->send_response(200, 'OK');
    }

    /**
     * Process payment status update with locking to prevent race conditions
     *
     * @param WC_Order $order WooCommerce order
     * @param array $data Callback data
     */
    private function process_payment_update($order, $data) {
        $order_id = $order->get_id();
        $payment_id = sanitize_text_field($data['id']);
        $status = sanitize_text_field($data['status']);
        $tx_hash = sanitize_text_field($data['txHash'] ?? '');

        // CRITICAL RACE CONDITION FIX: Enhanced locking with retry mechanism
        $lock_key = 'stablecoinpay_callback_' . $payment_id;
        $max_retries = 3;
        $retry_delay = 1; // seconds
        $lock_acquired = false;

        for ($retry = 0; $retry < $max_retries; $retry++) {
            try {
                if ($this->acquire_processing_lock($lock_key, 30)) {
                    $lock_acquired = true;
                    break;
                }

                if ($retry < $max_retries - 1) {
                    $this->log_info("Lock acquisition failed, retrying in {$retry_delay} seconds (attempt " . ($retry + 1) . "/{$max_retries})");
                    sleep($retry_delay);
                    $retry_delay *= 2; // Exponential backoff
                }

            } catch (Exception $e) {
                $this->log_error('Database error during lock acquisition: ' . $e->getMessage());

                // For database errors, don't retry - fail fast
                $this->send_response(500, 'Database error during payment processing');
                return;
            }
        }

        if (!$lock_acquired) {
            $this->log_error('Could not acquire processing lock for payment after ' . $max_retries . ' attempts: ' . $payment_id);

            // Check if this is a duplicate callback by examining payment status
            $current_status = StablecoinPay_HPOS::get_order_meta($order_id, '_stablecoinpay_status', true);
            if (in_array($current_status, ['CONFIRMED', 'EXPIRED', 'CANCELED'])) {
                $this->log_info('Payment already in final state, treating as successful: ' . $current_status);
                $this->send_response(200, 'Payment already processed');
                return;
            }

            // If not in final state, this is a genuine lock contention issue
            $this->send_response(409, 'Payment processing conflict - please retry');
            return;
        }

        try {
            // Check if payment is already in final state
            $current_status = StablecoinPay_HPOS::get_order_meta($order_id, '_stablecoinpay_status', true);
            if (in_array($current_status, ['CONFIRMED', 'EXPIRED', 'CANCELED'])) {
                $this->log_info('Payment already in final state: ' . $current_status . ' for order: ' . $order_id);
                return; // Not an error, just already processed
            }

            // Execute all payment processing operations within a database transaction
            $transaction_result = StablecoinPay_Database::execute_in_transaction(
                array($this, 'process_payment_callback_transaction'),
                array($order, $payment_id, $status, $tx_hash, $data)
            );

            if (!$transaction_result) {
                $this->log_error('Payment callback transaction failed for order: ' . $order_id);
                $this->send_response(500, 'Payment processing failed');
                return;
            }

            $this->log_info('Payment callback processed successfully for order: ' . $order_id);

        } finally {
            // Always release the lock
            $this->release_processing_lock($lock_key);
        }
    }

    /**
     * CRITICAL RACE CONDITION FIX: Enhanced processing lock with proper error handling
     *
     * @param string $lock_key Unique lock key
     * @param int $timeout Lock timeout in seconds
     * @return bool True if lock acquired, false otherwise
     * @throws Exception If database error occurs during locking
     */
    private function acquire_processing_lock($lock_key, $timeout = 30) {
        global $wpdb;

        $lock_name = 'stablecoinpay_' . md5($lock_key);

        // CRITICAL FIX: Proper GET_LOCK return value handling
        $result = $wpdb->get_var($wpdb->prepare("SELECT GET_LOCK(%s, %d)", $lock_name, $timeout));

        // Handle all possible return values from GET_LOCK
        if ($result === null) {
            // NULL = Database error occurred
            $db_error = $wpdb->last_error;
            $this->log_error("Database error during lock acquisition: " . $db_error . " for lock: " . $lock_name);

            // Log critical database error
            StablecoinPay_Security::log_security_event('Database lock error', array(
                'lock_name' => $lock_name,
                'lock_key' => $lock_key,
                'db_error' => $db_error,
                'ip_address' => $this->get_client_ip()
            ));

            throw new Exception('Database error during lock acquisition: ' . $db_error);
        }

        if ($result == 1) {
            // Lock acquired successfully
            $this->log_info("Lock acquired successfully: " . $lock_name);
            return true;
        }

        if ($result == 0) {
            // Lock timeout - another process is holding the lock
            $this->log_error("Lock timeout after {$timeout} seconds for: " . $lock_name);

            // Check if lock is held by a dead connection
            $lock_holder = $wpdb->get_var($wpdb->prepare("SELECT IS_USED_LOCK(%s)", $lock_name));
            if ($lock_holder !== null) {
                $this->log_error("Lock is held by connection ID: " . $lock_holder);

                // Log potential deadlock situation
                StablecoinPay_Security::log_security_event('Payment processing deadlock detected', array(
                    'lock_name' => $lock_name,
                    'lock_key' => $lock_key,
                    'holder_connection_id' => $lock_holder,
                    'timeout' => $timeout,
                    'ip_address' => $this->get_client_ip()
                ));
            }

            return false;
        }

        // Unexpected return value
        $this->log_error("Unexpected GET_LOCK return value: " . var_export($result, true) . " for lock: " . $lock_name);
        return false;
    }

    /**
     * CRITICAL RACE CONDITION FIX: Enhanced lock release with proper error handling
     *
     * @param string $lock_key Unique lock key
     * @return bool True if lock released, false otherwise
     */
    private function release_processing_lock($lock_key) {
        global $wpdb;

        $lock_name = 'stablecoinpay_' . md5($lock_key);

        // CRITICAL FIX: Proper RELEASE_LOCK return value handling
        $result = $wpdb->get_var($wpdb->prepare("SELECT RELEASE_LOCK(%s)", $lock_name));

        // Handle all possible return values from RELEASE_LOCK
        if ($result === null) {
            // NULL = Lock was not established by this thread or database error
            $this->log_error("Lock was not held by this connection or database error: " . $lock_name);
            return false;
        }

        if ($result == 1) {
            // Lock released successfully
            $this->log_info("Lock released successfully: " . $lock_name);
            return true;
        }

        if ($result == 0) {
            // Lock was not established by this thread
            $this->log_error("Attempted to release lock not held by this connection: " . $lock_name);
            return false;
        }

        // Unexpected return value
        $this->log_error("Unexpected RELEASE_LOCK return value: " . var_export($result, true) . " for lock: " . $lock_name);
        return false;
    }

    /**
     * Process payment callback within a database transaction
     *
     * @param WC_Order $order WooCommerce order
     * @param string $payment_id Payment ID
     * @param string $status Payment status
     * @param string $tx_hash Transaction hash
     * @param array $data Callback data
     * @return bool True on success, false on failure
     */
    public function process_payment_callback_transaction($order, $payment_id, $status, $tx_hash, $data) {
        $order_id = $order->get_id();

        try {
            // Log transaction event
            if (class_exists('StablecoinPay_Payment_Manager')) {
                StablecoinPay_Payment_Manager::log_transaction_event($payment_id, 'callback_received', $data);

                // Get current payment data to track changes
                $current_payment = StablecoinPay_Payment_Manager::get_payment($payment_id);

                // Update payment data in custom table
                $update_data = array(
                    'status' => $status,
                    'tx_hash' => $tx_hash,
                    'old_status' => $current_payment['status'] ?? 'unknown'
                );

                if (isset($data['confirmedAmount'])) {
                    $update_data['confirmed_amount'] = floatval($data['confirmedAmount']);
                }

                if (isset($data['confirmations'])) {
                    $update_data['confirmations'] = intval($data['confirmations']);
                }

                // Update payment without internal transaction (we're already in one)
                if (!StablecoinPay_Payment_Manager::update_payment_no_transaction($payment_id, $update_data)) {
                    throw new Exception('Failed to update payment data');
                }
            }

            // Update order meta
            StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_status', $status);
            StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_last_update', current_time('mysql'));

            if (!empty($tx_hash)) {
                StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_tx_hash', $tx_hash);
            }

            // Process based on status
            switch ($status) {
                case 'DETECTED':
                    $this->handle_payment_detected($order, $data);
                    break;

                case 'CONFIRMED':
                    $this->handle_payment_confirmed($order, $data);
                    break;

                case 'EXPIRED':
                    $this->handle_payment_expired($order, $data);
                    break;

                case 'CANCELED':
                    $this->handle_payment_canceled($order, $data);
                    break;

                default:
                    $this->log_info('Unknown payment status: ' . $status . ' for order ' . $order_id);
                    break;
            }

            return true;

        } catch (Exception $e) {
            $this->log_error('Payment callback transaction failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle payment detected
     *
     * @param WC_Order $order WooCommerce order
     * @param array $data Callback data
     */
    private function handle_payment_detected($order, $data) {
        $order_id = $order->get_id();
        $tx_hash = sanitize_text_field($data['txHash'] ?? '');

        StablecoinPay_HPOS::add_order_note($order_id, sprintf(
            __('Payment detected on blockchain. Transaction hash: %s', 'stablecoinpay-gateway'),
            $tx_hash
        ));

        $this->log_info('Payment detected for order ' . $order_id . ', tx: ' . $tx_hash);
    }

    /**
     * Handle payment confirmed
     *
     * @param WC_Order $order WooCommerce order
     * @param array $data Callback data
     */
    private function handle_payment_confirmed($order, $data) {
        $order_id = $order->get_id();
        $tx_hash = sanitize_text_field($data['txHash'] ?? '');
        $amount = floatval($data['amount'] ?? 0);

        // Only process if order is not already completed
        if (!$order->is_paid()) {
            // Mark order as paid - this handles WooCommerce's internal transaction logic
            $order->payment_complete($tx_hash);

            StablecoinPay_HPOS::add_order_note($order_id, sprintf(
                __('Payment confirmed! Amount: %s, Transaction hash: %s', 'stablecoinpay-gateway'),
                $amount,
                $tx_hash
            ));

            $this->log_info('Payment confirmed for order ' . $order_id . ', amount: ' . $amount . ', tx: ' . $tx_hash);

            // Send confirmation email
            do_action('stablecoinpay_payment_confirmed', $order, $data);
        }
    }

    /**
     * Handle payment expired
     * 
     * @param WC_Order $order WooCommerce order
     * @param array $data Callback data
     */
    private function handle_payment_expired($order, $data) {
        $order_id = $order->get_id();

        if ($order->get_status() === 'pending') {
            StablecoinPay_HPOS::update_order_status($order_id, 'cancelled', __('Payment expired', 'stablecoinpay-gateway'));

            StablecoinPay_HPOS::add_order_note($order_id, __('Payment expired - order cancelled', 'stablecoinpay-gateway'));

            $this->log_info('Payment expired for order ' . $order_id);
        }
    }

    /**
     * Handle payment canceled
     * 
     * @param WC_Order $order WooCommerce order
     * @param array $data Callback data
     */
    private function handle_payment_canceled($order, $data) {
        $order_id = $order->get_id();

        if ($order->get_status() === 'pending') {
            StablecoinPay_HPOS::update_order_status($order_id, 'cancelled', __('Payment cancelled', 'stablecoinpay-gateway'));

            StablecoinPay_HPOS::add_order_note($order_id, __('Payment cancelled by user', 'stablecoinpay-gateway'));

            $this->log_info('Payment cancelled for order ' . $order_id);
        }
    }

    /**
     * Verify IP whitelist
     * 
     * @return bool True if IP is allowed, false otherwise
     */
    private function verify_ip_whitelist() {
        // Check new webhook IP whitelist setting first, fallback to legacy callback setting
        $whitelist = StablecoinPay_Settings::get_setting('webhook_ip_whitelist', '') ?:
                    StablecoinPay_Settings::get_setting('callback_ip_whitelist', '');

        if (empty($whitelist)) {
            return true; // No whitelist configured, allow all
        }

        $client_ip = $this->get_client_ip();
        $allowed_ips = array_map('trim', explode(',', $whitelist));

        foreach ($allowed_ips as $allowed_ip) {
            if (empty($allowed_ip)) {
                continue;
            }

            // Check for CIDR notation
            if (strpos($allowed_ip, '/') !== false) {
                if ($this->ip_in_range($client_ip, $allowed_ip)) {
                    return true;
                }
            } else {
                if ($client_ip === $allowed_ip) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check if IP is in CIDR range
     * 
     * @param string $ip IP address to check
     * @param string $range CIDR range
     * @return bool True if IP is in range, false otherwise
     */
    private function ip_in_range($ip, $range) {
        list($subnet, $bits) = explode('/', $range);
        
        if ($bits === null) {
            $bits = 32;
        }

        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;

        return ($ip & $mask) == $subnet;
    }

    /**
     * Get client IP address
     * 
     * @return string Client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = sanitize_text_field($_SERVER[$key]);
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }

        return sanitize_text_field($_SERVER['REMOTE_ADDR'] ?? '');
    }

    /**
     * Send HTTP response
     * 
     * @param int $code HTTP status code
     * @param string $message Response message
     */
    private function send_response($code, $message) {
        status_header($code);
        echo esc_html($message);
        exit;
    }

    /**
     * Log info message
     * 
     * @param string $message Log message
     */
    private function log_info($message) {
        if (StablecoinPay_Settings::get_setting('debug_mode')) {
            $logger = wc_get_logger();
            $logger->info($message, array('source' => 'stablecoinpay-callback'));
        }
    }

    /**
     * Verify HMAC signature for callback
     *
     * @param string $payload Raw POST data
     * @return bool True if signature is valid, false otherwise
     */
    private function verify_callback_signature($payload) {
        if (empty($payload)) {
            $this->log_error('Empty callback payload');
            return false;
        }

        // Get signature from headers (try multiple header names)
        $signature = $_SERVER['HTTP_X_STABLECOINPAY_SIGNATURE'] ??
                    $_SERVER['HTTP_X_SIGNATURE'] ??
                    $_SERVER['HTTP_SIGNATURE'] ?? '';

        if (empty($signature)) {
            $this->log_error('Missing signature header');
            return false;
        }

        // Remove 'sha256=' prefix if present
        $signature = str_replace(['sha256=', 'SHA256='], '', $signature);

        // Get webhook secret from settings (preferred) or fall back to API secret
        $webhook_secret = StablecoinPay_Settings::get_setting('webhook_secret');

        if (empty($webhook_secret)) {
            // Fallback to API secret for backward compatibility
            $webhook_secret = StablecoinPay_Settings::get_setting('api_secret');

            if (empty($webhook_secret)) {
                $this->log_error('Neither webhook secret nor API secret configured');
                return false;
            }

            $this->log_info('Using API secret as fallback for webhook verification (consider configuring webhook secret)');
        }

        // Calculate expected signature
        $expected_signature = hash_hmac('sha256', $payload, $webhook_secret);

        // Use hash_equals for timing-safe comparison
        if (!hash_equals($expected_signature, $signature)) {
            $this->log_error('HMAC signature mismatch');
            StablecoinPay_Security::log_security_event('Invalid callback signature', array(
                'provided_signature' => substr($signature, 0, 10) . '...',
                'expected_signature' => substr($expected_signature, 0, 10) . '...',
                'payload_length' => strlen($payload),
                'ip_address' => $this->get_client_ip()
            ));
            return false;
        }

        return true;
    }

    /**
     * Validate callback data structure
     *
     * @param array $data Callback data
     * @return bool True if valid, false otherwise
     */
    private function validate_callback_data($data) {
        // Check required fields
        $required_fields = array('id', 'status');

        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                $this->log_error('Missing required field: ' . $field);
                return false;
            }
        }

        // Validate status
        $valid_statuses = array('DETECTED', 'CONFIRMED', 'EXPIRED', 'CANCELED');
        if (!in_array($data['status'], $valid_statuses)) {
            $this->log_error('Invalid status: ' . $data['status']);
            return false;
        }

        // Validate payment ID format
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $data['id'])) {
            $this->log_error('Invalid payment ID format: ' . $data['id']);
            return false;
        }

        // Validate transaction hash if present
        if (!empty($data['txHash'])) {
            // Allow different hash formats for different blockchains
            $valid_hash_patterns = [
                '/^0x[a-fA-F0-9]{64}$/',  // Ethereum-style
                '/^[a-fA-F0-9]{64}$/',    // Bitcoin-style
                '/^[a-zA-Z0-9]{40,90}$/'  // Other blockchain formats
            ];

            $valid_hash = false;
            foreach ($valid_hash_patterns as $pattern) {
                if (preg_match($pattern, $data['txHash'])) {
                    $valid_hash = true;
                    break;
                }
            }

            if (!$valid_hash) {
                $this->log_error('Invalid transaction hash format: ' . $data['txHash']);
                return false;
            }
        }

        // Validate amount if present
        if (isset($data['amount']) && (!is_numeric($data['amount']) || floatval($data['amount']) < 0)) {
            $this->log_error('Invalid amount: ' . $data['amount']);
            return false;
        }

        // Validate confirmations if present
        if (isset($data['confirmations']) && (!is_numeric($data['confirmations']) || intval($data['confirmations']) < 0)) {
            $this->log_error('Invalid confirmations: ' . $data['confirmations']);
            return false;
        }

        return true;
    }

    /**
     * CRITICAL SECURITY FIX: Validate payment ownership and binding to order
     *
     * @param WC_Order $order WooCommerce order
     * @param string $callback_payment_id Payment ID from callback
     * @param array $callback_data Full callback data
     * @return bool True if payment is valid for this order, false otherwise
     */
    private function validate_payment_ownership($order, $callback_payment_id, $callback_data) {
        $order_id = $order->get_id();

        // 1. PAYMENT ID VALIDATION: Check if payment ID belongs to this order
        $stored_payment_id = StablecoinPay_HPOS::get_order_meta($order_id, '_stablecoinpay_payment_id');
        $current_payment_id = StablecoinPay_HPOS::get_order_meta($order_id, '_stablecoinpay_current_payment_id');

        $payment_id_matches = ($stored_payment_id === $callback_payment_id) ||
                             ($current_payment_id === $callback_payment_id);

        if (!$payment_id_matches) {
            $this->log_error("Payment ID validation failed - Order: $order_id, Stored: $stored_payment_id, Current: $current_payment_id, Callback: $callback_payment_id");
            return false;
        }

        // 2. AMOUNT VALIDATION: Verify payment amount matches order total
        if (isset($callback_data['amount'])) {
            $callback_amount = floatval($callback_data['amount']);
            $order_total = floatval($order->get_total());

            // Allow small floating point differences (0.01)
            $amount_difference = abs($callback_amount - $order_total);
            if ($amount_difference > 0.01) {
                $this->log_error("Amount validation failed - Order: $order_id, Order Total: $order_total, Callback Amount: $callback_amount, Difference: $amount_difference");
                StablecoinPay_Security::log_security_event('Payment amount mismatch', array(
                    'order_id' => $order_id,
                    'payment_id' => $callback_payment_id,
                    'order_total' => $order_total,
                    'callback_amount' => $callback_amount,
                    'ip_address' => $this->get_client_ip()
                ));
                return false;
            }
        }

        // 3. TOKEN/BLOCKCHAIN VALIDATION: Verify payment method matches order selection
        $selected_method = StablecoinPay_HPOS::get_order_meta($order_id, '_stablecoinpay_selected_method');
        if (!empty($selected_method) && isset($callback_data['token']) && isset($callback_data['blockchain'])) {
            $enabled_methods = StablecoinPay_Settings::get_enabled_payment_methods();

            if (isset($enabled_methods[$selected_method])) {
                $expected_token = $enabled_methods[$selected_method]['token'];
                $expected_blockchain = $enabled_methods[$selected_method]['blockchain'];
                $callback_token = sanitize_text_field($callback_data['token']);
                $callback_blockchain = sanitize_text_field($callback_data['blockchain']);

                if ($expected_token !== $callback_token || $expected_blockchain !== $callback_blockchain) {
                    $this->log_error("Token/Blockchain validation failed - Order: $order_id, Expected: $expected_token/$expected_blockchain, Callback: $callback_token/$callback_blockchain");
                    StablecoinPay_Security::log_security_event('Payment method mismatch', array(
                        'order_id' => $order_id,
                        'payment_id' => $callback_payment_id,
                        'expected_token' => $expected_token,
                        'expected_blockchain' => $expected_blockchain,
                        'callback_token' => $callback_token,
                        'callback_blockchain' => $callback_blockchain,
                        'ip_address' => $this->get_client_ip()
                    ));
                    return false;
                }
            }
        }

        // 4. PAYMENT STATUS TRANSITION VALIDATION: Ensure valid status transitions
        $current_status = StablecoinPay_HPOS::get_order_meta($order_id, '_stablecoinpay_status');
        $new_status = sanitize_text_field($callback_data['status']);

        if (!$this->is_valid_status_transition($current_status, $new_status)) {
            $this->log_error("Invalid status transition - Order: $order_id, Current: $current_status, New: $new_status");
            StablecoinPay_Security::log_security_event('Invalid payment status transition', array(
                'order_id' => $order_id,
                'payment_id' => $callback_payment_id,
                'current_status' => $current_status,
                'new_status' => $new_status,
                'ip_address' => $this->get_client_ip()
            ));
            return false;
        }

        // 5. DUPLICATE TRANSACTION PROTECTION: Check for transaction hash reuse
        if (!empty($callback_data['txHash']) && $new_status === 'CONFIRMED') {
            $tx_hash = sanitize_text_field($callback_data['txHash']);
            if ($this->is_transaction_hash_used($tx_hash, $order_id)) {
                $this->log_error("Transaction hash already used - Order: $order_id, TxHash: $tx_hash");
                StablecoinPay_Security::log_security_event('Duplicate transaction hash', array(
                    'order_id' => $order_id,
                    'payment_id' => $callback_payment_id,
                    'tx_hash' => $tx_hash,
                    'ip_address' => $this->get_client_ip()
                ));
                return false;
            }
        }

        return true;
    }

    /**
     * Validate payment status transitions
     *
     * @param string $current_status Current payment status
     * @param string $new_status New payment status from callback
     * @return bool True if transition is valid, false otherwise
     */
    private function is_valid_status_transition($current_status, $new_status) {
        // Define valid status transitions
        $valid_transitions = array(
            '' => array('WAITING', 'DETECTED', 'CONFIRMED', 'EXPIRED', 'CANCELED'), // Initial state
            'WAITING' => array('DETECTED', 'CONFIRMED', 'EXPIRED', 'CANCELED'),
            'DETECTED' => array('CONFIRMED', 'EXPIRED', 'CANCELED'),
            'CONFIRMED' => array(), // Final state - no transitions allowed
            'EXPIRED' => array(), // Final state - no transitions allowed
            'CANCELED' => array() // Final state - no transitions allowed
        );

        $current_status = $current_status ?: '';

        return isset($valid_transitions[$current_status]) &&
               in_array($new_status, $valid_transitions[$current_status]);
    }

    /**
     * Check if transaction hash has been used by another order
     *
     * @param string $tx_hash Transaction hash
     * @param int $current_order_id Current order ID (to exclude from check)
     * @return bool True if transaction hash is already used, false otherwise
     */
    private function is_transaction_hash_used($tx_hash, $current_order_id) {
        global $wpdb;

        // Check in order meta for existing transaction hashes
        $existing_order = $wpdb->get_var($wpdb->prepare("
            SELECT post_id
            FROM {$wpdb->postmeta}
            WHERE meta_key = '_stablecoinpay_tx_hash'
            AND meta_value = %s
            AND post_id != %d
            LIMIT 1
        ", $tx_hash, $current_order_id));

        if ($existing_order) {
            $this->log_error("Transaction hash $tx_hash already used by order $existing_order");
            return true;
        }

        // Also check in custom payment table if available
        if (class_exists('StablecoinPay_Payment_Manager')) {
            $existing_payment = StablecoinPay_Payment_Manager::get_payment_by_tx_hash($tx_hash, $current_order_id);
            if ($existing_payment) {
                $this->log_error("Transaction hash $tx_hash already used by payment " . $existing_payment['id']);
                return true;
            }
        }

        return false;
    }

    /**
     * Auto-detect webhook endpoint ID from incoming webhook data
     * This allows the system to work with manually configured webhooks
     *
     * @param array $data Callback data
     */
    private function auto_detect_webhook_endpoint($data) {
        // Check if we already have a webhook endpoint ID stored
        $stored_endpoint_id = get_option('stablecoinpay_webhook_endpoint_id');

        if ($stored_endpoint_id) {
            return; // Already configured
        }

        // Try to detect webhook endpoint ID from the webhook data
        // This could be from headers or webhook metadata if available
        $webhook_endpoint_id = null;

        // Check for webhook endpoint ID in headers
        $webhook_id_header = $_SERVER['HTTP_X_WEBHOOK_ENDPOINT_ID'] ??
                            $_SERVER['HTTP_X_STABLECOINPAY_ENDPOINT_ID'] ??
                            null;

        if ($webhook_id_header) {
            $webhook_endpoint_id = intval($webhook_id_header);
        }

        // If we found a webhook endpoint ID, store it
        if ($webhook_endpoint_id && $webhook_endpoint_id > 0) {
            update_option('stablecoinpay_webhook_endpoint_id', $webhook_endpoint_id);
            $this->log_info('Auto-detected webhook endpoint ID: ' . $webhook_endpoint_id);
        } else {
            // Log that webhook is working but endpoint ID couldn't be detected
            $this->log_info('Webhook received but endpoint ID could not be auto-detected - manual configuration is working');
        }
    }

    /**
     * Log error message
     *
     * @param string $message Log message
     */
    private function log_error($message) {
        $logger = wc_get_logger();
        $logger->error($message, array('source' => 'stablecoinpay-callback'));
    }
}
