<?php
/**
 * Admin Interface
 * 
 * Handles admin functionality and AJAX requests
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_Admin {

    public function __construct() {
        add_action('wp_ajax_stablecoinpay_test_connection', array($this, 'ajax_test_connection'));
        add_action('wp_ajax_stablecoinpay_test_callback_url', array($this, 'ajax_test_callback_url'));
        add_action('wp_ajax_stablecoinpay_dismiss_cloudflare_notice', array($this, 'ajax_dismiss_cloudflare_notice'));
        add_action('wp_ajax_stablecoinpay_get_cloudflare_ips', array($this, 'ajax_get_cloudflare_ips'));
        // Webhook endpoint AJAX handlers removed - using manual configuration approach
        add_action('wp_ajax_stablecoinpay_get_payment_status', array('StablecoinPay_Payment_Page', 'ajax_get_payment_status'));
        add_action('wp_ajax_nopriv_stablecoinpay_get_payment_status', array('StablecoinPay_Payment_Page', 'ajax_get_payment_status'));
        add_action('admin_notices', array($this, 'admin_notices'));
        add_filter('woocommerce_order_actions', array($this, 'add_order_actions'));
        add_action('woocommerce_order_action_stablecoinpay_check_payment', array($this, 'check_payment_status'));
    }

    /**
     * Test API connection via AJAX
     */
    public function ajax_test_connection() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'stablecoinpay_admin')) {
            wp_send_json_error('Security check failed');
        }

        // Check user permissions
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            // Get current settings to test with
            $api_key = StablecoinPay_Settings::get_setting('api_key');
            $api_secret = StablecoinPay_Settings::get_setting('api_secret');
            $api_url = StablecoinPay_Settings::get_setting('api_url');

            // Validate that we have the required settings
            if (empty($api_key)) {
                wp_send_json_error(__('API Key is not configured. Please enter your API key in the settings.', 'stablecoinpay-gateway'));
                return;
            }

            if (empty($api_secret)) {
                wp_send_json_error(__('API Secret is not configured. Please enter your API secret in the settings.', 'stablecoinpay-gateway'));
                return;
            }

            if (empty($api_url)) {
                wp_send_json_error(__('API URL is not configured. Please enter your API URL in the settings.', 'stablecoinpay-gateway'));
                return;
            }

            // Test the connection
            $api = new StablecoinPay_API();
            $config = $api->get_config();

            $connection_result = $api->test_connection();

            if ($connection_result) {
                $message = __('Connection successful!', 'stablecoinpay-gateway');

                // Add additional info for debugging
                $debug_info = array();
                if (StablecoinPay_Settings::get_setting('debug_mode')) {
                    $debug_info = array(
                        'api_url' => esc_html($config['base_url']),
                        'test_mode' => $config['testmode'] ? 'Yes' : 'No',
                        'has_credentials' => $config['has_credentials'] ? 'Yes' : 'No',
                        'timestamp' => current_time('mysql')
                    );

                    $message .= sprintf(
                        ' (URL: %s, Test Mode: %s)',
                        $debug_info['api_url'],
                        $debug_info['test_mode']
                    );
                }

                wp_send_json_success(array(
                    'message' => $message,
                    'config' => $config,
                    'debug_info' => $debug_info
                ));
            } else {
                $error_message = __('❌ Connection failed. Please verify your API credentials and URL are correct.', 'stablecoinpay-gateway');

                // Add debug information if debug mode is enabled
                if (StablecoinPay_Settings::get_setting('debug_mode')) {
                    $error_message .= sprintf(
                        ' [Debug: URL=%s, Has Credentials=%s]',
                        esc_html($config['base_url']),
                        $config['has_credentials'] ? 'Yes' : 'No'
                    );
                }

                wp_send_json_error($error_message);
            }
        } catch (Exception $e) {
            // Provide more user-friendly error messages
            $error_message = $e->getMessage();

            // Check for common error patterns and provide helpful guidance
            if (strpos($error_message, 'timeout') !== false) {
                $user_message = __('Connection timed out. Please check your API URL and network connection.', 'stablecoinpay-gateway');
            } elseif (strpos($error_message, '401') !== false || strpos($error_message, 'authentication') !== false) {
                $user_message = __('Authentication failed. Please check your API key and secret.', 'stablecoinpay-gateway');
            } elseif (strpos($error_message, '404') !== false) {
                $user_message = __('API endpoint not found. Please check your API URL.', 'stablecoinpay-gateway');
            } elseif (strpos($error_message, 'SSL') !== false || strpos($error_message, 'certificate') !== false) {
                $user_message = __('SSL certificate error. Please check your API URL or contact support.', 'stablecoinpay-gateway');
            } else {
                $user_message = sprintf(__('Connection error: %s', 'stablecoinpay-gateway'), $error_message);
            }

            wp_send_json_error($user_message);
        }
    }

    /**
     * Display admin notices
     */
    public function admin_notices() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            echo '<div class="notice notice-error"><p>';
            echo '<strong>' . __('StablecoinPay Gateway', 'stablecoinpay-gateway') . '</strong>: ';
            echo __('WooCommerce is required for this plugin to work.', 'stablecoinpay-gateway');
            echo '</p></div>';
            return;
        }

        // Only show API configuration notice on non-StablecoinPay admin pages
        // and only if the gateway is enabled but not configured
        $current_screen = get_current_screen();
        $is_stablecoinpay_page = $current_screen && (
            strpos($current_screen->id, 'stablecoinpay') !== false ||
            ($current_screen->id === 'woocommerce_page_wc-settings' && isset($_GET['section']) && $_GET['section'] === 'stablecoinpay')
        );

        if (!$is_stablecoinpay_page) {
            $api_key = StablecoinPay_Settings::get_setting('api_key');
            $api_secret = StablecoinPay_Settings::get_setting('api_secret');
            $gateway_enabled = StablecoinPay_Settings::get_setting('enabled');

            // Only show notice if gateway is enabled but not configured
            if ($gateway_enabled && (empty($api_key) || empty($api_secret))) {
                $settings_url = admin_url('admin.php?page=stablecoinpay-settings');
                echo '<div class="notice notice-warning is-dismissible"><p>';
                echo '<strong>' . __('StablecoinPay Gateway', 'stablecoinpay-gateway') . '</strong>: ';
                printf(
                    __('Gateway is enabled but API credentials are missing. <a href="%s">Configure settings</a>.', 'stablecoinpay-gateway'),
                    esc_url($settings_url)
                );
                echo '</p></div>';
            }
        }

        // Check database tables
        if (class_exists('StablecoinPay_Database') && !StablecoinPay_Database::tables_exist()) {
            echo '<div class="notice notice-error"><p>';
            echo '<strong>' . __('StablecoinPay Gateway', 'stablecoinpay-gateway') . '</strong>: ';
            echo __('Database tables are missing. Please deactivate and reactivate the plugin.', 'stablecoinpay-gateway');
            echo '</p></div>';
        }

        // Check for Cloudflare and show IP whitelist notice
        $this->show_cloudflare_ip_notice();

        // Check for Cloudflare and show IP whitelist notice
        $this->show_cloudflare_ip_notice();
    }

    /**
     * Show Cloudflare IP whitelist notice if Cloudflare is detected
     */
    private function show_cloudflare_ip_notice() {
        // Only show on StablecoinPay settings pages
        $current_screen = get_current_screen();
        $is_stablecoinpay_page = $current_screen && (
            strpos($current_screen->id, 'stablecoinpay') !== false ||
            ($current_screen->id === 'woocommerce_page_wc-settings' && isset($_GET['section']) && $_GET['section'] === 'stablecoinpay')
        );

        if (!$is_stablecoinpay_page) {
            return;
        }

        // Check if Cloudflare is detected
        if ($this->is_cloudflare_detected()) {
            // Check if notice was dismissed
            $dismissed = get_user_meta(get_current_user_id(), 'stablecoinpay_cloudflare_notice_dismissed', true);

            if (!$dismissed) {
                $cloudflare_ips = $this->get_cloudflare_ip_ranges();

                if (!empty($cloudflare_ips)) {
                    echo '<div class="notice notice-warning is-dismissible" data-notice="cloudflare-ip">';
                    echo '<p><strong>' . __('Cloudflare Detected - IP Whitelist Required', 'stablecoinpay-gateway') . '</strong></p>';
                    echo '<p>' . __('Your site appears to be using Cloudflare. If you\'re using IP whitelisting in your StablecoinPay backend, you need to whitelist Cloudflare\'s IP ranges instead of your server IP.', 'stablecoinpay-gateway') . '</p>';
                    echo '<p><strong>' . __('Add these IP ranges to your StablecoinPay API key whitelist:', 'stablecoinpay-gateway') . '</strong></p>';
                    echo '<textarea readonly style="width: 100%; height: 120px; font-family: monospace; font-size: 12px;">';
                    echo esc_textarea(implode("\n", $cloudflare_ips));
                    echo '</textarea>';
                    echo '<p><em>' . __('Copy the above IP ranges and paste them into your StablecoinPay backend API key IP whitelist settings.', 'stablecoinpay-gateway') . '</em></p>';
                    echo '<script>
                        jQuery(document).ready(function($) {
                            $(document).on("click", "[data-notice=\'cloudflare-ip\'] .notice-dismiss", function() {
                                $.post(ajaxurl, {
                                    action: "stablecoinpay_dismiss_cloudflare_notice",
                                    nonce: "' . wp_create_nonce('stablecoinpay_dismiss_notice') . '"
                                });
                            });
                        });
                    </script>';
                    echo '</div>';
                }
            }
        }
    }

    /**
     * Detect if Cloudflare is being used
     *
     * @return bool True if Cloudflare is detected
     */
    private function is_cloudflare_detected() {
        // Check for Cloudflare headers
        $cloudflare_headers = array(
            'HTTP_CF_RAY',
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CF_IPCOUNTRY',
            'HTTP_CF_VISITOR'
        );

        foreach ($cloudflare_headers as $header) {
            if (!empty($_SERVER[$header])) {
                return true;
            }
        }

        // Check frontend for Cloudflare headers
        $response = wp_remote_head(home_url(), array('timeout' => 5));
        if (!is_wp_error($response)) {
            $headers = wp_remote_retrieve_headers($response);
            if (isset($headers['cf-ray']) || (isset($headers['server']) && strpos($headers['server'], 'cloudflare') !== false)) {
                return true;
            }
        }

        return false;
    }



    /**
     * Get Cloudflare IP ranges from their official endpoint
     *
     * @return array Array of IP ranges
     */
    private function get_cloudflare_ip_ranges() {
        // Check cache first
        $cached_ips = get_transient('stablecoinpay_cloudflare_ips');
        if ($cached_ips !== false) {
            return $cached_ips;
        }

        $ip_ranges = array();

        // Fetch IPv4 ranges
        $response = wp_remote_get('https://www.cloudflare.com/ips-v4/', array(
            'timeout' => 10,
            'sslverify' => true
        ));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $body = wp_remote_retrieve_body($response);

            // Extract IP ranges from the response
            if (preg_match_all('/(\d+\.\d+\.\d+\.\d+\/\d+)/', $body, $matches)) {
                $ip_ranges = $matches[1];
            }
        }

        // Cache for 24 hours
        if (!empty($ip_ranges)) {
            set_transient('stablecoinpay_cloudflare_ips', $ip_ranges, 24 * HOUR_IN_SECONDS);
        }

        return $ip_ranges;
    }

    /**
     * Add custom order actions
     * 
     * @param array $actions Existing actions
     * @return array Modified actions
     */
    public function add_order_actions($actions) {
        global $theorder;

        if (!$theorder) {
            return $actions;
        }

        // Only add action for StablecoinPay orders
        if ($theorder->get_payment_method() === 'stablecoinpay') {
            $actions['stablecoinpay_check_payment'] = __('Check StablecoinPay Status', 'stablecoinpay-gateway');
        }

        return $actions;
    }

    /**
     * Check payment status action
     * 
     * @param WC_Order $order Order object
     */
    public function check_payment_status($order) {
        $order_id = $order->get_id();
        $payment_id = StablecoinPay_HPOS::get_order_meta($order_id, '_stablecoinpay_payment_id');

        if (empty($payment_id)) {
            $order->add_order_note(__('No StablecoinPay payment ID found.', 'stablecoinpay-gateway'));
            return;
        }

        try {
            $api = new StablecoinPay_API();
            $payment_response = $api->get_payment($payment_id);

            if ($payment_response && isset($payment_response['data'])) {
                $payment_data = $payment_response['data'];
                $status = $payment_data['status'];
                $tx_hash = $payment_data['txHash'] ?? '';

                // Update order meta
                StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_status', $status);
                StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_last_update', current_time('mysql'));

                if (!empty($tx_hash)) {
                    StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_tx_hash', $tx_hash);
                }

                // Add order note
                $note = sprintf(
                    __('Payment status checked: %s', 'stablecoinpay-gateway'),
                    $status
                );

                if (!empty($tx_hash)) {
                    $note .= sprintf(' - TX: %s', $tx_hash);
                }

                $order->add_order_note($note);

                // Update order status if needed
                if ($status === 'CONFIRMED' && !$order->is_paid()) {
                    $order->payment_complete($tx_hash);
                } elseif (in_array($status, array('EXPIRED', 'CANCELED')) && $order->get_status() === 'pending') {
                    $order->update_status('cancelled', __('Payment ' . strtolower($status), 'stablecoinpay-gateway'));
                }
            } else {
                $order->add_order_note(__('Failed to retrieve payment status from API.', 'stablecoinpay-gateway'));
            }
        } catch (Exception $e) {
            $order->add_order_note(sprintf(
                __('Error checking payment status: %s', 'stablecoinpay-gateway'),
                $e->getMessage()
            ));
        }
    }

    /**
     * Get payment statistics for dashboard
     *
     * @param int $days Number of days to analyze
     * @return array Payment statistics
     */
    public static function get_payment_stats($days = 30) {
        if (class_exists('StablecoinPay_Database')) {
            return StablecoinPay_Database::get_payment_stats($days);
        }
        return array();
    }

    /**
     * Get recent payments for admin display
     *
     * @param int $limit Number of payments to retrieve
     * @return array Recent payments
     */
    public static function get_recent_payments($limit = 10) {
        if (class_exists('StablecoinPay_Payment_Manager')) {
            return StablecoinPay_Payment_Manager::get_recent_payments($limit);
        }
        return array();
    }

    /**
     * Export payment data to CSV
     * 
     * @param int $days Number of days to export
     */
    public static function export_payment_data($days = 30) {
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Insufficient permissions', 'stablecoinpay-gateway'));
        }

        if (!class_exists('StablecoinPay_Payment_Manager')) {
            wp_die(__('Payment manager not available', 'stablecoinpay-gateway'));
        }

        $payments = StablecoinPay_Payment_Manager::get_recent_payments(-1, $days);

        $filename = 'stablecoinpay-payments-' . date('Y-m-d') . '.csv';

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        $output = fopen('php://output', 'w');

        // CSV headers
        fputcsv($output, array(
            'Order ID',
            'Payment ID',
            'Status',
            'Token',
            'Blockchain',
            'Amount',
            'Fiat Amount',
            'Currency',
            'TX Hash',
            'Created At'
        ));

        // CSV data
        foreach ($payments as $payment) {
            fputcsv($output, array(
                $payment['order_id'],
                $payment['payment_id'],
                $payment['status'],
                $payment['token'],
                $payment['blockchain'],
                $payment['target_amount'],
                $payment['fiat_amount'],
                $payment['fiat_currency'],
                $payment['tx_hash'],
                $payment['created_at']
            ));
        }

        fclose($output);
        exit;
    }

    /**
     * Add meta box to order edit page
     */
    public static function add_order_meta_box() {
        add_meta_box(
            'stablecoinpay-payment-details',
            __('StablecoinPay Payment Details', 'stablecoinpay-gateway'),
            array(self::class, 'render_order_meta_box'),
            'shop_order',
            'side',
            'default'
        );
    }

    /**
     * Render order meta box
     * 
     * @param WP_Post $post Order post object
     */
    public static function render_order_meta_box($post) {
        $order = wc_get_order($post->ID);

        if ($order->get_payment_method() !== 'stablecoinpay') {
            echo '<p>' . __('This order does not use StablecoinPay.', 'stablecoinpay-gateway') . '</p>';
            return;
        }

        $payment_id = StablecoinPay_HPOS::get_order_meta($order->get_id(), '_stablecoinpay_payment_id');
        $status = StablecoinPay_HPOS::get_order_meta($order->get_id(), '_stablecoinpay_status');
        $tx_hash = StablecoinPay_HPOS::get_order_meta($order->get_id(), '_stablecoinpay_tx_hash');
        $last_update = StablecoinPay_HPOS::get_order_meta($order->get_id(), '_stablecoinpay_last_update');

        echo '<table class="widefat">';
        echo '<tr><td><strong>' . __('Payment ID:', 'stablecoinpay-gateway') . '</strong></td><td>' . esc_html($payment_id) . '</td></tr>';
        echo '<tr><td><strong>' . __('Status:', 'stablecoinpay-gateway') . '</strong></td><td>' . esc_html($status) . '</td></tr>';
        
        if ($tx_hash) {
            echo '<tr><td><strong>' . __('TX Hash:', 'stablecoinpay-gateway') . '</strong></td><td><code>' . esc_html($tx_hash) . '</code></td></tr>';
        }
        
        if ($last_update) {
            echo '<tr><td><strong>' . __('Last Update:', 'stablecoinpay-gateway') . '</strong></td><td>' . esc_html($last_update) . '</td></tr>';
        }
        
        echo '</table>';

        if ($payment_id) {
            $payment_url = add_query_arg(array(
                'order_id' => $order->get_id(),
                'payment_id' => $payment_id,
                'key' => $order->get_order_key()
            ), home_url('/stablecoinpay-payment/'));

            echo '<p><a href="' . esc_url($payment_url) . '" target="_blank" class="button">' . 
                 __('View Payment Page', 'stablecoinpay-gateway') . '</a></p>';
        }
    }

    /**
     * Clean up old data
     */
    public static function cleanup_old_data() {
        $deleted = 0;

        // Clean up old payment data (90 days)
        if (class_exists('StablecoinPay_Payment_Manager')) {
            $deleted = StablecoinPay_Payment_Manager::cleanup_old_payments(90);
        }

        // Clean up old logs (30 days)
        if (class_exists('StablecoinPay_Database')) {
            StablecoinPay_Database::cleanup_old_logs(30);
        }

        return $deleted;
    }

    /**
     * Test callback URL accessibility via AJAX
     */
    public function ajax_test_callback_url() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'stablecoinpay_test_callback')) {
            wp_send_json_error('Security check failed');
        }

        // Check user permissions
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error('Insufficient permissions');
        }

        $url = sanitize_url($_POST['url'] ?? '');

        if (empty($url)) {
            wp_send_json_error(__('No URL provided for testing', 'stablecoinpay-gateway'));
            return;
        }

        // Validate URL format
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            wp_send_json_error(__('Invalid URL format', 'stablecoinpay-gateway'));
            return;
        }

        try {
            // Test the callback endpoint
            $test_result = $this->test_callback_url_accessibility($url);

            if ($test_result['accessible']) {
                $message = __('Callback URL is accessible!', 'stablecoinpay-gateway');

                // Add additional info for debugging
                if (StablecoinPay_Settings::get_setting('debug_mode')) {
                    $message .= sprintf(
                        ' (Status: %d, Response Time: %dms)',
                        $test_result['status_code'],
                        $test_result['response_time']
                    );
                }

                wp_send_json_success(array(
                    'message' => $message,
                    'details' => $test_result
                ));
            } else {
                $error_message = __('❌ Callback URL is not accessible.', 'stablecoinpay-gateway');

                if (!empty($test_result['error'])) {
                    $error_message .= ' ' . sprintf(__('Error: %s', 'stablecoinpay-gateway'), $test_result['error']);
                }

                wp_send_json_error($error_message);
            }
        } catch (Exception $e) {
            wp_send_json_error(sprintf(__('Test failed: %s', 'stablecoinpay-gateway'), $e->getMessage()));
        }
    }

    /**
     * Test callback URL accessibility
     *
     * @param string $base_url Base URL to test
     * @return array Test results
     */
    private function test_callback_url_accessibility($base_url) {
        $start_time = microtime(true);

        // Construct the full callback URL
        $callback_url = rtrim($base_url, '/') . '/?wc-api=stablecoinpay_callback&order_id=test';

        // Test with a HEAD request first (lighter)
        $response = wp_remote_head($callback_url, array(
            'timeout' => 10,
            'sslverify' => true,
            'user-agent' => 'StablecoinPay-Test/1.0'
        ));

        $end_time = microtime(true);
        $response_time = round(($end_time - $start_time) * 1000); // Convert to milliseconds

        $result = array(
            'accessible' => false,
            'status_code' => 0,
            'response_time' => $response_time,
            'error' => '',
            'url_tested' => $callback_url
        );

        if (is_wp_error($response)) {
            $result['error'] = $response->get_error_message();

            // Try with SSL verification disabled for local development
            if (strpos($base_url, 'localhost') !== false || strpos($base_url, '127.0.0.1') !== false) {
                $response_no_ssl = wp_remote_head($callback_url, array(
                    'timeout' => 10,
                    'sslverify' => false,
                    'user-agent' => 'StablecoinPay-Test/1.0'
                ));

                if (!is_wp_error($response_no_ssl)) {
                    $result['status_code'] = wp_remote_retrieve_response_code($response_no_ssl);
                    $result['accessible'] = ($result['status_code'] < 500); // Accept even 404 as "accessible"
                    $result['error'] = 'SSL verification disabled for local testing';
                }
            }
        } else {
            $result['status_code'] = wp_remote_retrieve_response_code($response);

            // Consider the URL accessible if we get any response (even 404 is fine for testing)
            // The important thing is that the server is reachable
            $result['accessible'] = ($result['status_code'] > 0 && $result['status_code'] < 500);

            if (!$result['accessible'] && $result['status_code'] >= 500) {
                $result['error'] = 'Server error (HTTP ' . $result['status_code'] . ')';
            }
        }

        return $result;
    }

    /**
     * AJAX handler for dismissing Cloudflare notice
     */
    public function ajax_dismiss_cloudflare_notice() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'stablecoinpay_dismiss_notice')) {
            wp_send_json_error('Security check failed');
        }

        // Check user permissions
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error('Insufficient permissions');
        }

        // Mark notice as dismissed for current user
        update_user_meta(get_current_user_id(), 'stablecoinpay_cloudflare_notice_dismissed', true);

        wp_send_json_success('Notice dismissed');
    }

    /**
     * AJAX handler for getting Cloudflare IP ranges
     */
    public function ajax_get_cloudflare_ips() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'stablecoinpay_cloudflare')) {
            wp_send_json_error('Security check failed');
        }

        // Check user permissions
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error('Insufficient permissions');
        }

        $cloudflare_ips = $this->get_cloudflare_ip_ranges();
        $is_cloudflare = $this->is_cloudflare_detected();

        if (!empty($cloudflare_ips)) {
            wp_send_json_success(array(
                'ips' => $cloudflare_ips,
                'is_cloudflare_detected' => $is_cloudflare,
                'formatted_ips' => implode("\n", $cloudflare_ips),
                'count' => count($cloudflare_ips)
            ));
        } else {
            wp_send_json_error('Failed to fetch Cloudflare IP ranges');
        }
    }

    /**
     * Get Cloudflare IP ranges for display (public method)
     *
     * @return array Array of IP ranges
     */
    public static function get_cloudflare_ips_for_display() {
        $admin = new self();
        return $admin->get_cloudflare_ip_ranges();
    }

    /**
     * Check if Cloudflare is detected (public method)
     *
     * @return bool True if Cloudflare is detected
     */
    public static function is_using_cloudflare() {
        $admin = new self();
        return $admin->is_cloudflare_detected();
    }

    // Webhook endpoint AJAX handlers removed - using manual configuration approach
}
