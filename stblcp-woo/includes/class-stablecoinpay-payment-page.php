<?php
/**
 * Payment Page Handler
 * 
 * Handles the self-hosted payment page functionality
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_Payment_Page {

    public function __construct() {
        add_action('init', array($this, 'add_rewrite_rules'));
        add_filter('query_vars', array($this, 'add_query_vars'));
        add_action('template_redirect', array($this, 'handle_payment_page'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_payment_scripts'));
        add_action('wp_ajax_stablecoinpay_switch_payment_method', array($this, 'ajax_switch_payment_method'));
        add_action('wp_ajax_nopriv_stablecoinpay_switch_payment_method', array($this, 'ajax_switch_payment_method'));
        add_action('wp_ajax_stablecoinpay_trigger_immediate_expiry', array($this, 'ajax_trigger_immediate_expiry'));
        add_action('wp_ajax_nopriv_stablecoinpay_trigger_immediate_expiry', array($this, 'ajax_trigger_immediate_expiry'));
        add_action('wp_ajax_stablecoinpay_get_payment_status', array($this, 'ajax_get_payment_status'));
        add_action('wp_ajax_nopriv_stablecoinpay_get_payment_status', array($this, 'ajax_get_payment_status'));

        // Add admin action to flush rewrite rules
        add_action('admin_init', array($this, 'handle_admin_actions'));
    }

    /**
     * Add rewrite rules for payment page
     */
    public function add_rewrite_rules() {
        add_rewrite_rule(
            '^stablecoinpay-payment/?$',
            'index.php?stablecoinpay_payment_page=1',
            'top'
        );

        add_rewrite_tag('%stablecoinpay_payment_page%', '([^&]+)');
    }

    /**
     * Add query vars for payment page
     */
    public function add_query_vars($vars) {
        $vars[] = 'stablecoinpay_payment_page';
        return $vars;
    }

    /**
     * Manual flush rewrite rules (for debugging)
     * Call this function if payment page URLs are not working
     */
    public static function flush_rewrite_rules() {
        // Add the rewrite rules
        add_rewrite_rule(
            '^stablecoinpay-payment/?$',
            'index.php?stablecoinpay_payment_page=1',
            'top'
        );
        add_rewrite_tag('%stablecoinpay_payment_page%', '([^&]+)');

        // Flush the rules
        flush_rewrite_rules();

        if (WP_DEBUG) {
            error_log('StablecoinPay: Rewrite rules flushed manually');
        }
    }

    /**
     * Handle admin actions
     */
    public function handle_admin_actions() {
        if (isset($_GET['stablecoinpay_flush_rules']) && current_user_can('manage_options')) {
            if (wp_verify_nonce($_GET['_wpnonce'], 'stablecoinpay_flush_rules')) {
                self::flush_rewrite_rules();
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-success is-dismissible"><p>';
                    echo __('StablecoinPay rewrite rules have been flushed. Payment page URLs should now work.', 'stablecoinpay-gateway');
                    echo '</p></div>';
                });
            }
        }
    }

    /**
     * Handle payment page request
     */
    public function handle_payment_page() {
        $payment_page_var = get_query_var('stablecoinpay_payment_page');
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';

        // Check if this is a payment page request (either via rewrite rule or direct URL)
        $is_payment_page = $payment_page_var || strpos($request_uri, 'stablecoinpay-payment') !== false;

        // Debug: Log what we're getting
        if (WP_DEBUG) {
            error_log('StablecoinPay Payment Page Debug:');
            error_log('- Query var stablecoinpay_payment_page: ' . var_export($payment_page_var, true));
            error_log('- Request URI: ' . $request_uri);
            error_log('- Is payment page: ' . var_export($is_payment_page, true));
            error_log('- GET params: ' . print_r($_GET, true));
        }

        if (!$is_payment_page) {
            return;
        }

        $order_id = intval($_GET['order_id'] ?? 0);
        $payment_id = sanitize_text_field($_GET['payment_id'] ?? '');
        $order_key = sanitize_text_field($_GET['key'] ?? '');

        if (!$order_id || !$payment_id) {
            wp_die(__('Invalid payment parameters.', 'stablecoinpay-gateway'));
        }

        // Validate access
        if (!StablecoinPay_Security::validate_payment_access($order_id, $payment_id)) {
            wp_die(__('Access denied.', 'stablecoinpay-gateway'));
        }

        // Get order and payment data
        $order = StablecoinPay_HPOS::get_order($order_id);
        if (!$order) {
            wp_die(__('Order not found.', 'stablecoinpay-gateway'));
        }

        // Check if there's a current/switched payment ID stored in order meta
        $current_payment_id = StablecoinPay_HPOS::get_order_meta($order->get_id(), '_stablecoinpay_current_payment_id');
        $original_payment_id = $payment_id; // Store original for comparison

        if (!empty($current_payment_id)) {
            // Use the current payment ID (from payment switching)
            $payment_id = $current_payment_id;
            self::debug_log("Using switched payment ID: " . $payment_id . " (original: " . $original_payment_id . ")");
        } else {
            // Use original payment ID from URL
            self::debug_log("Using original payment ID: " . $payment_id);
        }

        // First try to get payment data from stored order meta (includes QR code)
        $stored_payment_data = StablecoinPay_HPOS::get_order_meta($order->get_id(), '_stablecoinpay_payment_data');
        $payment_data = array();

        if ($stored_payment_data) {
            $payment_data = json_decode($stored_payment_data, true) ?: array();
        }

        // Get fresh payment data from API for status updates
        $api = new StablecoinPay_API();
        $payment_response = $api->get_payment($payment_id);

        if ($payment_response && isset($payment_response['data'])) {
            $api_data = $payment_response['data'];

            // Log API data for debugging
            self::debug_log("Payment Page - API Data", [
                'payment_id' => $payment_id,
                'token' => $api_data['token'] ?? 'missing',
                'blockchain' => $api_data['blockchain'] ?? 'missing',
                'status' => $api_data['status'] ?? 'missing'
            ]);

            // Validate payment ID consistency to prevent data mixing
            if (isset($api_data['id']) && $api_data['id'] !== $payment_id) {
                self::debug_log("Payment ID mismatch! Expected: " . $payment_id . ", Got: " . $api_data['id']);
                // Use the API payment ID as the source of truth
                $payment_id = $api_data['id'];
            }

            // Merge API data with stored data, prioritizing API for status and fresh data
            $payment_data = array_merge($payment_data, $api_data);

            // But keep QR code from stored data if API doesn't have it
            if (empty($api_data['qrCode']) && !empty($stored_payment_data)) {
                $stored_data = json_decode($stored_payment_data, true);
                if (!empty($stored_data['qrCode'])) {
                    $payment_data['qrCode'] = $stored_data['qrCode'];
                }
            }
        } elseif (empty($payment_data)) {
            wp_die(__('Payment not found.', 'stablecoinpay-gateway'));
        }



        // Render payment page
        $this->render_payment_page($order, $payment_data);
        exit;
    }

    /**
     * Render payment page
     * 
     * @param WC_Order $order WooCommerce order
     * @param array $payment_data Payment data from API
     */
    private function render_payment_page($order, $payment_data) {
        // Set page title
        $page_title = sprintf(__('Payment for Order #%s', 'stablecoinpay-gateway'), $order->get_order_number());

        // Get site info
        $site_name = get_bloginfo('name');
        $site_url = home_url();



        // CRITICAL FIX: Prepare payment info using exact API amounts
        $address = $payment_data['walletAddress'] ?? '';

        // Use exact amount from API without any manipulation
        $amount = $payment_data['targetAmount'] ?? $payment_data['amount'] ?? '0';
        $token = $payment_data['token'] ?? 'USDT';
        $qr_code = $payment_data['qrCode'] ?? '';

        // Generate QR code if not available
        if (empty($qr_code) && !empty($address) && !empty($amount)) {
            $qr_code = self::generate_qr_code($address, $amount, $token);
        }

        // Calculate server-side remaining time using UTC for consistency with backend
        $expires_at = $payment_data['expiryTime'] ?? $payment_data['expiresAt'] ?? '';
        $time_remaining = null;
        $is_expired = false;

        if (!empty($expires_at)) {
            // Convert backend UTC time to timestamp
            $expires_timestamp = strtotime($expires_at . ' UTC');

            // Use UTC time for calculation (not WordPress timezone)
            $current_timestamp = time(); // UTC timestamp

            $remaining_seconds = $expires_timestamp - $current_timestamp;

            // Always report actual server time remaining (can be negative)
            $time_remaining = $remaining_seconds;
            $is_expired = ($remaining_seconds <= 0);


        }

        $payment_info = array(
            'id' => $payment_data['id'] ?? '',
            'status' => $payment_data['status'] ?? 'UNKNOWN',
            'amount' => $amount,
            'token' => $token,
            'blockchain' => $payment_data['blockchain'] ?? 'ETHEREUM',
            'network' => $payment_data['network'] ?? 'MAINNET',
            'walletAddress' => $address, // Changed from 'address' to 'walletAddress' for consistency
            'qr_code' => $qr_code,
            'expires_at' => $expires_at,
            'time_remaining_seconds' => $time_remaining,
            'is_expired' => $is_expired,
            'server_time' => current_time('mysql'),
            'order_total' => $order->get_total(),
            'order_currency' => $order->get_currency()
        );

        // Include payment page template
        include STABLECOINPAY_PLUGIN_PATH . 'templates/payment-page.php';
    }

    /**
     * Enqueue payment page scripts
     */
    public function enqueue_payment_scripts() {
        $payment_page_var = get_query_var('stablecoinpay_payment_page');
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        $is_payment_page = $payment_page_var || strpos($request_uri, 'stablecoinpay-payment') !== false;

        if (!$is_payment_page) {
            return;
        }

        // Enqueue payment page CSS
        wp_enqueue_style(
            'stablecoinpay-payment-page',
            STABLECOINPAY_PLUGIN_URL . 'assets/css/payment-page.css',
            array(),
            STABLECOINPAY_VERSION
        );

        // Enqueue payment page JavaScript (now using unified version)
        wp_enqueue_script(
            'stablecoinpay-payment-page',
            STABLECOINPAY_PLUGIN_URL . 'assets/js/payment-page.js',
            array('jquery'),
            STABLECOINPAY_VERSION,
            true
        );

        // Localize script with payment data
        $order_id = intval($_GET['order_id'] ?? 0);
        $payment_id = sanitize_text_field($_GET['payment_id'] ?? '');

        wp_localize_script('stablecoinpay-payment-page', 'stablecoinpay_payment', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'order_id' => $order_id,
            'payment_id' => $payment_id, // This is now the current payment ID (switched if applicable)
            'nonce' => wp_create_nonce('stablecoinpay_payment'),
            'poll_interval' => 5000, // 5 seconds
            'strings' => array(
                'waiting' => __('Waiting for payment...', 'stablecoinpay-gateway'),
                'detected' => __('Payment detected!', 'stablecoinpay-gateway'),
                'confirmed' => __('Payment confirmed!', 'stablecoinpay-gateway'),
                'expired' => __('Payment expired', 'stablecoinpay-gateway'),
                'canceled' => __('Payment canceled', 'stablecoinpay-gateway'),
                'error' => __('An error occurred', 'stablecoinpay-gateway'),
                'copied' => __('Copied to clipboard!', 'stablecoinpay-gateway'),
                'copy_failed' => __('Failed to copy', 'stablecoinpay-gateway'),
                'redirecting' => __('Redirecting...', 'stablecoinpay-gateway')
            )
        ));
    }

    /**
     * ARCHITECTURAL FIX: Rate limiting removed from plugin
     * Backend API handles all rate limiting - no need for duplicate limiting
     *
     * @deprecated Rate limiting moved to backend API
     * @return bool Always returns true - rate limiting handled by backend
     */
    private static function check_rate_limit($action, $identifier, $limit = 60, $window = 60) {
        // ARCHITECTURAL FIX: Backend API handles rate limiting
        // No need for duplicate rate limiting in plugin
        return true;
    }

    /**
     * Verify CSRF token for AJAX requests
     */
    private static function verify_ajax_nonce($nonce, $action = 'stablecoinpay_payment') {
        if (!wp_verify_nonce($nonce, $action)) {
            StablecoinPay_Security::log_security_event('CSRF token verification failed', array(
                'action' => $action,
                'provided_nonce' => $nonce,
                'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? ''),
                'referer' => sanitize_text_field($_SERVER['HTTP_REFERER'] ?? '')
            ));
            wp_send_json_error('Security verification failed. Please refresh the page and try again.');
            wp_die();
        }
    }

    /**
     * Get payment status via AJAX
     */
    public static function ajax_get_payment_status() {
        // Ensure clean output buffer and proper headers
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Start fresh output buffer
        ob_start();

        // Set proper headers
        if (!headers_sent()) {
            header('Content-Type: application/json; charset=utf-8');
            header('Cache-Control: no-cache, must-revalidate');
        }

        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'] ?? '', 'stablecoinpay_payment')) {
                wp_send_json_error('Security verification failed');
                return;
            }

            $order_id = intval($_POST['order_id'] ?? 0);
            $payment_id = sanitize_text_field($_POST['payment_id'] ?? '');

            if (!$order_id || !$payment_id) {
                wp_send_json_error('Invalid parameters');
                return;
            }

            // Get payment status from API
            $api = new StablecoinPay_API();
            $payment_response = $api->get_payment($payment_id);

            if (!$payment_response || !isset($payment_response['data'])) {
                wp_send_json_error('Payment not found');
                return;
            }

            $payment_data = $payment_response['data'];

            // Get order for redirect URL
            $order = StablecoinPay_HPOS::get_order($order_id);
            $redirect_url = '';

            // Handle confirmed payments
            if ($payment_data['status'] === 'CONFIRMED' && $order && !$order->is_paid()) {
                $tx_hash = sanitize_text_field($payment_data['txHash'] ?? $payment_data['transactionHash'] ?? '');
                $order->payment_complete($tx_hash);
                StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_status', 'CONFIRMED');
                if (!empty($tx_hash)) {
                    StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_tx_hash', $tx_hash);
                }
                $redirect_url = $order->get_checkout_order_received_url();
            } elseif ($payment_data['status'] === 'CONFIRMED' && $order) {
                $redirect_url = $order->get_checkout_order_received_url();
            }

            // Calculate actual remaining time from API expiry time
            $expires_at = $payment_data['expiryTime'] ?? '';
            $time_remaining_seconds = 0;
            $is_expired = false;

            if (!empty($expires_at)) {
                // Convert API UTC time to timestamp
                $expires_timestamp = strtotime($expires_at . ' UTC');
                $current_timestamp = time(); // UTC timestamp
                $remaining_seconds = $expires_timestamp - $current_timestamp;

                $time_remaining_seconds = max(0, $remaining_seconds); // Don't return negative
                $is_expired = ($remaining_seconds <= 0);
            }

            // Simple response with basic data
            wp_send_json_success(array(
                'status' => $payment_data['status'],
                'tx_hash' => $payment_data['txHash'] ?? $payment_data['transactionHash'] ?? '',
                'expires_at' => $expires_at,
                'time_remaining_seconds' => $time_remaining_seconds,
                'is_expired' => $is_expired,
                'server_time' => current_time('mysql'),
                'confirmations' => $payment_data['confirmations'] ?? 0,
                'required_confirmations' => $payment_data['requiredConfirmations'] ?? 3,
                'blockchain' => $payment_data['blockchain'] ?? '',
                'network' => $payment_data['network'] ?? '',
                'redirect_url' => $redirect_url
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to get payment status: ' . $e->getMessage());
        } catch (Throwable $e) {
            wp_send_json_error('Server error occurred');
        }

        // Clean output buffer and exit
        $output = ob_get_clean();
        if (!empty($output)) {
            error_log('StablecoinPay AJAX unexpected output: ' . $output);
        }
        wp_die();
    }

    /**
     * Trigger immediate expiry via AJAX
     */
    public static function ajax_trigger_immediate_expiry() {
        $start_time = microtime(true);

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'stablecoinpay_payment')) {
            wp_die('Security check failed');
        }

        // Rate limiting - more restrictive for expiry actions
        $payment_id = sanitize_text_field($_POST['payment_id'] ?? '');
        if (!self::check_rate_limit('expiry', $payment_id, 5, 60)) {
            wp_send_json_error('Rate limit exceeded. Please wait before trying again.');
            return;
        }

        // Minimal validation for speed
        $payment_id = sanitize_text_field($_POST['payment_id'] ?? '');
        $order_id = intval($_POST['order_id'] ?? 0);

        if (!$payment_id || !$order_id) {
            wp_send_json_error('Invalid parameters');
        }

        // Validate access
        if (!StablecoinPay_Security::validate_payment_access($order_id, $payment_id)) {
            wp_send_json_error('Access denied');
        }

        // Fallback approach: Check if payment is expired and cancel it
        try {
            $api = new StablecoinPay_API();

            // First, get current payment status to check if it's actually expired
            $payment_response = $api->get_payment($payment_id);

            if (!$payment_response || !isset($payment_response['data'])) {
                throw new Exception('Failed to get payment data');
            }

            $payment_data = $payment_response['data'];
            $expires_at = $payment_data['expiryTime'] ?? '';
            $current_status = $payment_data['status'] ?? '';

            // Check if payment is actually expired
            $is_expired = false;
            if (!empty($expires_at)) {
                $expires_timestamp = strtotime($expires_at . ' UTC');
                $current_timestamp = time();
                $is_expired = ($expires_timestamp <= $current_timestamp);
            }



            $response_time = (microtime(true) - $start_time) * 1000;

            // If payment is expired and still waiting, try to cancel it
            if ($is_expired && $current_status === 'WAITING') {
                $cancel_response = $api->make_request('POST', '/payments/' . $payment_id . '/cancel');

                if ($cancel_response && isset($cancel_response['data'])) {
                    // Successfully cancelled/expired
                    $response = array(
                        'data' => array(
                            'status' => 'EXPIRED',
                            'isExpired' => true,
                            'serverTime' => gmdate('Y-m-d H:i:s'),
                            'timeRemaining' => 0,
                            'responseTime' => $response_time
                        )
                    );
                } else {
                    throw new Exception('Failed to cancel expired payment');
                }
            } else {
                // Payment not expired or already processed
                $response = array(
                    'data' => array(
                        'status' => $current_status,
                        'isExpired' => $is_expired,
                        'serverTime' => gmdate('Y-m-d H:i:s'),
                        'timeRemaining' => $is_expired ? 0 : max(0, $expires_timestamp - time()),
                        'responseTime' => $response_time
                    )
                );
            }

            if ($response && isset($response['data'])) {


            // Update order status if payment expired
            if ($response['data']['status'] === 'EXPIRED' || $response['data']['status'] === 'CANCELED') {
                $order = StablecoinPay_HPOS::get_order($order_id);
                if ($order && $order->get_status() === 'pending') {
                    StablecoinPay_HPOS::update_order_status($order_id, 'cancelled', __('Payment expired immediately', 'stablecoinpay-gateway'));
                    StablecoinPay_HPOS::add_order_note($order_id, __('Payment expired - frontend trigger', 'stablecoinpay-gateway'));
                    StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_status', 'EXPIRED');
                }
            }

            wp_send_json_success(array(
                'status' => $response['data']['status'] === 'CANCELED' ? 'EXPIRED' : $response['data']['status'],
                'isExpired' => $response['data']['isExpired'],
                'responseTime' => $response['data']['responseTime'],
                'serverTime' => $response['data']['serverTime'],
                'timeRemaining' => $response['data']['timeRemaining']
            ));
        } else {

            wp_send_json_error('Failed to process expiry - Invalid API response');
        }

        } catch (Exception $e) {
            $response_time = (microtime(true) - $start_time) * 1000;

            wp_send_json_error('Failed to process expiry - ' . $e->getMessage());
        }
    }

    /**
     * Switch payment method via AJAX
     */
    public function ajax_switch_payment_method() {
        // Ensure clean output buffer and proper headers
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Start fresh output buffer
        ob_start();

        // Set proper headers
        if (!headers_sent()) {
            header('Content-Type: application/json; charset=utf-8');
            header('Cache-Control: no-cache, must-revalidate');
        }

        try {
            // Verify nonce first
            if (!wp_verify_nonce($_POST['nonce'] ?? '', 'stablecoinpay_payment')) {
                wp_send_json_error('Security check failed');
                return;
            }

            // Get and validate parameters
            $order_id = intval($_POST['order_id'] ?? 0);
            $token = sanitize_text_field($_POST['token'] ?? '');
            $blockchain = sanitize_text_field($_POST['blockchain'] ?? '');

            if (!$order_id || !$token || !$blockchain) {
                wp_send_json_error('Invalid parameters');
                return;
            }

            // Get order
            $order = StablecoinPay_HPOS::get_order($order_id);
            if (!$order) {
                wp_send_json_error('Order not found');
                return;
            }

            // CRITICAL RACE CONDITION FIX: Add locking for payment method switching
            $lock_key = 'stablecoinpay_switch_' . $order_id;
            if (!$this->acquire_switch_lock($lock_key)) {
                wp_send_json_error('Payment method switching in progress, please wait');
                return;
            }

            try {
                // Check if order is still in valid state for switching
                $order_status = $order->get_status();
                if (!in_array($order_status, ['pending', 'on-hold'])) {
                    wp_send_json_error('Cannot switch payment method for order in ' . $order_status . ' status');
                    return;
                }

                // Check if there's already a confirmed payment
                $current_payment_status = StablecoinPay_HPOS::get_order_meta($order_id, '_stablecoinpay_status');
                if ($current_payment_status === 'CONFIRMED') {
                    wp_send_json_error('Payment already confirmed, cannot switch method');
                    return;
                }

                // Create new payment with selected token/blockchain
                $api = new StablecoinPay_API();

            // Get settings
            $test_mode = StablecoinPay_Settings::get_setting('test_mode', false);
            $expiry_minutes = StablecoinPay_Settings::get_setting('payment_expiry', 30);

            $payment_data = array(
                'amount' => $order->get_total(),
                'currency' => $order->get_currency(),
                'token' => $token,
                'blockchain' => $blockchain,
                'network' => $test_mode ? 'TESTNET' : 'MAINNET',
                'expiryMinutes' => $expiry_minutes,
                'metadata' => array(
                    'order_id' => $order->get_id(),
                    'order_number' => $order->get_order_number(),
                    'customer_email' => $order->get_billing_email()
                )
            );

            $payment_response = $api->create_payment($payment_data);

            if (!$payment_response || !isset($payment_response['data'])) {
                throw new Exception('Failed to create payment');
            }

            $new_payment_data = $payment_response['data'];



            // Store new payment data
            StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_payment_id', $new_payment_data['id']);
            StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_payment_data', wp_json_encode($new_payment_data));
            StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_status', $new_payment_data['status']);
            StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_current_payment_id', $new_payment_data['id']);

            // Calculate actual remaining time from new payment expiry time
            $expires_at = $new_payment_data['expiryTime'] ?? '';
            $time_remaining_seconds = 0;
            $is_expired = false;

            if (!empty($expires_at)) {
                // Convert API UTC time to timestamp
                $expires_timestamp = strtotime($expires_at . ' UTC');
                $current_timestamp = time(); // UTC timestamp
                $remaining_seconds = $expires_timestamp - $current_timestamp;

                $time_remaining_seconds = max(0, $remaining_seconds); // Don't return negative
                $is_expired = ($remaining_seconds <= 0);
            }

            // CRITICAL FIX: Prepare response data using exact API amounts
            $payment_info = array(
                'id' => $new_payment_data['id'],
                'status' => $new_payment_data['status'],
                'amount' => $new_payment_data['targetAmount'] ?? $new_payment_data['amount'] ?? '0', // Use exact API amount
                'token' => $token,
                'blockchain' => $blockchain,
                'network' => $new_payment_data['network'] ?? ($test_mode ? 'TESTNET' : 'MAINNET'),
                'walletAddress' => $new_payment_data['walletAddress'] ?? '',
                'qrCode' => $new_payment_data['qrCode'] ?? '',
                'expiresAt' => $expires_at,
                'timeRemainingSeconds' => $time_remaining_seconds,
                'isExpired' => $is_expired,
                'serverTime' => current_time('mysql')
            );

                wp_send_json_success(array(
                    'payment_info' => $payment_info,
                    'message' => 'Payment method updated successfully'
                ));

            } catch (Exception $e) {
                wp_send_json_error('Failed to update payment method: ' . $e->getMessage());
            } catch (Throwable $e) {
                wp_send_json_error('Server error occurred');
            } finally {
                // Always release the switch lock
                $this->release_switch_lock($lock_key);
            }

        } catch (Exception $e) {
            wp_send_json_error('Failed to update payment method: ' . $e->getMessage());
        } catch (Throwable $e) {
            wp_send_json_error('Server error occurred');
        }

        // Clean output buffer and exit
        $output = ob_get_clean();
        if (!empty($output)) {
            error_log('StablecoinPay AJAX unexpected output: ' . $output);
        }
        wp_die();
    }

    /**
     * Generate QR code for payment
     * 
     * @param string $address Wallet address
     * @param string $amount Amount to pay
     * @param string $token Token symbol
     * @return string QR code data URL
     */
    public static function generate_qr_code($address, $amount, $token) {
        // Create payment URI
        $payment_uri = sprintf('%s:%s?amount=%s', strtolower($token), $address, $amount);

        // Use a simple QR code generation method
        // In production, you might want to use a more robust QR code library
        $qr_api_url = sprintf(
            'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=%s',
            urlencode($payment_uri)
        );

        return $qr_api_url;
    }

    /**
     * Get supported tokens for selection
     * 
     * @return array Supported tokens
     */
    public static function get_supported_tokens() {
        return array(
            // Stablecoins
            'USDT' => array(
                'name' => 'Tether USD',
                'symbol' => 'USDT',
                'blockchains' => array('ETHEREUM', 'BSC', 'TRON', 'SOLANA', 'TON')
            ),
            'USDC' => array(
                'name' => 'USD Coin',
                'symbol' => 'USDC',
                'blockchains' => array('ETHEREUM', 'BSC', 'TRON', 'SOLANA', 'TON')
            ),
            'DAI' => array(
                'name' => 'Dai Stablecoin',
                'symbol' => 'DAI',
                'blockchains' => array('ETHEREUM', 'BSC')
            ),
            'PYUSD' => array(
                'name' => 'PayPal USD',
                'symbol' => 'PYUSD',
                'blockchains' => array('ETHEREUM')
            ),
            'BUSD' => array(
                'name' => 'Binance USD',
                'symbol' => 'BUSD',
                'blockchains' => array('BSC')
            ),
            // Native Coins
            'ETH' => array(
                'name' => 'Ethereum',
                'symbol' => 'ETH',
                'blockchains' => array('ETHEREUM')
            ),
            'BNB' => array(
                'name' => 'Binance Coin',
                'symbol' => 'BNB',
                'blockchains' => array('BSC')
            ),
            'TRX' => array(
                'name' => 'Tron',
                'symbol' => 'TRX',
                'blockchains' => array('TRON')
            ),
            'SOL' => array(
                'name' => 'Solana',
                'symbol' => 'SOL',
                'blockchains' => array('SOLANA')
            ),
            'TON' => array(
                'name' => 'Toncoin',
                'symbol' => 'TON',
                'blockchains' => array('TON')
            )
        );
    }

    /**
     * Get blockchain display names
     * 
     * @return array Blockchain display names
     */
    public static function get_blockchain_names() {
        return array(
            'ETHEREUM' => 'Ethereum',
            'BSC' => 'Binance Smart Chain',
            'TRON' => 'Tron',
            'SOLANA' => 'Solana',
            'TON' => 'TON'
        );
    }

    /**
     * Format time remaining
     * 
     * @param string $expires_at Expiration timestamp
     * @return array Time remaining data
     */
    public static function format_time_remaining($expires_at) {
        if (empty($expires_at)) {
            return array('expired' => true, 'formatted' => '00:00');
        }

        $expires_timestamp = strtotime($expires_at);
        $current_timestamp = current_time('timestamp');
        $remaining = $expires_timestamp - $current_timestamp;

        if ($remaining <= 0) {
            return array('expired' => true, 'formatted' => '00:00');
        }

        $minutes = floor($remaining / 60);
        $seconds = $remaining % 60;

        return array(
            'expired' => false,
            'formatted' => sprintf('%02d:%02d', $minutes, $seconds),
            'total_seconds' => $remaining
        );
    }

    /**
     * Environment-aware debug logging
     *
     * @param string $message Log message
     * @param array $context Additional context data
     */
    private static function debug_log($message, $context = array()) {
        // Only log in debug mode or development environment
        if (!WP_DEBUG && !StablecoinPay_Settings::get_setting('debug_mode', false)) {
            return;
        }

        $logger = wc_get_logger();
        $log_message = $message;

        if (!empty($context)) {
            $log_message .= ' - Context: ' . wp_json_encode($context);
        }

        $logger->debug($log_message, array('source' => 'stablecoinpay-payment-page'));
    }

    /**
     * Check if running in production environment
     *
     * @return bool True if production, false otherwise
     */
    private static function is_production() {
        // Check various indicators of production environment
        $indicators = array(
            !WP_DEBUG,
            !StablecoinPay_Settings::get_setting('debug_mode', false),
            !in_array($_SERVER['HTTP_HOST'] ?? '', ['localhost', '127.0.0.1', 'dev.local']),
            !StablecoinPay_Settings::get_setting('testmode', false)
        );

        // Consider production if most indicators are true
        return count(array_filter($indicators)) >= 3;
    }

    /**
     * Add security headers to response
     */
    private static function add_security_headers() {
        // Prevent caching of sensitive data
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Content security headers
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');

        // Only add HSTS in production and over HTTPS
        if (self::is_production() && is_ssl()) {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
    }

    /**
     * CRITICAL RACE CONDITION FIX: Acquire lock for payment method switching
     *
     * @param string $lock_key Unique lock key
     * @param int $timeout Lock timeout in seconds
     * @return bool True if lock acquired, false otherwise
     */
    private function acquire_switch_lock($lock_key, $timeout = 15) {
        global $wpdb;

        $lock_name = 'stablecoinpay_switch_' . md5($lock_key);
        $result = $wpdb->get_var($wpdb->prepare("SELECT GET_LOCK(%s, %d)", $lock_name, $timeout));

        if ($result === null) {
            error_log('StablecoinPay: Database error during switch lock acquisition: ' . $wpdb->last_error);
            return false;
        }

        return $result == 1;
    }

    /**
     * CRITICAL RACE CONDITION FIX: Release lock for payment method switching
     *
     * @param string $lock_key Unique lock key
     * @return bool True if lock released, false otherwise
     */
    private function release_switch_lock($lock_key) {
        global $wpdb;

        $lock_name = 'stablecoinpay_switch_' . md5($lock_key);
        $result = $wpdb->get_var($wpdb->prepare("SELECT RELEASE_LOCK(%s)", $lock_name));

        return $result == 1;
    }
}
