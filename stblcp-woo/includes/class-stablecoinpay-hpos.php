<?php
/**
 * HPOS Compatibility Layer
 * 
 * Provides compatibility methods for WooCommerce High-Performance Order Storage (HPOS)
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_HPOS {

    /**
     * Check if HPOS is enabled
     * 
     * @return bool True if HPOS is enabled, false otherwise
     */
    public static function is_hpos_enabled() {
        return class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') && 
               \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled();
    }

    /**
     * Get order using HPOS-compatible method
     * 
     * @param int $order_id Order ID
     * @return WC_Order|false Order object or false if not found
     */
    public static function get_order($order_id) {
        return wc_get_order($order_id);
    }

    /**
     * Update order meta using HPOS-compatible method
     * 
     * @param int $order_id Order ID
     * @param string $meta_key Meta key
     * @param mixed $meta_value Meta value
     * @return bool True on success, false on failure
     */
    public static function update_order_meta($order_id, $meta_key, $meta_value) {
        $order = self::get_order($order_id);
        if ($order) {
            $order->update_meta_data($meta_key, $meta_value);
            $order->save();
            return true;
        }
        return false;
    }

    /**
     * Get order meta using HPOS-compatible method
     * 
     * @param int $order_id Order ID
     * @param string $meta_key Meta key
     * @param bool $single Whether to return a single value
     * @return mixed Meta value
     */
    public static function get_order_meta($order_id, $meta_key, $single = true) {
        $order = self::get_order($order_id);
        if ($order) {
            return $order->get_meta($meta_key, $single);
        }
        return $single ? '' : array();
    }

    /**
     * Delete order meta using HPOS-compatible method
     * 
     * @param int $order_id Order ID
     * @param string $meta_key Meta key
     * @return bool True on success, false on failure
     */
    public static function delete_order_meta($order_id, $meta_key) {
        $order = self::get_order($order_id);
        if ($order) {
            $order->delete_meta_data($meta_key);
            $order->save();
            return true;
        }
        return false;
    }

    /**
     * Search orders using HPOS-compatible method
     * 
     * @param array $args Query arguments
     * @return array Array of WC_Order objects
     */
    public static function search_orders($args) {
        return wc_get_orders($args);
    }

    /**
     * Add order note using HPOS-compatible method
     * 
     * @param int $order_id Order ID
     * @param string $note Note content
     * @param bool $is_customer_note Whether this is a customer note
     * @return int|false Note ID on success, false on failure
     */
    public static function add_order_note($order_id, $note, $is_customer_note = false) {
        $order = self::get_order($order_id);
        if ($order) {
            return $order->add_order_note($note, $is_customer_note);
        }
        return false;
    }

    /**
     * Update order status using HPOS-compatible method
     * 
     * @param int $order_id Order ID
     * @param string $status New status
     * @param string $note Optional note
     * @return bool True on success, false on failure
     */
    public static function update_order_status($order_id, $status, $note = '') {
        $order = self::get_order($order_id);
        if ($order) {
            $order->update_status($status, $note);
            return true;
        }
        return false;
    }

    /**
     * Get orders by meta query using HPOS-compatible method
     * 
     * @param string $meta_key Meta key to search
     * @param mixed $meta_value Meta value to search
     * @param string $meta_compare Comparison operator
     * @return array Array of WC_Order objects
     */
    public static function get_orders_by_meta($meta_key, $meta_value, $meta_compare = '=') {
        $args = array(
            'meta_query' => array(
                array(
                    'key' => $meta_key,
                    'value' => $meta_value,
                    'compare' => $meta_compare
                )
            ),
            'limit' => -1
        );

        return self::search_orders($args);
    }

    /**
     * Check if order exists
     * 
     * @param int $order_id Order ID
     * @return bool True if order exists, false otherwise
     */
    public static function order_exists($order_id) {
        $order = self::get_order($order_id);
        return $order && $order->get_id() > 0;
    }

    /**
     * Get order by payment ID
     * 
     * @param string $payment_id StablecoinPay payment ID
     * @return WC_Order|false Order object or false if not found
     */
    public static function get_order_by_payment_id($payment_id) {
        $orders = self::get_orders_by_meta('_stablecoinpay_payment_id', $payment_id);
        return !empty($orders) ? $orders[0] : false;
    }


}
