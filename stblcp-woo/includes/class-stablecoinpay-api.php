<?php
/**
 * StablecoinPay API Client
 * 
 * Handles communication with the StablecoinPay API
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_API {

    private $api_key;
    private $api_secret;
    private $base_url;
    private $testmode;
    private $logger;

    // Circuit breaker removed - backend API has proper rate limiting

    public function __construct($api_key = null, $api_secret = null, $base_url = null, $testmode = false) {
        $this->api_key = $api_key ?: StablecoinPay_Settings::get_setting('api_key');
        $this->api_secret = $api_secret ?: StablecoinPay_Settings::get_setting('api_secret');
        $this->testmode = $testmode ?: StablecoinPay_Settings::get_setting('test_mode');

        // Use configurable API URL or fallback to default
        $this->base_url = $base_url ?: StablecoinPay_Settings::get_setting('api_url', 'http://localhost:3000/api/v1');
        $this->base_url = rtrim($this->base_url, '/');

        // Initialize logger for debug mode
        $this->logger = wc_get_logger();

        // Circuit breaker removed - backend API handles rate limiting
        // Clean up any existing circuit breaker state
        delete_option('stablecoinpay_circuit_breaker_state');
    }

    /**
     * Create a new payment
     *
     * @param array $payment_data Payment data
     * @return array|false Payment response or false on failure
     */
    public function create_payment($payment_data) {
        $endpoint = '/payments';

        $token = sanitize_text_field($payment_data['token'] ?? 'USDT');

        // PRECISION FIX: Validate amount without converting to float to preserve precision
        // The API backend uses Decimal.js for precise amount handling
        if (!is_numeric($payment_data['amount']) || (float)$payment_data['amount'] <= 0) {
            $error = StablecoinPay_Error_Handler::validation_error(
                __('Please enter a valid payment amount', 'stablecoinpay-gateway'),
                array('amount' => $payment_data['amount'])
            );
            throw new Exception(StablecoinPay_Error_Handler::get_user_message($error));
        }

        $data = array(
            'amount' => $payment_data['amount'], // PRECISION FIX: Send as string to preserve precision
            'currency' => sanitize_text_field($payment_data['currency']),
            'token' => $token,
            'blockchain' => sanitize_text_field($payment_data['blockchain'] ?? 'ETHEREUM'),
            'network' => sanitize_text_field($payment_data['network'] ?? ($this->testmode ? 'TESTNET' : 'MAINNET')),
            'expiryMinutes' => intval($payment_data['expiryMinutes'] ?? 30),
            'metadata' => $payment_data['metadata'] ?? array()
        );

        // Note: Webhook endpoint ID is no longer automatically added
        // Manual webhook configuration is now used instead of automatic endpoint creation

        // ENHANCED DEBUGGING: Log payment creation data
        $this->log_error('=== PAYMENT CREATION DEBUG ===');
        $this->log_error('Original Payment Data: ' . wp_json_encode($payment_data));
        $this->log_error('Processed API Data: ' . wp_json_encode($data));
        $this->log_error('API Base URL: ' . $this->base_url);
        $this->log_error('API Key (first 10 chars): ' . substr($this->api_key, 0, 10) . '...');
        $this->log_error('Test Mode: ' . ($this->testmode ? 'true' : 'false'));

        return $this->make_request('POST', $endpoint, $data);
    }

    /**
     * Get payment details
     * 
     * @param string $payment_id Payment ID
     * @return array|false Payment details or false on failure
     */
    public function get_payment($payment_id) {
        $endpoint = '/payments/' . sanitize_text_field($payment_id);
        return $this->make_request('GET', $endpoint);
    }

    /**
     * Get payment status
     *
     * @param string $payment_id Payment ID
     * @return string|false Payment status or false on failure
     */
    public function get_payment_status($payment_id) {
        $payment = $this->get_payment($payment_id);
        return $payment ? $payment['status'] : false;
    }

    /**
     * Cancel a payment
     *
     * @param string $payment_id Payment ID
     * @param string $reason Optional cancellation reason
     * @return array|false Cancellation response or false on failure
     */
    public function cancel_payment($payment_id, $reason = 'Order cancelled') {
        $endpoint = '/payments/' . sanitize_text_field($payment_id) . '/cancel';

        $data = array();
        if (!empty($reason)) {
            $data['reason'] = sanitize_text_field($reason);
        }

        try {
            $response = $this->make_request('POST', $endpoint, $data);

            if ($response && isset($response['success']) && $response['success']) {
                $this->log_debug('Payment cancelled successfully: ' . $payment_id);
                return $response['data'];
            }

            $this->log_error('Failed to cancel payment: ' . $payment_id . ' - Response: ' . wp_json_encode($response));
            return false;

        } catch (Exception $e) {
            $this->log_error('Exception cancelling payment ' . $payment_id . ': ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Test API connection
     *
     * @return bool True if connection successful, false otherwise
     */
    public function test_connection() {
        try {
            // Check if credentials are set
            if (empty($this->api_key) || empty($this->api_secret)) {
                $this->log_error('Test connection failed: Missing API credentials');
                return false;
            }

            // Check if base URL is set
            if (empty($this->base_url)) {
                $this->log_error('Test connection failed: Missing API base URL');
                return false;
            }

            $endpoint = '/health';
            $response = $this->make_request('GET', $endpoint);

            // Validate response structure
            if (!is_array($response)) {
                $this->log_error('Test connection failed: Invalid response format');
                return false;
            }

            // Check for success indicator in response
            if (isset($response['success']) && $response['success'] === true) {
                $this->log_debug('Test connection successful');
                return true;
            }

            // Check for status field indicating healthy service
            if (isset($response['status']) && in_array($response['status'], ['ok', 'healthy', 'up'])) {
                $this->log_debug('Test connection successful (status: ' . $response['status'] . ')');
                return true;
            }

            // If we get a response but no clear success indicator, log it for debugging
            $this->log_error('Test connection: Ambiguous response - ' . wp_json_encode($response));

            // Be more strict about what we consider success
            // Only return true if we have a clear success indicator
            return false;

        } catch (Exception $e) {
            $this->log_error('Test connection failed with exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get supported tokens
     * 
     * @return array|false Supported tokens or false on failure
     */
    public function get_supported_tokens() {
        $endpoint = '/tokens';
        return $this->make_request('GET', $endpoint);
    }

    /**
     * Get exchange rates
     * 
     * @param string $currency Base currency (default: USD)
     * @return array|false Exchange rates or false on failure
     */
    public function get_exchange_rates($currency = 'USD') {
        $endpoint = '/rates/' . sanitize_text_field($currency);
        return $this->make_request('GET', $endpoint);
    }

    /**
     * Make HTTP request to API with retry logic
     *
     * @param string $method HTTP method
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @return array|false Response data or false on failure
     */
    public function make_request($method, $endpoint, $data = array(), $retry_count = 0) {
        // Circuit breaker removed - backend API handles rate limiting properly

        $url = $this->base_url . $endpoint;
        $max_retries = 3;

        $headers = array(
            'Content-Type' => 'application/json',
            'X-API-Key' => $this->api_key,
            'X-API-Secret' => $this->api_secret,
            'User-Agent' => 'WooCommerce-StablecoinPay/' . STABLECOINPAY_VERSION
        );

        $args = array(
            'method' => $method,
            'headers' => $headers,
            'timeout' => 30,
            'sslverify' => !$this->testmode
        );

        if (!empty($data) && in_array($method, array('POST', 'PUT', 'PATCH'))) {
            $args['body'] = wp_json_encode($data);
        }

        // ENHANCED DEBUGGING: Log detailed request information (only in debug mode)
        if (StablecoinPay_Settings::get_setting('debug_mode')) {
            $this->log_debug('=== DETAILED API REQUEST DEBUG ===');
            $this->log_debug('URL: ' . $url);
            $this->log_debug('Method: ' . $method);
            $this->log_debug('Headers: ' . wp_json_encode($headers));
            $this->log_debug('Request Data: ' . wp_json_encode($data));
            $this->log_debug('Test Mode: ' . ($this->testmode ? 'true' : 'false'));
        }

        $this->log_request($method, $url, $data);

        $response = wp_remote_request($url, $args);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $this->log_error('Request failed: ' . $error_message);

            // Retry on network errors with enhanced logic
            if ($retry_count < $max_retries && $this->is_retryable_error($error_message, 0)) {
                $delay = $this->calculate_retry_delay($retry_count);
                $this->log_error("Retrying request (attempt " . ($retry_count + 1) . "/$max_retries) after {$delay}s delay");
                sleep($delay);
                return $this->make_request($method, $endpoint, $data, $retry_count + 1);
            }

            $error = StablecoinPay_Error_Handler::network_error(
                __('Unable to connect to payment service. Please check your connection and try again.', 'stablecoinpay-gateway'),
                array('error_message' => $error_message, 'retry_count' => $retry_count)
            );
            throw new Exception(StablecoinPay_Error_Handler::get_user_message($error));
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        // ENHANCED DEBUGGING: Log detailed response information (only in debug mode)
        if (StablecoinPay_Settings::get_setting('debug_mode')) {
            $this->log_debug('=== DETAILED API RESPONSE DEBUG ===');
            $this->log_debug('Status Code: ' . $status_code);
            $this->log_debug('Response Body: ' . substr($body, 0, 500) . (strlen($body) > 500 ? '...' : ''));
        }

        $this->log_response($status_code, $body);

        if ($status_code < 200 || $status_code >= 300) {
            $this->log_error('HTTP error: ' . $status_code . ' - ' . substr($body, 0, 200));

            // Check if this status code is retryable
            if ($retry_count < $max_retries && $this->is_retryable_error('', $status_code)) {
                $delay = $this->calculate_retry_delay($retry_count);
                $this->log_error("Retrying HTTP $status_code error (attempt " . ($retry_count + 1) . "/$max_retries) after {$delay}s delay");
                sleep($delay);
                return $this->make_request($method, $endpoint, $data, $retry_count + 1);
            }

            // Try to parse error response for better error handling
            $error_data = json_decode($body, true);
            if ($error_data && isset($error_data['error'])) {
                $error_message = $error_data['error']['message'] ?? 'Unknown API error';
                $error_code = $error_data['error']['code'] ?? 'UNKNOWN_ERROR';

                // Sanitize error message for user display
                $user_safe_message = $this->sanitize_error_message($error_message, $error_code);
                $error = StablecoinPay_Error_Handler::api_error(
                    $user_safe_message,
                    array('error_code' => $error_code, 'status_code' => $status_code)
                );
                throw new Exception(StablecoinPay_Error_Handler::get_user_message($error));
            }

            // Generic error message for users
            $user_message = $this->get_user_friendly_error($status_code);
            throw new Exception($user_message);
        }

        $decoded = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->log_error('JSON decode error: ' . json_last_error_msg());
            throw new Exception(__('Invalid response from payment service.', 'stablecoinpay-gateway'));
        }

        return $decoded;
    }

    /**
     * Log API request
     * 
     * @param string $method HTTP method
     * @param string $url Request URL
     * @param array $data Request data
     */
    private function log_request($method, $url, $data) {
        if (StablecoinPay_Settings::get_setting('debug_mode')) {
            $message = sprintf(
                'API Request: %s %s - Data: %s',
                $method,
                $url,
                wp_json_encode($data)
            );
            $this->logger->info($message, array('source' => 'stablecoinpay'));
        }
    }

    /**
     * Log API response
     * 
     * @param int $status_code HTTP status code
     * @param string $body Response body
     */
    private function log_response($status_code, $body) {
        if (StablecoinPay_Settings::get_setting('debug_mode')) {
            $message = sprintf(
                'API Response: %d - Body: %s',
                $status_code,
                $body
            );
            $this->logger->info($message, array('source' => 'stablecoinpay'));
        }
    }

    /**
     * Log API error
     *
     * @param string $message Error message
     */
    private function log_error($message) {
        $this->logger->error($message, array('source' => 'stablecoinpay'));
    }

    /**
     * Log API debug information
     *
     * @param string $message Debug message
     */
    private function log_debug($message) {
        if (StablecoinPay_Settings::get_setting('debug_mode')) {
            $this->logger->debug($message, array('source' => 'stablecoinpay'));
        }
    }

    /**
     * Log API info message
     *
     * @param string $message Info message
     */
    private function log_info($message) {
        $this->logger->info($message, array('source' => 'stablecoinpay'));
    }

    /**
     * Check if error is retryable
     *
     * @param string $error_message Error message
     * @param int $status_code HTTP status code
     * @return bool True if error is retryable
     */
    private function is_retryable_error($error_message, $status_code = 0) {
        // Network-level retryable errors
        $retryable_errors = array(
            'timeout',
            'connection',
            'network',
            'dns',
            'ssl',
            'temporary',
            'reset',
            'refused',
            'unreachable'
        );

        $error_lower = strtolower($error_message);
        foreach ($retryable_errors as $retryable) {
            if (strpos($error_lower, $retryable) !== false) {
                return true;
            }
        }

        // HTTP status code based retry logic
        $retryable_status_codes = array(
            408, // Request Timeout
            429, // Too Many Requests
            500, // Internal Server Error
            502, // Bad Gateway
            503, // Service Unavailable
            504, // Gateway Timeout
            520, // Unknown Error (Cloudflare)
            521, // Web Server Is Down (Cloudflare)
            522, // Connection Timed Out (Cloudflare)
            523, // Origin Is Unreachable (Cloudflare)
            524  // A Timeout Occurred (Cloudflare)
        );

        return in_array($status_code, $retryable_status_codes);
    }

    /**
     * Calculate retry delay with exponential backoff and jitter
     *
     * @param int $attempt_number Current attempt number (0-based)
     * @param int $base_delay Base delay in seconds
     * @param int $max_delay Maximum delay in seconds
     * @return int Delay in seconds
     */
    private function calculate_retry_delay($attempt_number, $base_delay = 1, $max_delay = 30) {
        // Exponential backoff: base_delay * (2 ^ attempt_number)
        $delay = $base_delay * pow(2, $attempt_number);

        // Cap at maximum delay
        $delay = min($delay, $max_delay);

        // Add jitter (random variation of ±25%)
        $jitter = $delay * 0.25;
        $delay = $delay + mt_rand(-$jitter * 100, $jitter * 100) / 100;

        // Ensure minimum delay
        return max(1, round($delay));
    }

    /**
     * Validate API credentials
     * 
     * @return bool True if credentials are valid, false otherwise
     */
    public function validate_credentials() {
        if (empty($this->api_key) || empty($this->api_secret)) {
            return false;
        }

        return $this->test_connection();
    }

    /**
     * Sanitize error message for user display
     *
     * @param string $error_message Raw error message from API
     * @param string $error_code Error code from API
     * @return string User-safe error message
     */
    private function sanitize_error_message($error_message, $error_code) {
        // Map of error codes to user-friendly messages
        $error_map = array(
            'INVALID_CREDENTIALS' => __('Invalid API credentials. Please check your settings.', 'stablecoinpay-gateway'),
            'INSUFFICIENT_FUNDS' => __('Insufficient funds for this transaction.', 'stablecoinpay-gateway'),
            'INVALID_AMOUNT' => __('Invalid payment amount specified.', 'stablecoinpay-gateway'),
            'UNSUPPORTED_TOKEN' => __('The selected cryptocurrency is not supported.', 'stablecoinpay-gateway'),
            'RATE_LIMIT_EXCEEDED' => __('Too many requests. Please wait a moment and try again.', 'stablecoinpay-gateway'),
            'PAYMENT_EXPIRED' => __('Payment has expired. Please create a new payment.', 'stablecoinpay-gateway'),
            'NETWORK_ERROR' => __('Network connectivity issue. Please try again.', 'stablecoinpay-gateway')
        );

        // Return mapped message if available, otherwise generic message
        if (isset($error_map[$error_code])) {
            return $error_map[$error_code];
        }

        // Sanitize the raw message by removing sensitive information
        $sanitized = preg_replace('/\b(?:key|secret|token|password|credential)\b[:\s]*[^\s]+/i', '[REDACTED]', $error_message);
        $sanitized = preg_replace('/\b(?:\d{1,3}\.){3}\d{1,3}\b/', '[IP_REDACTED]', $sanitized);

        return sprintf(__('API Error: %s', 'stablecoinpay-gateway'), sanitize_text_field($sanitized));
    }

    /**
     * Get user-friendly error message based on HTTP status code
     *
     * @param int $status_code HTTP status code
     * @return string User-friendly error message with actionable guidance
     */
    private function get_user_friendly_error($status_code) {
        $status_messages = array(
            0 => __('Unable to connect to payment service. Please check your internet connection and try again.', 'stablecoinpay-gateway'),
            400 => __('Invalid payment request. Please verify your payment details and try again. If the problem persists, contact support.', 'stablecoinpay-gateway'),
            401 => __('Payment authentication failed. This is a configuration issue - please contact the store administrator.', 'stablecoinpay-gateway'),
            403 => __('Payment access denied. Your account may not have permission for this operation. Please contact support.', 'stablecoinpay-gateway'),
            404 => __('Payment service endpoint not found. This may be a temporary issue - please try again in a few minutes.', 'stablecoinpay-gateway'),
            408 => __('Payment request timed out. Please check your internet connection and try again.', 'stablecoinpay-gateway'),
            422 => __('Payment data validation failed. Please check your payment details and try again.', 'stablecoinpay-gateway'),
            429 => __('Too many payment requests. Please wait 30 seconds and try again.', 'stablecoinpay-gateway'),
            500 => __('Payment service is experiencing technical difficulties. Please try again in a few minutes or contact support.', 'stablecoinpay-gateway'),
            502 => __('Payment gateway is temporarily unavailable. Please try again in a few minutes.', 'stablecoinpay-gateway'),
            503 => __('Payment service is under maintenance. Please try again later or contact support for estimated completion time.', 'stablecoinpay-gateway'),
            504 => __('Payment service response timed out. Please try again. If this continues, contact support.', 'stablecoinpay-gateway')
        );

        $default_message = __('Payment processing failed. Please try refreshing the page or contact support if the problem continues.', 'stablecoinpay-gateway');

        return $status_messages[$status_code] ?? $default_message;
    }

    // Circuit breaker functionality removed - backend API handles rate limiting

    /**
     * Get API configuration
     *
     * @return array API configuration
     */
    public function get_config() {
        return array(
            'base_url' => $this->base_url,
            'testmode' => $this->testmode,
            'has_credentials' => !empty($this->api_key) && !empty($this->api_secret)
        );
    }

    /**
     * Get user's webhook endpoints
     *
     * @param int $user_id User ID (optional, uses current user if not provided)
     * @return array|false Webhook endpoints or false on failure
     */
    public function get_webhook_endpoints($user_id = null) {
        if (!$user_id) {
            // Get current user ID from JWT token or API key
            $user_id = $this->get_current_user_id();
        }

        if (!$user_id) {
            $this->log_error('Cannot get webhook endpoints: No user ID available');
            return false;
        }

        $endpoint = '/admin/users/' . intval($user_id) . '/webhooks/endpoints';

        $response = $this->make_request('GET', $endpoint);

        if ($response && isset($response['success']) && $response['success']) {
            return $response['data'];
        }

        return false;
    }

    /**
     * Create a webhook endpoint
     *
     * @param array $endpoint_data Webhook endpoint data
     * @return array|false Created endpoint or false on failure
     */
    public function create_webhook_endpoint($endpoint_data) {
        $user_id = $this->get_current_user_id();

        if (!$user_id) {
            $this->log_error('Cannot create webhook endpoint: No user ID available');
            return false;
        }

        $endpoint = '/admin/users/' . intval($user_id) . '/webhooks/endpoints';

        $response = $this->make_request('POST', $endpoint, $endpoint_data);

        if ($response && isset($response['success']) && $response['success']) {
            return $response['data'];
        }

        return false;
    }

    /**
     * Get or create default webhook endpoint for WooCommerce
     *
     * @return int|false Webhook endpoint ID or false on failure
     */
    public function get_or_create_woocommerce_webhook_endpoint() {
        // Check if we have a stored webhook endpoint ID
        $stored_endpoint_id = get_option('stablecoinpay_webhook_endpoint_id');

        if ($stored_endpoint_id) {
            // Verify the endpoint still exists and is active
            $endpoints = $this->get_webhook_endpoints();
            if ($endpoints) {
                foreach ($endpoints as $endpoint) {
                    if ($endpoint['id'] == $stored_endpoint_id && $endpoint['isActive']) {
                        return intval($stored_endpoint_id);
                    }
                }
            }
        }

        // Create a new webhook endpoint for WooCommerce
        $callback_url = home_url('/wc-api/stablecoinpay_callback/');

        $endpoint_data = array(
            'name' => 'WooCommerce - ' . get_bloginfo('name'),
            'url' => $callback_url,
            'events' => array(
                'payment.created',
                'payment.completed',
                'payment.failed',
                'payment.expired',
                'transaction.detected',
                'transaction.confirmed'
            ),
            'domain' => parse_url(home_url(), PHP_URL_HOST),
            'timeout' => 30000,
            'maxRetries' => 3,
            'retryDelay' => 5000,
            'retryBackoff' => 'exponential',
            'verifySSL' => is_ssl()
        );

        $created_endpoint = $this->create_webhook_endpoint($endpoint_data);

        if ($created_endpoint && isset($created_endpoint['id'])) {
            // Store the endpoint ID for future use
            update_option('stablecoinpay_webhook_endpoint_id', $created_endpoint['id']);

            $this->log_info('Created WooCommerce webhook endpoint: ' . $created_endpoint['id']);

            return intval($created_endpoint['id']);
        }

        $this->log_error('Failed to create WooCommerce webhook endpoint');
        return false;
    }

    /**
     * Get current user ID from API context
     *
     * @return int|false User ID or false if not available
     */
    private function get_current_user_id() {
        // For now, we'll use a stored user ID or admin user
        // In a full implementation, this would be extracted from JWT token
        $stored_user_id = get_option('stablecoinpay_api_user_id');

        if ($stored_user_id) {
            return intval($stored_user_id);
        }

        // Fallback: use admin user ID (this should be configured during setup)
        $admin_users = get_users(array('role' => 'administrator', 'number' => 1));
        if (!empty($admin_users)) {
            $user_id = $admin_users[0]->ID;
            update_option('stablecoinpay_api_user_id', $user_id);
            return $user_id;
        }

        return false;
    }
}
