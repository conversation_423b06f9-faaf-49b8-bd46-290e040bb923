<?php
/**
 * Blockchain Explorer Utility
 * 
 * Handles blockchain explorer URL generation for different networks
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_Blockchain_Explorer {

    /**
     * Get blockchain explorer URL for a transaction
     * 
     * @param string $blockchain Blockchain name (ETHEREUM, BSC, TRON, SOLANA, TON)
     * @param string $network Network type (MAINNET, TESTNET)
     * @param string $tx_hash Transaction hash
     * @return string Explorer URL
     */
    public static function get_transaction_url($blockchain, $network, $tx_hash) {
        if (empty($blockchain) || empty($network) || empty($tx_hash)) {
            return '';
        }

        $blockchain = strtoupper($blockchain);
        $network = strtoupper($network);
        $is_mainnet = strpos($network, 'MAINNET') !== false;

        switch ($blockchain) {
            case 'ETHEREUM':
            case 'ETH':
                return $is_mainnet 
                    ? "https://etherscan.io/tx/{$tx_hash}"
                    : "https://sepolia.etherscan.io/tx/{$tx_hash}";

            case 'BSC':
            case 'BINANCE':
                return $is_mainnet 
                    ? "https://bscscan.com/tx/{$tx_hash}"
                    : "https://testnet.bscscan.com/tx/{$tx_hash}";

            case 'TRON':
            case 'TRX':
                return $is_mainnet 
                    ? "https://tronscan.org/#/transaction/{$tx_hash}"
                    : "https://shasta.tronscan.org/#/transaction/{$tx_hash}";

            case 'SOLANA':
            case 'SOL':
                return $is_mainnet 
                    ? "https://solscan.io/tx/{$tx_hash}"
                    : "https://solscan.io/tx/{$tx_hash}?cluster=testnet";

            case 'TON':
                return $is_mainnet 
                    ? "https://tonscan.org/tx/{$tx_hash}"
                    : "https://testnet.tonscan.org/tx/{$tx_hash}";

            default:
                return '';
        }
    }

    /**
     * Get blockchain explorer URL for an address
     * 
     * @param string $blockchain Blockchain name
     * @param string $network Network type
     * @param string $address Wallet address
     * @return string Explorer URL
     */
    public static function get_address_url($blockchain, $network, $address) {
        if (empty($blockchain) || empty($network) || empty($address)) {
            return '';
        }

        $blockchain = strtoupper($blockchain);
        $network = strtoupper($network);
        $is_mainnet = strpos($network, 'MAINNET') !== false;

        switch ($blockchain) {
            case 'ETHEREUM':
            case 'ETH':
                return $is_mainnet 
                    ? "https://etherscan.io/address/{$address}"
                    : "https://sepolia.etherscan.io/address/{$address}";

            case 'BSC':
            case 'BINANCE':
                return $is_mainnet 
                    ? "https://bscscan.com/address/{$address}"
                    : "https://testnet.bscscan.com/address/{$address}";

            case 'TRON':
            case 'TRX':
                return $is_mainnet 
                    ? "https://tronscan.org/#/address/{$address}"
                    : "https://shasta.tronscan.org/#/address/{$address}";

            case 'SOLANA':
            case 'SOL':
                return $is_mainnet 
                    ? "https://solscan.io/account/{$address}"
                    : "https://solscan.io/account/{$address}?cluster=testnet";

            case 'TON':
                return $is_mainnet 
                    ? "https://tonscan.org/address/{$address}"
                    : "https://testnet.tonscan.org/address/{$address}";

            default:
                return '';
        }
    }

    /**
     * Get blockchain name for display
     * 
     * @param string $blockchain Blockchain name
     * @return string Display name
     */
    public static function get_blockchain_display_name($blockchain) {
        $blockchain = strtoupper($blockchain);
        
        switch ($blockchain) {
            case 'ETHEREUM':
            case 'ETH':
                return 'Ethereum';
            case 'BSC':
            case 'BINANCE':
                return 'BSC';
            case 'TRON':
            case 'TRX':
                return 'Tron';
            case 'SOLANA':
            case 'SOL':
                return 'Solana';
            case 'TON':
                return 'TON';
            default:
                return ucfirst(strtolower($blockchain));
        }
    }

    /**
     * Check if blockchain explorer URL is supported
     * 
     * @param string $blockchain Blockchain name
     * @return bool True if supported, false otherwise
     */
    public static function is_supported($blockchain) {
        $blockchain = strtoupper($blockchain);
        $supported = ['ETHEREUM', 'ETH', 'BSC', 'BINANCE', 'TRON', 'TRX', 'SOLANA', 'SOL', 'TON'];
        return in_array($blockchain, $supported);
    }
}
