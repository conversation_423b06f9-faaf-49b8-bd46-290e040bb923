<?php
/**
 * StablecoinPay Quick Buy AJAX Handlers
 * 
 * Handles AJAX requests for Quick Buy functionality
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_Quick_Buy_Ajax {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_ajax_hooks();
    }
    
    /**
     * Initialize AJAX hooks
     */
    private function init_ajax_hooks() {
        // Handle quick buy requests
        add_action('wp_ajax_stablecoinpay_quick_buy', array($this, 'handle_quick_buy'));
        add_action('wp_ajax_nopriv_stablecoinpay_quick_buy', array($this, 'handle_quick_buy'));

        // Handle email collection - OpenNode pattern (direct order creation)
        add_action('wp_ajax_stablecoinpay_email_collection', array($this, 'handle_email_collection'));
        add_action('wp_ajax_nopriv_stablecoinpay_email_collection', array($this, 'handle_email_collection'));
    }
    
    /**
     * Handle Quick Buy AJAX request
     */
    public function handle_quick_buy() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'stablecoinpay_quick_buy')) {
            wp_die('Security check failed');
        }
        
        try {
            $context = sanitize_text_field($_POST['context']);
            $method_key = sanitize_text_field(isset($_POST['method_key']) ? $_POST['method_key'] : '');
            $token = sanitize_text_field($_POST['token']);
            $blockchain = sanitize_text_field($_POST['blockchain']);

            // Debug logging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Quick Buy AJAX - Context: ' . $context);
                error_log('StablecoinPay Quick Buy AJAX - Method Key: ' . $method_key);
                error_log('StablecoinPay Quick Buy AJAX - Token: ' . $token);
                error_log('StablecoinPay Quick Buy AJAX - Blockchain: ' . $blockchain);
            }

            // Store selected payment method in session for later use
            if (!WC()->session) {
                WC()->session = new WC_Session_Handler();
                WC()->session->init();
            }

            // Store complete method information for accurate payment processing
            WC()->session->set('quick_buy_method_key', $method_key);
            WC()->session->set('quick_buy_token', $token);
            WC()->session->set('quick_buy_blockchain', $blockchain);

            if ($context === 'product') {
                $result = $this->process_product_quick_buy();
            } elseif ($context === 'cart') {
                $result = $this->process_cart_quick_buy();
            } else {
                $error = StablecoinPay_Error_Handler::validation_error(
                    __('Invalid request context', 'stablecoinpay-gateway'),
                    array('context' => $context)
                );
                throw new Exception(StablecoinPay_Error_Handler::get_user_message($error));
            }

            wp_send_json_success($result);

        } catch (Exception $e) {
            // Check if this is already a StablecoinPay error
            if (strpos($e->getMessage(), 'Payment service error:') !== false ||
                strpos($e->getMessage(), 'Connection error:') !== false ||
                strpos($e->getMessage(), 'Payment error:') !== false) {
                // Already formatted user message
                wp_send_json_error(array('message' => $e->getMessage()));
            } else {
                // Create generic error for unexpected exceptions
                $error = StablecoinPay_Error_Handler::handle_error(
                    'general',
                    $e->getMessage(),
                    array('trace' => $e->getTraceAsString()),
                    'medium',
                    false
                );
                wp_send_json_error(array(
                    'message' => __('An error occurred. Please try again or contact support.', 'stablecoinpay-gateway')
                ));
            }
        }
    }
    
    /**
     * Process product Quick Buy
     */
    private function process_product_quick_buy() {
        $product_id = intval($_POST['product_id']);
        $quantity = intval($_POST['quantity']) ?: 1;
        $variation_id = intval($_POST['variation_id']) ?: 0;
        $variation_data = $_POST['variation_data'] ?: array();

        // Get method key from POST data
        $method_key = sanitize_text_field($_POST['method_key'] ?? '');

        // Store method key in session for later use
        if ($method_key) {
            WC()->session->set('quick_buy_method_key', $method_key);

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Quick Buy: Stored method key in session: ' . $method_key);
            }
        }

        // Validate product
        $product = wc_get_product($product_id);
        if (!$product || !$product->is_purchasable()) {
            $error = StablecoinPay_Error_Handler::validation_error(
                __('This product is not available for purchase', 'stablecoinpay-gateway'),
                array('product_id' => $product_id)
            );
            throw new Exception(StablecoinPay_Error_Handler::get_user_message($error));
        }

        // Check stock
        if (!$product->is_in_stock()) {
            $error = StablecoinPay_Error_Handler::validation_error(
                __('This product is currently out of stock', 'stablecoinpay-gateway'),
                array('product_id' => $product_id)
            );
            throw new Exception(StablecoinPay_Error_Handler::get_user_message($error));
        }

        // For variable products, validate variation
        if ($product->is_type('variable') && !$variation_id) {
            $error = StablecoinPay_Error_Handler::validation_error(
                __('Please select product options before proceeding', 'stablecoinpay-gateway'),
                array('product_id' => $product_id)
            );
            throw new Exception(StablecoinPay_Error_Handler::get_user_message($error));
        }

        // Check if user is logged in
        if (is_user_logged_in()) {
            return $this->create_product_order_for_logged_in_user($product_id, $quantity, $variation_id, $variation_data);
        } else {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Quick Buy: Guest user - returning email modal data');
                error_log('StablecoinPay Quick Buy: Product data: ' . print_r(array(
                    'product_id' => $product_id,
                    'quantity' => $quantity,
                    'variation_id' => $variation_id,
                    'variation_data' => $variation_data
                ), true));
            }

            return array(
                'requires_email' => true,
                'method_key' => $method_key,  // CRITICAL FIX: Include method_key for email modal
                'product_data' => array(
                    'product_id' => $product_id,
                    'quantity' => $quantity,
                    'variation_id' => $variation_id,
                    'variation_data' => $variation_data
                )
            );
        }
    }
    
    /**
     * Process cart Quick Buy
     */
    private function process_cart_quick_buy() {
        // Check if cart has items
        if (!WC() || !WC()->cart || WC()->cart->is_empty()) {
            throw new Exception('Cart is empty');
        }

        // Get method key from session
        $method_key = WC()->session->get('quick_buy_method_key', '');

        // Check if user is logged in
        if (is_user_logged_in()) {
            return $this->create_cart_order_for_logged_in_user();
        } else {
            return array(
                'requires_email' => true,
                'method_key' => $method_key,  // CRITICAL FIX: Include method_key for email modal
                'cart_data' => WC()->cart->get_cart_contents()
            );
        }
    }

    /**
     * Create product order for logged-in user - Updated to use direct approach
     */
    private function create_product_order_for_logged_in_user($product_id, $quantity, $variation_id, $variation_data) {
        $current_user = wp_get_current_user();
        $method_key = WC()->session->get('quick_buy_method_key', '');

        return $this->create_direct_product_order(
            $product_id,
            $quantity,
            $variation_id,
            $variation_data,
            $current_user->user_email,
            $current_user->display_name,
            $method_key
        );
    }

    /**
     * Create cart order for logged-in user - Updated to use direct approach
     */
    private function create_cart_order_for_logged_in_user() {
        $current_user = wp_get_current_user();
        $method_key = WC()->session->get('quick_buy_method_key', '');

        return $this->create_direct_cart_order(
            $current_user->user_email,
            $current_user->display_name,
            $method_key
        );
    }

    /**
     * Handle email collection AJAX request - OpenNode Pattern
     * Direct order creation without session dependencies
     */
    public function handle_email_collection() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'stablecoinpay_quick_buy')) {
            wp_die('Security check failed');
        }

        try {
            $email = sanitize_email($_POST['email']);
            $name = sanitize_text_field($_POST['name']);
            $context = sanitize_text_field($_POST['context']);
            $method_key = sanitize_text_field($_POST['method_key'] ?? '');

            // Enhanced validation - OpenNode style
            if (empty($email) || !is_email($email)) {
                wp_send_json_error(array('message' => __('Valid email address is required', 'stablecoinpay')));
                return;
            }

            if (empty($method_key)) {
                wp_send_json_error(array('message' => __('Payment method selection is required', 'stablecoinpay')));
                return;
            }

            // Generate name from email if name field is disabled or empty
            if (empty($name) || !StablecoinPay_Settings::is_quick_buy_name_required()) {
                $email_parts = explode('@', $email);
                $name = ucfirst($email_parts[0]); // Use part before @ and capitalize first letter
            }

            // Debug logging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Email Collection: Email: ' . $email . ', Name: ' . $name . ', Context: ' . $context . ', Method: ' . $method_key);
            }

            // Direct order creation based on context - OpenNode approach
            if ($context === 'product') {
                $product_data = $_POST['product_data'];
                $result = $this->create_direct_product_order(
                    intval($product_data['product_id']),
                    intval($product_data['quantity']),
                    intval($product_data['variation_id'] ?? 0),
                    $product_data['variation_data'] ?? array(),
                    $email,
                    $name,
                    $method_key
                );
            } else {
                $result = $this->create_direct_cart_order($email, $name, $method_key);
            }

            wp_send_json_success($result);

        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Email Collection Error: ' . $e->getMessage());
            }
            wp_send_json_error(array('message' => $e->getMessage()));
        }
    }
    
    /**
     * Create direct product order - OpenNode Pattern
     * No session dependency, direct order creation
     */
    private function create_direct_product_order($product_id, $quantity, $variation_id, $variation_data, $email, $name, $method_key) {
        // Validate inputs - OpenNode style
        if (empty($product_id)) {
            throw new Exception('Invalid product');
        }

        // Create order directly - no session dependency
        $order = wc_create_order();
        if (is_wp_error($order)) {
            throw new Exception('Failed to create order: ' . $order->get_error_message());
        }

        // Add product to order
        $product = wc_get_product($variation_id ?: $product_id);
        if (!$product || !$product->is_purchasable()) {
            wp_delete_post($order->get_id(), true); // Cleanup
            throw new Exception('Product is not available for purchase');
        }

        $order->add_product($product, $quantity, array(
            'variation' => $variation_data
        ));

        // Set customer data - OpenNode approach
        $order->set_billing_email($email);

        if (!empty($name)) {
            $name_parts = explode(' ', trim($name), 2);
            $order->set_billing_first_name(sanitize_text_field($name_parts[0]));
            if (isset($name_parts[1])) {
                $order->set_billing_last_name(sanitize_text_field($name_parts[1]));
            }
        }

        // Calculate totals
        $order->calculate_totals();

        // Set payment method and metadata
        $order->set_payment_method('stablecoinpay');
        $order->set_payment_method_title('StablecoinPay');

        // Store method selection and source metadata
        StablecoinPay_HPOS::update_order_meta($order->get_id(), '_stablecoinpay_selected_method', $method_key);
        StablecoinPay_HPOS::update_order_meta($order->get_id(), '_stablecoinpay_order_source', 'quick_buy_guest_product');
        StablecoinPay_HPOS::update_order_meta($order->get_id(), '_stablecoinpay_source_product_id', $product_id);

        // Add order note
        $order->add_order_note(__('Order created via StablecoinPay Quick Buy button on product page.', 'stablecoinpay'));

        // Save order
        $order->save();

        // Process payment immediately - OpenNode pattern
        return $this->process_direct_payment($order, $method_key);
    }

    /**
     * Create direct cart order - OpenNode Pattern
     * Direct cart to order conversion without session dependencies
     */
    private function create_direct_cart_order($email, $name, $method_key) {
        // Debug logging for cart order creation
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('StablecoinPay: Starting cart order creation');
            error_log('StablecoinPay: Email: ' . $email . ', Method: ' . $method_key);
        }

        // Check if cart is empty
        if (WC()->cart->is_empty()) {
            throw new Exception('Your cart is empty');
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('StablecoinPay: Cart has ' . WC()->cart->get_cart_contents_count() . ' items');
        }

        try {
            // Create order directly using wc_create_order instead of checkout->create_order
            $order = wc_create_order();

            if (is_wp_error($order)) {
                throw new Exception('Failed to create order: ' . $order->get_error_message());
            }

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay: Order created with ID: ' . $order->get_id());
            }

            // Add cart items to order
            foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
                $product = $cart_item['data'];
                $quantity = $cart_item['quantity'];

                $order->add_product($product, $quantity, array(
                    'variation' => isset($cart_item['variation']) ? $cart_item['variation'] : array()
                ));
            }

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay: Added cart items to order');
            }

            // Set customer data
            $order->set_billing_email($email);

            // Add customer name if provided
            if (!empty($name)) {
                $name_parts = explode(' ', trim($name), 2);
                $order->set_billing_first_name(sanitize_text_field($name_parts[0]));
                if (isset($name_parts[1])) {
                    $order->set_billing_last_name(sanitize_text_field($name_parts[1]));
                }
            }

            // Calculate totals
            $order->calculate_totals();

            // Set payment method and metadata
            $order->set_payment_method('stablecoinpay');
            $order->set_payment_method_title('StablecoinPay');

            // Store method selection and source metadata
            StablecoinPay_HPOS::update_order_meta($order->get_id(), '_stablecoinpay_selected_method', $method_key);
            StablecoinPay_HPOS::update_order_meta($order->get_id(), '_stablecoinpay_order_source', 'quick_buy_guest_cart');

            // Add order note
            $order->add_order_note(__('Order created via StablecoinPay Quick Buy button on cart page.', 'stablecoinpay'));

            // Save order
            $order->save();

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay: Order saved, proceeding to payment processing');
            }

            // Process payment immediately - OpenNode pattern
            $result = $this->process_direct_payment($order, $method_key);

            // Empty cart on successful payment processing
            if (isset($result['redirect_url'])) {
                WC()->cart->empty_cart();
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('StablecoinPay: Cart emptied after successful payment processing');
                }
            }

            return $result;

        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay: Cart order creation failed: ' . $e->getMessage());
            }
            throw $e;
        }
    }
    
    /**
     * Process direct payment - OpenNode Pattern
     * Direct payment processing without intermediate steps
     */
    private function process_direct_payment($order, $method_key) {
        // Validate inputs - OpenNode style
        if (!$order || !$order->get_id()) {
            throw new Exception('Invalid order for payment processing');
        }

        if (empty($method_key)) {
            throw new Exception('Payment method selection is required');
        }

        // Initialize gateway
        if (!class_exists('WC_StablecoinPay_Gateway')) {
            throw new Exception('Payment gateway not available');
        }

        $gateway = new WC_StablecoinPay_Gateway();

        // CRITICAL FIX: Refresh settings in AJAX context to ensure proper configuration
        $gateway->refresh_settings();

        if (!$gateway->is_available()) {
            throw new Exception('Payment gateway not configured properly');
        }

        // Set payment method in $_POST for gateway processing - OpenNode approach
        $_POST['stablecoinpay_payment_method'] = $method_key;
        $_POST['payment_method'] = 'stablecoinpay';

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('StablecoinPay Direct Payment: Order ID: ' . $order->get_id());
            error_log('StablecoinPay Direct Payment: Method Key: ' . $method_key);
            error_log('StablecoinPay Direct Payment: Order Total: ' . $order->get_total());
        }

        // Process payment directly - no intermediate steps
        $result = $gateway->process_payment($order->get_id());

        if ($result['result'] === 'success') {
            if (empty($result['redirect'])) {
                throw new Exception('Payment processing succeeded but no redirect URL provided');
            }

            // Return redirect URL - OpenNode pattern
            return array('redirect_url' => $result['redirect']);
        } else {
            // Enhanced error handling
            $error_message = 'Payment processing failed';
            if (isset($result['messages'])) {
                $error_message .= ' - ' . $result['messages'];
            }

            // Log error for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('StablecoinPay Direct Payment Error: ' . $error_message);
                error_log('Order ID: ' . $order->get_id());
                error_log('Method Key: ' . $method_key);
            }

            throw new Exception($error_message);
        }
    }

    /**
     * Legacy method for backward compatibility
     * @deprecated Use process_direct_payment instead
     */
    private function process_payment_with_method($order, $method_key) {
        return $this->process_direct_payment($order, $method_key);
    }


}
