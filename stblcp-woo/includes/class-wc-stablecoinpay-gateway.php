<?php
/**
 * WooCommerce StablecoinPay Gateway
 * 
 * Main payment gateway class for WooCommerce integration
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WC_StablecoinPay_Gateway extends WC_Payment_Gateway {

    private $logger;
    private $testmode;
    private $api_url;
    private $api_key;
    private $api_secret;

    public function __construct() {
        $this->id = 'stablecoinpay';
        $this->icon = STABLECOINPAY_PLUGIN_URL . 'assets/images/stablecoinpay-icon.svg';
        $this->has_fields = true;
        $this->method_title = __('StablecoinPay', 'stablecoinpay-gateway');
        $this->method_description = __('Accept cryptocurrency payments via StablecoinPay', 'stablecoinpay-gateway');

        $this->supports = array(
            'products',
            'refunds'
        );

        // Checkout block support is handled in main plugin file

        $this->init_form_fields();
        $this->init_settings();

        $this->title = $this->get_option('title');
        $this->description = $this->get_option('description');
        $this->enabled = $this->get_option('enabled');
        $this->testmode = StablecoinPay_Settings::get_setting('test_mode', false);

        // Get settings from centralized settings
        $this->api_url = StablecoinPay_Settings::get_setting('api_url', 'http://localhost:3000/api/v1');
        $this->api_key = StablecoinPay_Settings::get_setting('api_key');
        $this->api_secret = StablecoinPay_Settings::get_setting('api_secret');



        add_action('woocommerce_update_options_payment_gateways_' . $this->id, array($this, 'process_admin_options'));
        add_action('wp_enqueue_scripts', array($this, 'payment_scripts'));

        // Initialize logger
        $this->logger = wc_get_logger();
    }

    /**
     * Initialize form fields
     */
    public function init_form_fields() {
        $this->form_fields = array(
            'enabled' => array(
                'title' => __('Enable/Disable', 'stablecoinpay-gateway'),
                'type' => 'checkbox',
                'label' => __('Enable StablecoinPay Gateway', 'stablecoinpay-gateway'),
                'default' => 'no'
            ),
            'title' => array(
                'title' => __('Title', 'stablecoinpay-gateway'),
                'type' => 'text',
                'description' => __('Payment method title that customers see', 'stablecoinpay-gateway'),
                'default' => __('Cryptocurrency Payment', 'stablecoinpay-gateway'),
                'desc_tip' => true,
            ),
            'description' => array(
                'title' => __('Description', 'stablecoinpay-gateway'),
                'type' => 'textarea',
                'description' => __('Payment method description', 'stablecoinpay-gateway'),
                'default' => __('Pay securely with cryptocurrency', 'stablecoinpay-gateway'),
            ),

            'settings_notice' => array(
                'title' => __('Configuration', 'stablecoinpay-gateway'),
                'type' => 'title',
                'description' => sprintf(
                    __('Configure API settings and other options in the <a href="%s">StablecoinPay Settings</a> page.', 'stablecoinpay-gateway'),
                    admin_url('admin.php?page=stablecoinpay-settings')
                ),
            )
        );
    }

    /**
     * Check if gateway is available
     */
    public function is_available() {
        // Debug logging
        if (StablecoinPay_Settings::get_setting('debug_mode')) {
            error_log('StablecoinPay is_available() check:');
            error_log('- Enabled: ' . ($this->enabled ? 'yes' : 'no'));
            error_log('- API Key: ' . (!empty($this->api_key) ? 'set (' . strlen($this->api_key) . ' chars)' : 'empty'));
            error_log('- API Secret: ' . (!empty($this->api_secret) ? 'set (' . strlen($this->api_secret) . ' chars)' : 'empty'));
        }

        if (!$this->enabled || 'yes' !== $this->enabled) {
            if (StablecoinPay_Settings::get_setting('debug_mode')) {
                error_log('StablecoinPay not available: Gateway not enabled');
            }
            return false;
        }

        // CRITICAL FIX: Refresh credentials if they appear to be empty
        // This can happen during AJAX requests or when gateway is instantiated in different contexts
        if (empty($this->api_key) || empty($this->api_secret)) {
            // Try to reload credentials from settings
            $this->api_key = StablecoinPay_Settings::get_setting('api_key');
            $this->api_secret = StablecoinPay_Settings::get_setting('api_secret');

            if (StablecoinPay_Settings::get_setting('debug_mode')) {
                error_log('StablecoinPay: Refreshed credentials from settings');
                error_log('- API Key after refresh: ' . (!empty($this->api_key) ? 'set (' . strlen($this->api_key) . ' chars)' : 'empty'));
                error_log('- API Secret after refresh: ' . (!empty($this->api_secret) ? 'set (' . strlen($this->api_secret) . ' chars)' : 'empty'));
            }
        }

        // Check if API credentials are configured after refresh
        if (empty($this->api_key) || empty($this->api_secret)) {
            if (StablecoinPay_Settings::get_setting('debug_mode')) {
                error_log('StablecoinPay not available: API credentials not configured');
            }
            return false;
        }

        return true;
    }

    /**
     * Refresh settings from centralized settings
     * Useful for AJAX contexts where settings might not be properly loaded
     */
    public function refresh_settings() {
        $this->api_url = StablecoinPay_Settings::get_setting('api_url', 'http://localhost:3000/api/v1');
        $this->api_key = StablecoinPay_Settings::get_setting('api_key');
        $this->api_secret = StablecoinPay_Settings::get_setting('api_secret');
        $this->testmode = StablecoinPay_Settings::get_setting('test_mode', false);

        if (StablecoinPay_Settings::get_setting('debug_mode')) {
            error_log('StablecoinPay: Settings refreshed');
            error_log('- API URL: ' . $this->api_url);
            error_log('- API Key: ' . (!empty($this->api_key) ? 'set (' . strlen($this->api_key) . ' chars)' : 'empty'));
            error_log('- API Secret: ' . (!empty($this->api_secret) ? 'set (' . strlen($this->api_secret) . ' chars)' : 'empty'));
            error_log('- Test Mode: ' . ($this->testmode ? 'yes' : 'no'));
        }
    }

    /**
     * Process payment
     */
    public function process_payment($order_id) {
        $order = StablecoinPay_HPOS::get_order($order_id);

        if (!$order) {
            wc_add_notice(__('Order not found.', 'stablecoinpay-gateway'), 'error');
            return array('result' => 'failure');
        }

        try {
            // Create StablecoinPay payment with configurable settings
            $api = new StablecoinPay_API();

            // Get payment method from checkout selection or use defaults
            // Handle both traditional checkout and block checkout
            $selected_method = '';

            // Traditional checkout
            if (isset($_POST['stablecoinpay_payment_method'])) {
                $selected_method = sanitize_text_field($_POST['stablecoinpay_payment_method']);
            }

            // Block checkout (WooCommerce Blocks)
            if (empty($selected_method) && isset($_POST['payment_data'])) {
                $payment_data_json = stripslashes($_POST['payment_data']);
                $payment_data_array = json_decode($payment_data_json, true);
                if (isset($payment_data_array['stablecoinpay_payment_method'])) {
                    $selected_method = sanitize_text_field($payment_data_array['stablecoinpay_payment_method']);
                }
            }

            $enabled_methods = StablecoinPay_Settings::get_enabled_payment_methods();

            // Debug: Log POST data
            $this->log("🔍 POST Data: " . wp_json_encode([
                'stablecoinpay_payment_method' => $selected_method,
                'has_payment_data' => isset($_POST['payment_data']),
                'all_post_keys' => array_keys($_POST),
                'enabled_methods_count' => count($enabled_methods)
            ]), 'info');

            if (!empty($selected_method) && isset($enabled_methods[$selected_method])) {
                $method = $enabled_methods[$selected_method];
                $token = $method['token'];
                $blockchain = $method['blockchain'];
                $this->log("✅ Using selected payment method from checkout: {$token} on {$blockchain} (method: {$selected_method})", 'info');
            } else {
                // Fallback to defaults
                $token = StablecoinPay_Settings::get_setting('default_token', 'USDT');
                $blockchain = StablecoinPay_Settings::get_setting('default_blockchain', 'ETHEREUM');
                $this->log("⚠️ Using default payment method (no selection): {$token} on {$blockchain} (selected_method: '{$selected_method}')", 'info');
            }

            $test_mode = StablecoinPay_Settings::get_setting('test_mode', false);

            // Log test mode status
            if ($test_mode) {
                $this->log('Test mode enabled - using TESTNET', 'info');
            } else {
                $this->log('Production mode - using MAINNET', 'info');
            }

            // Get enabled payment methods from settings
            $enabled_methods = StablecoinPay_Settings::get_enabled_payment_methods();

            // Find a valid token/blockchain combination from enabled methods
            $valid_combination_found = false;
            foreach ($enabled_methods as $method_key => $method) {
                if ($method['token'] === $token && $method['blockchain'] === $blockchain) {
                    $valid_combination_found = true;
                    break;
                }
            }

            // If current combination is not enabled, use the first enabled method
            if (!$valid_combination_found && !empty($enabled_methods)) {
                $first_method = reset($enabled_methods);
                $this->log('Token/blockchain combination not enabled: ' . $token . '/' . $blockchain . '. Using ' . $first_method['token'] . '/' . $first_method['blockchain'] . '.', 'warning');
                $token = $first_method['token'];
                $blockchain = $first_method['blockchain'];
            } elseif (empty($enabled_methods)) {
                // Fallback if no methods are enabled
                $this->log('No payment methods enabled. Using USDT/ETHEREUM as fallback.', 'warning');
                $token = 'USDT';
                $blockchain = 'ETHEREUM';
            }

            // Prepare payment data according to API specification
            $payment_data = array(
                'amount' => $order->get_total(),  // PRECISION FIX: Send as string to preserve precision
                'currency' => $order->get_currency(),       // API expects 'currency' (stored as fiatCurrency)
                'token' => $token,                          // Token symbol (USDT, USDC, etc.)
                'blockchain' => $blockchain,                // Blockchain enum (ETHEREUM, TRON, etc.)
                'network' => $test_mode ? 'TESTNET' : 'MAINNET', // Network enum (MAINNET, TESTNET)
                'expiryMinutes' => intval(StablecoinPay_Settings::get_setting('payment_expiry', 30)),
            );

            // Note: Webhook endpoint configuration is now handled manually
            // The webhook URL is configured directly in the StablecoinPay admin dashboard
            $webhooks_enabled = StablecoinPay_Settings::get_setting('enable_webhooks', false) ||
                               StablecoinPay_Settings::get_setting('enable_callbacks', false); // Backward compatibility

            if ($webhooks_enabled) {
                $this->log('Webhooks enabled - using manual webhook configuration', 'info');
            } else {
                $this->log('Webhooks disabled - payment status will be checked via polling', 'info');
            }

            $payment_data['metadata'] = array(
                'order_id' => $order_id,
                'order_key' => $order->get_order_key(),
                'customer_id' => $order->get_customer_id(),
                'customer_email' => $order->get_billing_email(),
                'site_url' => home_url(),
                'plugin_version' => STABLECOINPAY_VERSION
            );

            // Log the payment data for debugging
            $this->log('Creating payment with data: ' . wp_json_encode($payment_data), 'info');

            $payment = $api->create_payment($payment_data);

            if ($payment && isset($payment['data'])) {
                $payment_data_response = $payment['data'];

                // Store payment data using HPOS-compatible methods
                StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_payment_id', $payment_data_response['id']);
                StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_payment_data', wp_json_encode($payment_data_response));
                StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_status', $payment_data_response['status']);
                StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_created_at', current_time('mysql'));

                // 🔄 CRITICAL FIX: Clear any previous current payment ID and set new one
                StablecoinPay_HPOS::update_order_meta($order_id, '_stablecoinpay_current_payment_id', $payment_data_response['id']);
                $this->log("🆕 New payment created, cleared old current payment ID and set new: " . $payment_data_response['id'], 'info');

                // Store in custom table for better performance
                if (class_exists('StablecoinPay_Payment_Manager')) {
                    StablecoinPay_Payment_Manager::store_payment($order_id, $payment_data_response);
                }

                // Mark as pending
                StablecoinPay_HPOS::update_order_status($order_id, 'pending', __('Awaiting cryptocurrency payment', 'stablecoinpay-gateway'));

                // Add order note with debug info
                StablecoinPay_HPOS::add_order_note($order_id, sprintf(
                    __('StablecoinPay payment created. Payment ID: %s, Token: %s, Network: %s, Selected Method: %s', 'stablecoinpay-gateway'),
                    $payment_data_response['id'],
                    $payment_data_response['token'] ?? 'USDT',
                    $payment_data_response['network'] ?? 'MAINNET',
                    $selected_method ?: 'none'
                ));

                // Redirect to payment page with order key for security
                return array(
                    'result' => 'success',
                    'redirect' => add_query_arg(array(
                        'order_id' => $order_id,
                        'payment_id' => $payment_data_response['id'],
                        'key' => $order->get_order_key()
                    ), home_url('/stablecoinpay-payment/'))
                );
            }

        } catch (Exception $e) {
            // Log error if debug mode is enabled
            $this->log('Payment creation failed: ' . $e->getMessage(), 'error');

            // Provide more helpful error messages based on the error
            $error_message = $e->getMessage();
            $user_message = __('Payment processing failed. Please try again or contact support.', 'stablecoinpay-gateway');

            // Check for specific API errors
            if (strpos($error_message, 'PERMISSION_ERROR') !== false || strpos($error_message, '403') !== false) {
                $user_message = __('Payment service temporarily unavailable. Please try again later or contact support.', 'stablecoinpay-gateway');
                $this->log('API Permission Error - Check IP whitelist configuration', 'error');
            } elseif (strpos($error_message, 'connection') !== false || strpos($error_message, 'timeout') !== false) {
                $user_message = __('Connection error. Please check your internet connection and try again.', 'stablecoinpay-gateway');
            }

            wc_add_notice($user_message, 'error');
            return array('result' => 'failure');
        }

        return array('result' => 'failure');
    }

    /**
     * Process refund
     */
    public function process_refund($order_id, $amount = null, $reason = '') {
        $order = StablecoinPay_HPOS::get_order($order_id);

        if (!$order) {
            return new WP_Error('error', __('Order not found.', 'stablecoinpay-gateway'));
        }

        $payment_id = StablecoinPay_HPOS::get_order_meta($order_id, '_stablecoinpay_payment_id');

        if (empty($payment_id)) {
            return new WP_Error('error', __('Payment ID not found.', 'stablecoinpay-gateway'));
        }

        // Add refund note
        StablecoinPay_HPOS::add_order_note($order_id, sprintf(
            __('Refund requested: %s. Reason: %s. Manual processing required.', 'stablecoinpay-gateway'),
            wc_price($amount),
            $reason
        ));

        $this->log(sprintf('Refund requested for order %d, payment %s, amount %s', $order_id, $payment_id, $amount));

        return true;
    }

    /**
     * Display payment fields on checkout
     */
    public function payment_fields() {
        // Get enabled payment methods
        $enabled_methods = StablecoinPay_Settings::get_enabled_payment_methods();

        if (empty($enabled_methods)) {
            echo '<div class="stablecoinpay-error">' . __('No payment methods available.', 'stablecoinpay-gateway') . '</div>';
            return;
        }

        // Get default method
        $default_token = StablecoinPay_Settings::get_setting('default_token', 'USDT');
        $default_blockchain = StablecoinPay_Settings::get_setting('default_blockchain', 'ETHEREUM');
        $test_mode = (bool) StablecoinPay_Settings::get_setting('test_mode', false);

        // Organize methods by type
        $stablecoins = array();
        $native_coins = array();

        foreach ($enabled_methods as $method_key => $method) {
            $option_data = array(
                'value' => $method_key,
                'token' => $method['token'],
                'blockchain' => $method['blockchain'],
                'label' => $method['label'],
                'network_label' => $method['network_label']
            );

            $is_stablecoin = in_array($method['token'], array('USDT', 'USDC', 'DAI', 'PYUSD', 'BUSD'));
            if ($is_stablecoin) {
                $stablecoins[] = $option_data;
            } else {
                $native_coins[] = $option_data;
            }
        }

        // Display clean payment method selector
        ob_start(); // Start output buffering to catch any stray output
        ?>
        <div class="stablecoinpay-checkout-container">
            <?php if ($this->description): ?>
                <div class="stablecoinpay-description">
                    <?php echo wp_kses_post($this->description); ?>
                </div>
            <?php endif; ?>

            <div class="stablecoinpay-payment-selector">
                <label for="stablecoinpay-payment-method-dropdown" class="stablecoinpay-label">
                    <?php _e('Choose cryptocurrency:', 'stablecoinpay-gateway'); ?>
                </label>

                <div class="custom-select-wrapper">
                    <!-- Custom Dropdown -->
                    <div class="custom-dropdown" id="stablecoinpay-payment-method-dropdown" role="combobox" aria-labelledby="stablecoinpay-payment-method-label" aria-expanded="false">
                        <button type="button" class="dropdown-button" aria-haspopup="listbox">
                            <div class="dropdown-selected">
                                <?php
                                // Find the default selected option to show in button
                                $default_option = null;
                                foreach (array_merge($stablecoins, $native_coins) as $option) {
                                    if ($option['token'] === $default_token && $option['blockchain'] === $default_blockchain) {
                                        $default_option = $option;
                                        break;
                                    }
                                }

                                if ($default_option) {
                                    $icon_url = STABLECOINPAY_PLUGIN_URL . 'assets/icons/' . strtolower($default_option['token']) . '.svg';
                                    ?>
                                    <img class="dropdown-selected-icon" src="<?php echo esc_url($icon_url); ?>" alt="<?php echo esc_attr($default_option['token']); ?>">
                                    <div class="dropdown-selected-text">
                                        <span class="dropdown-selected-token"><?php echo esc_html($default_option['label']); ?></span>
                                        <span class="dropdown-selected-network"><?php echo esc_html($default_option['blockchain']); ?> Network</span>
                                    </div>
                                    <?php
                                } else {
                                    ?>
                                    <img class="dropdown-selected-icon" src="<?php echo esc_url(STABLECOINPAY_PLUGIN_URL . 'assets/icons/usdt.svg'); ?>" alt="Select" style="display: none;">
                                    <div class="dropdown-selected-text">
                                        <span class="dropdown-selected-token"><?php _e('Select payment method...', 'stablecoinpay-gateway'); ?></span>
                                        <span class="dropdown-selected-network" style="display: none;"></span>
                                    </div>
                                    <?php
                                }
                                ?>
                            </div>
                            <svg class="dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <div class="dropdown-menu" role="listbox">
                            <?php if (!empty($stablecoins)): ?>
                                <div class="dropdown-group">
                                    <div class="dropdown-group-label"><?php _e('Stablecoins', 'stablecoinpay-gateway'); ?></div>
                                    <?php foreach ($stablecoins as $option): ?>
                                        <?php
                                        $selected = ($option['token'] === $default_token && $option['blockchain'] === $default_blockchain) ? ' selected' : '';
                                        $icon_url = STABLECOINPAY_PLUGIN_URL . 'assets/icons/' . strtolower($option['token']) . '.svg';
                                        ?>
                                        <button type="button" class="dropdown-option<?php echo esc_attr($selected); ?>" data-value="<?php echo esc_attr($option['value']); ?>" data-token="<?php echo esc_attr($option['token']); ?>" data-blockchain="<?php echo esc_attr($option['blockchain']); ?>" data-label="<?php echo esc_attr($option['label']); ?>" role="option" tabindex="0">
                                            <img class="dropdown-option-icon" src="<?php echo esc_url($icon_url); ?>" alt="<?php echo esc_attr($option['token']); ?>">
                                            <div class="dropdown-option-text">
                                                <span class="dropdown-option-token"><?php echo esc_html($option['label']); ?></span>
                                                <span class="dropdown-option-network"><?php echo esc_html($option['blockchain']); ?> Network</span>
                                            </div>
                                        </button>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($native_coins)): ?>
                                <div class="dropdown-group">
                                    <div class="dropdown-group-label"><?php _e('Native Coins', 'stablecoinpay-gateway'); ?></div>
                                    <?php foreach ($native_coins as $option): ?>
                                        <?php
                                        $selected = ($option['token'] === $default_token && $option['blockchain'] === $default_blockchain) ? ' selected' : '';
                                        $icon_url = STABLECOINPAY_PLUGIN_URL . 'assets/icons/' . strtolower($option['token']) . '.svg';
                                        ?>
                                        <button type="button" class="dropdown-option<?php echo esc_attr($selected); ?>" data-value="<?php echo esc_attr($option['value']); ?>" data-token="<?php echo esc_attr($option['token']); ?>" data-blockchain="<?php echo esc_attr($option['blockchain']); ?>" data-label="<?php echo esc_attr($option['label']); ?>" role="option" tabindex="0">
                                            <img class="dropdown-option-icon" src="<?php echo esc_url($icon_url); ?>" alt="<?php echo esc_attr($option['token']); ?>">
                                            <div class="dropdown-option-text">
                                                <span class="dropdown-option-token"><?php echo esc_html($option['label']); ?></span>
                                                <span class="dropdown-option-network"><?php echo esc_html($option['blockchain']); ?> Network</span>
                                            </div>
                                        </button>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Hidden native select for form submission -->
                    <select id="stablecoinpay_payment_method" name="stablecoinpay_payment_method" class="payment-method-select" aria-hidden="true" tabindex="-1">
                        <option value=""><?php _e('Select payment method...', 'stablecoinpay-gateway'); ?></option>
                        <?php if (!empty($stablecoins)): ?>
                            <?php foreach ($stablecoins as $option): ?>
                                <?php
                                $selected = ($option['token'] === $default_token && $option['blockchain'] === $default_blockchain) ? 'selected' : '';
                                ?>
                                <option value="<?php echo esc_attr($option['value']); ?>" data-token="<?php echo esc_attr($option['token']); ?>" data-blockchain="<?php echo esc_attr($option['blockchain']); ?>" data-label="<?php echo esc_attr($option['label']); ?>" <?php echo esc_attr($selected); ?>>
                                    <?php echo esc_html($option['label']); ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <?php if (!empty($native_coins)): ?>
                            <?php foreach ($native_coins as $option): ?>
                                <?php
                                $selected = ($option['token'] === $default_token && $option['blockchain'] === $default_blockchain) ? 'selected' : '';
                                ?>
                                <option value="<?php echo esc_attr($option['value']); ?>" data-token="<?php echo esc_attr($option['token']); ?>" data-blockchain="<?php echo esc_attr($option['blockchain']); ?>" data-label="<?php echo esc_attr($option['label']); ?>" <?php echo esc_attr($selected); ?>>
                                    <?php echo esc_html($option['label']); ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
            </div>

            <?php if ($test_mode): ?>
                <div class="stablecoinpay-test-notice">
                    <span class="test-badge"><?php _e('TEST MODE', 'stablecoinpay-gateway'); ?></span>
                    <?php _e('No real payments will be processed', 'stablecoinpay-gateway'); ?>
                </div>
            <?php endif; ?>
        </div>
        <?php
        $output = ob_get_clean(); // Get the buffered output
        echo $output; // Echo the clean output
    }

    /**
     * Enqueue payment scripts
     */
    public function payment_scripts() {
        // Only load on checkout, cart, and admin pages
        if (!is_admin() && !is_cart() && !is_checkout()) {
            return;
        }

        // Check if WooCommerce Blocks is being used
        $is_blocks_checkout = $this->is_blocks_checkout();

        if ($is_blocks_checkout) {
            // Use unified styles for blocks checkout
            wp_enqueue_style(
                'stablecoinpay-checkout-unified',
                STABLECOINPAY_PLUGIN_URL . 'assets/css/checkout.css',
                array(),
                STABLECOINPAY_VERSION
            );
        } else {
            // Use unified styles for legacy checkout too (same CSS works for both)
            wp_enqueue_style(
                'stablecoinpay-checkout-unified',
                STABLECOINPAY_PLUGIN_URL . 'assets/css/checkout.css',
                array(),
                STABLECOINPAY_VERSION
            );

            // Load simple JavaScript for shortcode-based checkout
            wp_enqueue_script(
                'stablecoinpay-checkout-simple',
                STABLECOINPAY_PLUGIN_URL . 'assets/js/checkout-legacy.js',
                array('jquery'),
                STABLECOINPAY_VERSION,
                true
            );
            
            // Add inline script for fallback initialization
            wp_add_inline_script(
                'stablecoinpay-checkout-simple',
                'window.addEventListener("load", function() {
                     if (document.getElementById("stablecoinpay-payment-method-dropdown")) {
                         if (typeof initStablecoinPayDropdown === "function") {
                             initStablecoinPayDropdown();
                         }
                     }
                 });'
            );
        }
    }

    /**
     * Check if current checkout is using WooCommerce Blocks
     */
    private function is_blocks_checkout() {
        // Check if WooCommerce Blocks is active and checkout block is being used
        if (class_exists('\Automattic\WooCommerce\Blocks\Package') && 
            function_exists('has_block') && 
            is_checkout()) {
            
            // Check if checkout page has the checkout block
            $checkout_page_id = wc_get_page_id('checkout');
            if ($checkout_page_id && has_block('woocommerce/checkout', $checkout_page_id)) {
                error_log('StablecoinPay: Using Blocks checkout');
                return true;
            }
        }
        
        error_log('StablecoinPay: Using Legacy checkout');
        return false;
    }

    /**
     * Log messages if debug mode is enabled
     */
    private function log($message, $level = 'info') {
        if (StablecoinPay_Settings::get_setting('debug_mode')) {
            $this->logger->log($level, $message, array('source' => 'stablecoinpay'));
        }
    }

    /**
     * Admin options
     */
    public function admin_options() {
        ?>
        <h3><?php echo esc_html($this->method_title); ?></h3>
        <p><?php echo esc_html($this->method_description); ?></p>

        <?php if (!$this->api_key || !$this->api_secret): ?>
        <div class="notice notice-info">
            <p>
                <strong><?php _e('Quick Setup:', 'stablecoinpay-gateway'); ?></strong>
                <?php printf(
                    __('Configure your API credentials in the <a href="%s" target="_blank">StablecoinPay Settings</a> page to enable cryptocurrency payments.', 'stablecoinpay-gateway'),
                    admin_url('admin.php?page=stablecoinpay-settings')
                ); ?>
            </p>
        </div>
        <?php else: ?>
        <div class="notice notice-success">
            <p>
                <strong><?php _e('API Configured:', 'stablecoinpay-gateway'); ?></strong>
                <?php printf(
                    __('API credentials are set. <a href="%s" target="_blank">Manage settings</a> or enable the gateway below.', 'stablecoinpay-gateway'),
                    admin_url('admin.php?page=stablecoinpay-settings')
                ); ?>
            </p>
        </div>
        <?php endif; ?>

        <table class="form-table">
            <?php $this->generate_settings_html(); ?>
        </table>
        <?php
    }


}
