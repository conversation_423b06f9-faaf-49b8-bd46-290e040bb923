<?php
/**
 * Database Operations
 * 
 * Handles database table creation and management for StablecoinPay
 * 
 * @package StablecoinPay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class StablecoinPay_Database {

    /**
     * Create plugin database tables
     */
    public static function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Payments table
        $payments_table = $wpdb->prefix . 'stablecoinpay_payments';
        
        $payments_sql = "CREATE TABLE $payments_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            order_id bigint(20) NOT NULL,
            payment_id varchar(255) NOT NULL,
            status varchar(50) NOT NULL DEFAULT 'pending',
            token varchar(20) NOT NULL,
            blockchain varchar(50) NOT NULL,
            network varchar(20) NOT NULL,
            target_amount decimal(20,8) NOT NULL,
            request_amount decimal(20,8) NOT NULL,
            confirmed_amount decimal(20,8) DEFAULT NULL,
            fiat_amount decimal(20,8) NOT NULL,
            fiat_currency varchar(3) NOT NULL,
            wallet_address varchar(255) NOT NULL,
            tx_hash varchar(255) DEFAULT NULL,
            confirmations int(11) DEFAULT 0,
            webhook_endpoint_id int(11) DEFAULT NULL,
            metadata text DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY payment_id (payment_id),
            KEY order_id (order_id),
            KEY status (status),
            KEY status_created (status, created_at),
            KEY order_status (order_id, status),
            KEY blockchain_network (blockchain, network),
            KEY tx_hash (tx_hash),
            KEY created_at (created_at),
            KEY updated_at (updated_at),
            KEY wallet_address (wallet_address),
            KEY token_blockchain (token, blockchain),
            KEY confirmations (confirmations)
        ) $charset_collate;";

        // Transaction logs table
        $logs_table = $wpdb->prefix . 'stablecoinpay_transaction_logs';

        $logs_sql = "CREATE TABLE $logs_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            payment_id varchar(255) NOT NULL,
            event_type varchar(50) NOT NULL,
            event_data text DEFAULT NULL,
            ip_address varchar(45) DEFAULT NULL,
            user_agent text DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY payment_id (payment_id),
            KEY event_type (event_type),
            KEY created_at (created_at)
        ) $charset_collate;";

        // Rate limits table
        $rate_limits_table = $wpdb->prefix . 'stablecoinpay_rate_limits';

        $rate_limits_sql = "CREATE TABLE $rate_limits_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            identifier varchar(255) NOT NULL,
            action varchar(100) NOT NULL,
            order_id bigint(20) DEFAULT NULL,
            ip_address varchar(45) NOT NULL,
            attempts int(11) NOT NULL DEFAULT 1,
            window_start datetime NOT NULL,
            expires_at datetime NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_rate_limit (identifier, action),
            KEY order_id (order_id),
            KEY ip_address (ip_address),
            KEY expires_at (expires_at),
            KEY window_start (window_start),
            KEY action (action)
        ) $charset_collate;";

        // Security events table
        $security_events_table = $wpdb->prefix . 'stablecoinpay_security_events';

        $security_events_sql = "CREATE TABLE $security_events_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            event_type varchar(100) NOT NULL,
            severity enum('low','medium','high','critical') NOT NULL DEFAULT 'medium',
            order_id bigint(20) DEFAULT NULL,
            ip_address varchar(45) NOT NULL,
            user_agent text DEFAULT NULL,
            event_data text DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY event_type (event_type),
            KEY severity (severity),
            KEY order_id (order_id),
            KEY ip_address (ip_address),
            KEY created_at (created_at),
            KEY severity_created (severity, created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        // Capture dbDelta output to prevent "headers already sent" errors
        ob_start();
        $payments_result = dbDelta($payments_sql);
        $logs_result = dbDelta($logs_sql);
        $rate_limits_result = dbDelta($rate_limits_sql);
        $security_events_result = dbDelta($security_events_sql);
        $dbdelta_output = ob_get_clean();

        // Log dbDelta output for debugging if needed
        if (!empty($dbdelta_output) && WP_DEBUG) {
            error_log('StablecoinPay dbDelta output: ' . $dbdelta_output);
        }

        // Verify tables were created successfully
        if (!self::tables_exist()) {
            // Add admin notice for failed table creation
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>';
                echo __('StablecoinPay: Failed to create database tables. Please check your database permissions.', 'stablecoinpay-gateway');
                echo '</p></div>';
            });

            return false;
        }

        // Add foreign key constraints with proper error handling
        self::add_foreign_key_constraints();

        // Update database version
        update_option('stablecoinpay_db_version', STABLECOINPAY_VERSION);

        return true;
    }

    /**
     * Check if database tables exist
     *
     * @return bool True if tables exist, false otherwise
     */
    public static function tables_exist() {
        global $wpdb;

        $payments_table = $wpdb->prefix . 'stablecoinpay_payments';
        $logs_table = $wpdb->prefix . 'stablecoinpay_transaction_logs';
        $rate_limits_table = $wpdb->prefix . 'stablecoinpay_rate_limits';
        $security_events_table = $wpdb->prefix . 'stablecoinpay_security_events';

        $payments_exists = $wpdb->get_var("SHOW TABLES LIKE '$payments_table'") === $payments_table;
        $logs_exists = $wpdb->get_var("SHOW TABLES LIKE '$logs_table'") === $logs_table;
        $rate_limits_exists = $wpdb->get_var("SHOW TABLES LIKE '$rate_limits_table'") === $rate_limits_table;
        $security_events_exists = $wpdb->get_var("SHOW TABLES LIKE '$security_events_table'") === $security_events_table;

        return $payments_exists && $logs_exists && $rate_limits_exists && $security_events_exists;
    }

    /**
     * Add foreign key constraints to tables
     */
    private static function add_foreign_key_constraints() {
        global $wpdb;

        $payments_table = $wpdb->prefix . 'stablecoinpay_payments';
        $logs_table = $wpdb->prefix . 'stablecoinpay_transaction_logs';
        $rate_limits_table = $wpdb->prefix . 'stablecoinpay_rate_limits';
        $security_events_table = $wpdb->prefix . 'stablecoinpay_security_events';

        $foreign_keys = array(
            array(
                'table' => $logs_table,
                'constraint' => 'fk_transaction_logs_payment_id',
                'sql' => "ALTER TABLE {$logs_table}
                         ADD CONSTRAINT fk_transaction_logs_payment_id
                         FOREIGN KEY (payment_id) REFERENCES {$payments_table}(payment_id)
                         ON DELETE CASCADE ON UPDATE CASCADE"
            ),
            array(
                'table' => $rate_limits_table,
                'constraint' => 'fk_rate_limits_order_id',
                'sql' => "ALTER TABLE {$rate_limits_table}
                         ADD CONSTRAINT fk_rate_limits_order_id
                         FOREIGN KEY (order_id) REFERENCES {$wpdb->posts}(ID)
                         ON DELETE CASCADE ON UPDATE CASCADE"
            ),
            array(
                'table' => $security_events_table,
                'constraint' => 'fk_security_events_order_id',
                'sql' => "ALTER TABLE {$security_events_table}
                         ADD CONSTRAINT fk_security_events_order_id
                         FOREIGN KEY (order_id) REFERENCES {$wpdb->posts}(ID)
                         ON DELETE SET NULL ON UPDATE CASCADE"
            )
        );

        foreach ($foreign_keys as $fk) {
            // Check if constraint already exists
            $constraint_exists = $wpdb->get_var($wpdb->prepare("
                SELECT COUNT(*)
                FROM information_schema.TABLE_CONSTRAINTS
                WHERE CONSTRAINT_SCHEMA = %s
                AND TABLE_NAME = %s
                AND CONSTRAINT_NAME = %s
                AND CONSTRAINT_TYPE = 'FOREIGN KEY'
            ", DB_NAME, str_replace($wpdb->prefix, '', $fk['table']), $fk['constraint']));

            if (!$constraint_exists) {
                $result = $wpdb->query($fk['sql']);
                if ($result === false) {
                    error_log("Failed to create foreign key constraint {$fk['constraint']}: " . $wpdb->last_error);
                }
            }
        }
    }

    /**
     * Get database version
     * 
     * @return string Database version
     */
    public static function get_db_version() {
        return get_option('stablecoinpay_db_version', '0.0.0');
    }

    /**
     * Check if database needs update
     * 
     * @return bool True if update needed, false otherwise
     */
    public static function needs_update() {
        return version_compare(self::get_db_version(), STABLECOINPAY_VERSION, '<');
    }

    /**
     * Update database if needed
     */
    public static function maybe_update() {
        if (self::needs_update() || !self::tables_exist()) {
            self::create_tables();
            self::migrate_to_webhook_endpoints();
        }
    }

    /**
     * Migrate from callback_url to webhook_endpoint_id
     */
    private static function migrate_to_webhook_endpoints() {
        global $wpdb;

        $payments_table = $wpdb->prefix . 'stablecoinpay_payments';

        // Check if callback_url column still exists
        $column_exists = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = %s
            AND TABLE_NAME = %s
            AND COLUMN_NAME = 'callback_url'
        ", DB_NAME, str_replace($wpdb->prefix, '', $payments_table)));

        if ($column_exists) {
            // Add webhook_endpoint_id column if it doesn't exist
            $webhook_column_exists = $wpdb->get_var($wpdb->prepare("
                SELECT COUNT(*)
                FROM information_schema.COLUMNS
                WHERE TABLE_SCHEMA = %s
                AND TABLE_NAME = %s
                AND COLUMN_NAME = 'webhook_endpoint_id'
            ", DB_NAME, str_replace($wpdb->prefix, '', $payments_table)));

            if (!$webhook_column_exists) {
                $wpdb->query("ALTER TABLE {$payments_table} ADD COLUMN webhook_endpoint_id int(11) DEFAULT NULL");
            }

            // Set webhook_endpoint_id to NULL for all existing records (they'll use default endpoint)
            $wpdb->query("UPDATE {$payments_table} SET webhook_endpoint_id = NULL WHERE webhook_endpoint_id IS NULL");

            // Drop the callback_url column
            $wpdb->query("ALTER TABLE {$payments_table} DROP COLUMN callback_url");

            error_log('StablecoinPay: Migrated from callback_url to webhook_endpoint_id');
        }
    }

    /**
     * Clean up old transaction logs
     * 
     * @param int $days Number of days to keep logs (default: 30)
     */
    public static function cleanup_old_logs($days = 30) {
        global $wpdb;

        $logs_table = $wpdb->prefix . 'stablecoinpay_transaction_logs';
        
        $wpdb->query($wpdb->prepare(
            "DELETE FROM $logs_table WHERE created_at < DATE_SUB(NOW(), INTERVAL %d DAY)",
            $days
        ));
    }

    /**
     * Get payment statistics
     * 
     * @param int $days Number of days to analyze (default: 30)
     * @return array Payment statistics
     */
    public static function get_payment_stats($days = 30) {
        global $wpdb;

        $payments_table = $wpdb->prefix . 'stablecoinpay_payments';
        
        $stats = array();

        // Total payments
        $stats['total'] = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $payments_table WHERE created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)",
                $days
            )
        );

        // Confirmed payments
        $stats['confirmed'] = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $payments_table WHERE status = 'CONFIRMED' AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)",
                $days
            )
        );

        // Total amount
        $stats['total_amount'] = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT SUM(fiat_amount) FROM $payments_table WHERE status = 'CONFIRMED' AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)",
                $days
            )
        ) ?: 0;

        // Success rate
        $stats['success_rate'] = $stats['total'] > 0 ? round(($stats['confirmed'] / $stats['total']) * 100, 2) : 0;

        // Popular tokens
        $stats['popular_tokens'] = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT token, COUNT(*) as count FROM $payments_table WHERE created_at >= DATE_SUB(NOW(), INTERVAL %d DAY) GROUP BY token ORDER BY count DESC LIMIT 5",
                $days
            ),
            ARRAY_A
        );

        return $stats;
    }

    /**
     * Drop plugin tables (used during uninstall)
     */
    public static function drop_tables() {
        global $wpdb;

        $payments_table = $wpdb->prefix . 'stablecoinpay_payments';
        $logs_table = $wpdb->prefix . 'stablecoinpay_transaction_logs';
        $rate_limits_table = $wpdb->prefix . 'stablecoinpay_rate_limits';
        $security_events_table = $wpdb->prefix . 'stablecoinpay_security_events';

        // Drop tables in reverse order to handle foreign key constraints
        $wpdb->query("SET FOREIGN_KEY_CHECKS = 0");
        $wpdb->query("DROP TABLE IF EXISTS $logs_table");
        $wpdb->query("DROP TABLE IF EXISTS $rate_limits_table");
        $wpdb->query("DROP TABLE IF EXISTS $security_events_table");
        $wpdb->query("DROP TABLE IF EXISTS $payments_table");
        $wpdb->query("SET FOREIGN_KEY_CHECKS = 1");

        delete_option('stablecoinpay_db_version');
        delete_option('stablecoinpay_circuit_breaker_state');
    }

    /**
     * Start a database transaction
     *
     * @return bool True on success, false on failure
     */
    public static function start_transaction() {
        global $wpdb;

        $result = $wpdb->query('START TRANSACTION');

        if ($result === false) {
            error_log('StablecoinPay: Failed to start database transaction: ' . $wpdb->last_error);
            return false;
        }

        return true;
    }

    /**
     * Commit a database transaction
     *
     * @return bool True on success, false on failure
     */
    public static function commit_transaction() {
        global $wpdb;

        $result = $wpdb->query('COMMIT');

        if ($result === false) {
            error_log('StablecoinPay: Failed to commit database transaction: ' . $wpdb->last_error);
            return false;
        }

        return true;
    }

    /**
     * Rollback a database transaction
     *
     * @return bool True on success, false on failure
     */
    public static function rollback_transaction() {
        global $wpdb;

        $result = $wpdb->query('ROLLBACK');

        if ($result === false) {
            error_log('StablecoinPay: Failed to rollback database transaction: ' . $wpdb->last_error);
            return false;
        }

        return true;
    }

    /**
     * Execute a callback within a database transaction
     *
     * @param callable $callback Function to execute within transaction
     * @param array $args Arguments to pass to callback
     * @return mixed Result of callback or false on failure
     */
    public static function execute_in_transaction($callback, $args = array()) {
        if (!is_callable($callback)) {
            error_log('StablecoinPay: Invalid callback provided to execute_in_transaction');
            return false;
        }

        // Start transaction
        if (!self::start_transaction()) {
            return false;
        }

        try {
            // Execute callback
            $result = call_user_func_array($callback, $args);

            // If callback returned false or null, consider it a failure
            if ($result === false || $result === null) {
                self::rollback_transaction();
                return false;
            }

            // Commit transaction
            if (!self::commit_transaction()) {
                self::rollback_transaction();
                return false;
            }

            return $result;

        } catch (Exception $e) {
            // Rollback on any exception
            self::rollback_transaction();
            error_log('StablecoinPay: Transaction failed with exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if database supports transactions
     *
     * @return bool True if transactions are supported, false otherwise
     */
    public static function supports_transactions() {
        global $wpdb;

        // Check if we're using InnoDB or another transactional engine
        $engine = $wpdb->get_var("SELECT ENGINE FROM information_schema.TABLES WHERE TABLE_SCHEMA = '" . DB_NAME . "' AND TABLE_NAME = '" . $wpdb->prefix . "stablecoinpay_payments'");

        return in_array(strtolower($engine), array('innodb', 'xtradb', 'aria'));
    }

    /**
     * Get transaction isolation level
     *
     * @return string Current transaction isolation level
     */
    public static function get_transaction_isolation_level() {
        global $wpdb;

        return $wpdb->get_var("SELECT @@transaction_isolation");
    }

    /**
     * Set transaction isolation level for critical operations
     *
     * @param string $level Isolation level (READ COMMITTED, REPEATABLE READ, etc.)
     * @return bool True on success, false on failure
     */
    public static function set_transaction_isolation_level($level = 'READ COMMITTED') {
        global $wpdb;

        $valid_levels = array('READ UNCOMMITTED', 'READ COMMITTED', 'REPEATABLE READ', 'SERIALIZABLE');

        if (!in_array($level, $valid_levels)) {
            error_log('StablecoinPay: Invalid transaction isolation level: ' . $level);
            return false;
        }

        $result = $wpdb->query("SET SESSION TRANSACTION ISOLATION LEVEL " . $level);

        if ($result === false) {
            error_log('StablecoinPay: Failed to set transaction isolation level: ' . $wpdb->last_error);
            return false;
        }

        return true;
    }
}
