# StablecoinPay WooCommerce Plugin Installation Guide

## Prerequisites

Before installing the StablecoinPay WooCommerce plugin, ensure you have:

- WordPress 5.0 or higher
- WooCommerce 3.0 or higher  
- PHP 7.4 or higher
- MySQL 5.6 or higher
- SSL certificate (recommended)
- StablecoinPay API credentials

## Installation Steps

### 1. Download and Install

#### Option A: WordPress Admin (Recommended)
1. Log in to your WordPress admin panel
2. Go to **Plugins > Add New**
3. Search for "WooCommerce StablecoinPay Gateway"
4. Click **Install Now** and then **Activate**

#### Option B: Manual Installation
1. Download the plugin zip file
2. Go to **Plugins > Add New > Upload Plugin**
3. Choose the zip file and click **Install Now**
4. Click **Activate Plugin**

#### Option C: FTP Upload
1. Extract the plugin zip file
2. Upload the `stablecoinpay-gateway` folder to `/wp-content/plugins/`
3. Go to **Plugins** in WordPress admin
4. Activate "WooCommerce StablecoinPay Gateway"

### 2. Configure API Settings

1. Go to **WooCommerce > StablecoinPay** in your WordPress admin
2. Configure the following settings:

#### API Configuration
- **API URL**: Your StablecoinPay API endpoint (default: `http://localhost:3000/api/v1`)
- **API Key**: Your StablecoinPay API key
- **API Secret**: Your StablecoinPay API secret

#### Payment Settings  
- **Default Token**: Choose default cryptocurrency (USDT, USDC, DAI, PYUSD, BUSD)
- **Default Blockchain**: Select default network (Ethereum, BSC, Tron, Solana, TON)
- **Payment Expiry**: Set payment timeout in minutes (1-1440)

#### Advanced Settings
- **Test Mode**: Enable for testing with testnet
- **Debug Mode**: Enable for detailed logging
- **Callback IP Whitelist**: Restrict callback access by IP (optional)

3. Click **Save Changes**
4. Click **Test API Connection** to verify your settings

### 3. Enable Payment Method

1. Go to **WooCommerce > Settings > Payments**
2. Find "StablecoinPay" in the payment methods list
3. Click **Enable** to activate the payment method
4. Click **Manage** to configure payment method settings:
   - **Title**: Payment method name shown to customers
   - **Description**: Description shown during checkout
   - **Test Mode**: Enable/disable test mode

5. Click **Save changes**

### 4. Test the Integration

1. Create a test product in your WooCommerce store
2. Add it to cart and proceed to checkout
3. Select "StablecoinPay" as payment method
4. Complete the order to test the payment flow
5. Verify the payment page loads correctly
6. Check order status updates in WooCommerce admin

## Configuration Options

### General Settings

| Setting | Description | Default |
|---------|-------------|---------|
| Enable Gateway | Enable/disable the payment method | Disabled |
| Test Mode | Use testnet for testing | Enabled |
| Debug Mode | Enable detailed logging | Disabled |

### API Settings

| Setting | Description | Required |
|---------|-------------|----------|
| API URL | StablecoinPay API endpoint | Yes |
| API Key | Your API key | Yes |
| API Secret | Your API secret | Yes |

### Payment Settings

| Setting | Description | Default |
|---------|-------------|---------|
| Default Token | Default cryptocurrency | USDT |
| Default Blockchain | Default network | Ethereum |
| Payment Expiry | Payment timeout (minutes) | 30 |

## Troubleshooting

### Common Issues

#### 1. Payment Page Not Loading (404 Error)
**Solution**: Flush rewrite rules
1. Go to **Settings > Permalinks**
2. Click **Save Changes** (no changes needed)

#### 2. API Connection Failed
**Solutions**:
- Verify API credentials are correct
- Check API URL format (include protocol: http/https)
- Ensure your server can make outbound HTTP requests
- Check firewall settings

#### 3. Callback URL Not Working
**Solutions**:
- Verify callback URL is accessible from external servers
- Check IP whitelist settings
- Review server logs for errors
- Ensure SSL certificate is valid (for HTTPS)

#### 4. Database Tables Missing
**Solution**: Deactivate and reactivate the plugin
1. Go to **Plugins**
2. Deactivate "WooCommerce StablecoinPay Gateway"
3. Reactivate the plugin

### Debug Information

Enable debug mode to get detailed logs:
1. Go to **WooCommerce > StablecoinPay**
2. Enable **Debug Mode**
3. Save changes
4. View logs at **WooCommerce > Status > Logs**

### Support

For additional support:
- Check the plugin documentation
- Review WooCommerce system status
- Contact StablecoinPay support with your API credentials

## Security Considerations

1. **Use HTTPS**: Always use SSL certificates in production
2. **API Credentials**: Keep API credentials secure and never share them
3. **IP Whitelisting**: Configure callback IP restrictions if possible
4. **Regular Updates**: Keep the plugin updated to the latest version
5. **Backup**: Regular backup your WordPress site and database

## Performance Optimization

1. **Caching**: Exclude payment pages from caching
   - Add `/stablecoinpay-payment/*` to cache exclusions
   - Add `/wc-api/stablecoinpay/*` to cache exclusions

2. **Database**: The plugin creates custom tables for better performance
   - Regular cleanup of old payment data is performed automatically
   - Monitor database size if you have high transaction volume

3. **Logging**: Disable debug mode in production to reduce log file size

## Next Steps

After successful installation:

1. **Test thoroughly** with small amounts in test mode
2. **Configure your preferred tokens** and blockchains
3. **Customize payment page** styling if needed
4. **Set up monitoring** for payment notifications
5. **Train your team** on the new payment process

## Uninstallation

To completely remove the plugin:

1. Deactivate the plugin
2. Delete the plugin files
3. Optionally remove database tables:
   - `wp_stablecoinpay_payments`
   - `wp_stablecoinpay_transaction_logs`
4. Remove plugin options from `wp_options` table
