<?php
/**
 * PayOp Gateway Registration Debug <PERSON>ript
 * 
 * Run this script to debug gateway registration issues
 * Access via: /wp-content/plugins/payop-direct-payment-woo/debug-gateway-registration.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-load.php');
}

// Only allow admin access
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

echo '<h1>PayOp Gateway Registration Debug</h1>';

// Check if WooCommerce is active
echo '<h2>1. WooCommerce Status</h2>';
if (class_exists('WooCommerce')) {
    echo '✅ WooCommerce is active<br>';
    echo 'WooCommerce Version: ' . WC_VERSION . '<br>';
} else {
    echo '❌ WooCommerce is not active<br>';
}

// Check if our plugin is loaded
echo '<h2>2. PayOp Plugin Status</h2>';
if (defined('PAYOP_WC_VERSION')) {
    echo '✅ PayOp plugin constants defined<br>';
    echo 'Plugin Version: ' . PAYOP_WC_VERSION . '<br>';
    echo 'Plugin Directory: ' . PAYOP_WC_PLUGIN_DIR . '<br>';
} else {
    echo '❌ PayOp plugin constants not defined<br>';
}

// Check if gateway classes exist
echo '<h2>3. Gateway Classes</h2>';
if (class_exists('\PayOp\WooCommerce\Gateway\PayOp_Gateway')) {
    echo '✅ Namespaced PayOp_Gateway class exists<br>';
} else {
    echo '❌ Namespaced PayOp_Gateway class not found<br>';
}

if (class_exists('PayOp_Gateway')) {
    echo '✅ Global PayOp_Gateway alias exists<br>';
} else {
    echo '❌ Global PayOp_Gateway alias not found<br>';
}

if (class_exists('WC_PayOp_Gateway')) {
    echo '✅ WC_PayOp_Gateway alias exists<br>';
} else {
    echo '❌ WC_PayOp_Gateway alias not found<br>';
}

// Check registered payment gateways
echo '<h2>4. Registered Payment Gateways</h2>';
if (function_exists('WC')) {
    $gateways = WC()->payment_gateways()->payment_gateways();
    echo 'Total registered gateways: ' . count($gateways) . '<br><br>';
    
    $payop_found = false;
    foreach ($gateways as $id => $gateway) {
        if (strpos($id, 'payop') !== false || strpos(get_class($gateway), 'PayOp') !== false) {
            echo '✅ PayOp gateway found: ' . $id . ' (' . get_class($gateway) . ')<br>';
            $payop_found = true;
        }
    }
    
    if (!$payop_found) {
        echo '❌ PayOp gateway not found in registered gateways<br>';
        echo '<br><strong>All registered gateways:</strong><br>';
        foreach ($gateways as $id => $gateway) {
            echo '- ' . $id . ' (' . get_class($gateway) . ')<br>';
        }
    }
} else {
    echo '❌ WooCommerce not initialized<br>';
}

// Check if our registration function exists
echo '<h2>5. Registration Function</h2>';
if (function_exists('add_payop_gateway')) {
    echo '✅ add_payop_gateway function exists<br>';
} else {
    echo '❌ add_payop_gateway function not found<br>';
}

// Check filters
echo '<h2>6. Filter Hooks</h2>';
global $wp_filter;
if (isset($wp_filter['woocommerce_payment_gateways'])) {
    echo '✅ woocommerce_payment_gateways filter has callbacks<br>';
    $callbacks = $wp_filter['woocommerce_payment_gateways']->callbacks;
    foreach ($callbacks as $priority => $functions) {
        foreach ($functions as $function) {
            if (is_array($function['function'])) {
                $callback_name = get_class($function['function'][0]) . '::' . $function['function'][1];
            } else {
                $callback_name = $function['function'];
            }
            echo '- Priority ' . $priority . ': ' . $callback_name . '<br>';
        }
    }
} else {
    echo '❌ No callbacks registered for woocommerce_payment_gateways filter<br>';
}

// Test gateway instantiation
echo '<h2>7. Gateway Instantiation Test</h2>';
try {
    if (class_exists('WC_PayOp_Gateway')) {
        $gateway = new WC_PayOp_Gateway();
        echo '✅ WC_PayOp_Gateway can be instantiated<br>';
        echo 'Gateway ID: ' . $gateway->id . '<br>';
        echo 'Gateway Title: ' . $gateway->method_title . '<br>';
        echo 'Gateway Enabled: ' . ($gateway->enabled ? 'Yes' : 'No') . '<br>';
        echo 'Gateway Available: ' . ($gateway->is_available() ? 'Yes' : 'No') . '<br>';
    } else {
        echo '❌ WC_PayOp_Gateway class not available for instantiation<br>';
    }
} catch (Exception $e) {
    echo '❌ Error instantiating gateway: ' . $e->getMessage() . '<br>';
}

echo '<br><hr><br>';
echo '<strong>Debug completed at:</strong> ' . date('Y-m-d H:i:s') . '<br>';
echo '<a href="' . admin_url('admin.php?page=wc-settings&tab=checkout') . '">Go to WooCommerce Payment Settings</a>';
