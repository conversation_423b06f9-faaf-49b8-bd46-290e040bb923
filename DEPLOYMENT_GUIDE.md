# PayOp WooCommerce Plugin - Deployment Guide

## 🚀 Pre-Deployment Checklist

### ✅ **Environment Verification**
- [ ] WordPress 6.0+ installed
- [ ] WooCommerce 7.0+ active
- [ ] PHP 8.0+ (8.2+ recommended)
- [ ] SSL certificate installed and active
- [ ] Database backup completed
- [ ] Site backup completed

### ✅ **PayOp Account Setup**
- [ ] PayOp merchant account created
- [ ] API credentials obtained
- [ ] Test credentials verified
- [ ] Production credentials ready
- [ ] Webhook URLs configured

### ✅ **Testing Completed**
- [ ] All unit tests passing
- [ ] Integration tests completed
- [ ] Security tests passed
- [ ] Performance tests acceptable
- [ ] User acceptance testing completed

## 📦 Deployment Methods

### Method 1: WordPress Admin Upload

1. **Prepare Plugin Package**
   ```bash
   # Create deployment package
   zip -r payop-woocommerce-gateway.zip . \
     -x "tests/*" "*.git*" "node_modules/*" "*.md" "composer.*"
   ```

2. **Upload via Admin**
   - Go to **Plugins → Add New → Upload Plugin**
   - Select the zip file
   - Click **Install Now**
   - Activate the plugin

### Method 2: FTP/SFTP Upload

1. **Upload Files**
   ```bash
   # Upload to plugins directory
   scp -r payop-direct-payment-woo/ user@server:/path/to/wp-content/plugins/
   ```

2. **Set Permissions**
   ```bash
   # Set correct permissions
   chmod -R 755 /path/to/wp-content/plugins/payop-direct-payment-woo/
   chown -R www-data:www-data /path/to/wp-content/plugins/payop-direct-payment-woo/
   ```

3. **Activate Plugin**
   - Go to **Plugins** in WordPress admin
   - Find "PayOp Payment Gateway"
   - Click **Activate**

### Method 3: WP-CLI Deployment

```bash
# Install plugin via WP-CLI
wp plugin install payop-woocommerce-gateway.zip --activate

# Or if uploading manually
wp plugin activate payop-direct-payment-woo
```

## ⚙️ Configuration Steps

### 1. Initial Plugin Setup

```bash
# Verify plugin activation
wp plugin list --status=active | grep payop

# Check for any errors
wp plugin status payop-direct-payment-woo
```

### 2. Database Setup Verification

The plugin automatically creates required tables. Verify:

```sql
-- Check if tables exist
SHOW TABLES LIKE 'wp_payop_%';

-- Verify table structure
DESCRIBE wp_payop_transactions;
DESCRIBE wp_payop_payment_methods;
```

### 3. Configure PayOp Settings

1. **Navigate to Settings**
   - Go to **WooCommerce → Settings → Payments**
   - Click **PayOp Payment Gateway**

2. **Enter API Credentials**
   ```
   Public Key: application-606
   Secret Key: [your-secret-key]
   JWT Token: [your-jwt-token]
   Project ID: 606
   ```

3. **Configure Options**
   - Enable the gateway
   - Set title and description
   - Configure debug logging (disable in production)

### 4. Test API Connection

1. Click **Test Connection** button
2. Verify payment methods load
3. Check debug logs for any issues

## 🔒 Security Configuration

### 1. SSL/TLS Setup

Ensure SSL is properly configured:

```bash
# Test SSL certificate
curl -I https://yoursite.com

# Verify SSL grade
curl -s "https://api.ssllabs.com/api/v3/analyze?host=yoursite.com"
```

### 2. Webhook Security

Configure webhook endpoint:

```
Webhook URL: https://yoursite.com/wc-api/payop_ipn
Allowed IPs: *************, *************, ************, *************
```

### 3. File Permissions

Set secure file permissions:

```bash
# Plugin files
find /path/to/plugin/ -type f -exec chmod 644 {} \;
find /path/to/plugin/ -type d -exec chmod 755 {} \;

# Sensitive files
chmod 600 /path/to/plugin/includes/utils/class-payop-config.php
```

## 🌍 Environment-Specific Configuration

### Development Environment

```php
// wp-config.php additions for development
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('PAYOP_WC_DEBUG', true);
```

### Staging Environment

```php
// wp-config.php for staging
define('WP_DEBUG', false);
define('WP_DEBUG_LOG', true);
define('PAYOP_WC_ENVIRONMENT', 'staging');
```

### Production Environment

```php
// wp-config.php for production
define('WP_DEBUG', false);
define('WP_DEBUG_LOG', false);
define('PAYOP_WC_ENVIRONMENT', 'production');
```

## 📊 Monitoring Setup

### 1. Error Monitoring

Configure error monitoring:

```php
// Add to wp-config.php
define('PAYOP_WC_ERROR_REPORTING', true);
define('PAYOP_WC_ADMIN_EMAIL', '<EMAIL>');
```

### 2. Performance Monitoring

Set up performance monitoring:

```php
// Monitor API response times
add_action('payop_api_request_complete', function($endpoint, $duration) {
    if ($duration > 5000) { // 5 seconds
        error_log("PayOp API slow response: {$endpoint} took {$duration}ms");
    }
});
```

### 3. Log Rotation

Configure log rotation:

```bash
# Add to logrotate.d
/path/to/wp-content/debug.log {
    daily
    missingok
    rotate 30
    compress
    notifempty
    create 644 www-data www-data
}
```

## 🔄 Update Procedures

### 1. Pre-Update Checklist

- [ ] Create full site backup
- [ ] Test update in staging environment
- [ ] Review changelog for breaking changes
- [ ] Schedule maintenance window
- [ ] Notify stakeholders

### 2. Update Process

```bash
# 1. Backup current version
cp -r /path/to/plugin/ /path/to/backup/plugin-$(date +%Y%m%d)/

# 2. Download new version
wget https://releases.example.com/payop-woocommerce-gateway-v1.1.0.zip

# 3. Extract and replace
unzip payop-woocommerce-gateway-v1.1.0.zip
rsync -av payop-direct-payment-woo/ /path/to/wp-content/plugins/payop-direct-payment-woo/

# 4. Run any database migrations
wp payop migrate

# 5. Clear caches
wp cache flush
```

### 3. Post-Update Verification

- [ ] Plugin activates without errors
- [ ] Settings are preserved
- [ ] API connection works
- [ ] Payment methods load
- [ ] Test transaction completes
- [ ] Logs show no errors

## 🚨 Rollback Procedures

### Emergency Rollback

```bash
# 1. Deactivate plugin
wp plugin deactivate payop-direct-payment-woo

# 2. Restore backup
rm -rf /path/to/wp-content/plugins/payop-direct-payment-woo/
cp -r /path/to/backup/plugin-YYYYMMDD/ /path/to/wp-content/plugins/payop-direct-payment-woo/

# 3. Restore database if needed
mysql -u user -p database < backup-YYYYMMDD.sql

# 4. Reactivate plugin
wp plugin activate payop-direct-payment-woo
```

## 📈 Performance Optimization

### 1. Caching Configuration

```php
// Optimize payment method caching
add_filter('payop_cache_settings', function($settings) {
    return [
        'enabled' => true,
        'ttl' => 3600, // 1 hour
        'max_size' => 1000
    ];
});
```

### 2. Database Optimization

```sql
-- Add indexes for better performance
ALTER TABLE wp_payop_transactions ADD INDEX idx_order_id (order_id);
ALTER TABLE wp_payop_transactions ADD INDEX idx_invoice_id (invoice_id);
ALTER TABLE wp_payop_transactions ADD INDEX idx_status (status);
```

### 3. CDN Configuration

Configure CDN for static assets:

```php
// Use CDN for plugin assets
add_filter('payop_asset_url', function($url) {
    return str_replace(site_url(), 'https://cdn.yoursite.com', $url);
});
```

## 🔍 Health Checks

### Automated Health Checks

```bash
#!/bin/bash
# health-check.sh

# Check plugin status
wp plugin status payop-direct-payment-woo

# Test API connection
wp payop test-connection

# Check recent errors
wp payop check-errors --since="1 hour ago"

# Verify webhook endpoint
curl -f https://yoursite.com/wc-api/payop_ipn
```

### Monitoring Alerts

Set up monitoring alerts for:

- Payment success rate < 95%
- API response time > 5 seconds
- Error rate > 1%
- Webhook failures
- Database connection issues

## 📞 Support and Maintenance

### Support Contacts

- **Technical Issues**: <EMAIL>
- **PayOp API Issues**: <EMAIL>
- **Emergency Contact**: +1-XXX-XXX-XXXX

### Maintenance Schedule

- **Daily**: Monitor error logs and performance
- **Weekly**: Review transaction reports
- **Monthly**: Update plugin if new version available
- **Quarterly**: Full security audit

### Documentation Updates

Keep documentation current:

- Update configuration guides
- Maintain troubleshooting docs
- Document any customizations
- Update deployment procedures

---

**Deployment Complete!** 🎉

Your PayOp WooCommerce plugin is now ready for production use. Monitor the system closely for the first 24-48 hours after deployment.
